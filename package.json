{"name": "insolence-rgs", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "test": "turbo run test", "mock-operator": "cd apps/mockOperatorApi && pnpm dev", "test:session-flow": "node test-casino-session-flow.js", "casino:demo": "echo 'Starting casino demo environment...' && pnpm run mock-operator & pnpm run dev"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "dependencies": {"@types/ioredis": "^5.0.0", "axios": "^1.9.0", "body-parser": "^2.2.0", "bull": "^4.16.5", "chalk": "^5.4.1", "cors": "^2.8.5", "dotenv-flow": "^4.1.0", "express": "^4.21.2", "express-jwt": "^8.5.1", "express-rate-limit": "^7.5.0", "glob": "^10.4.5", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jose": "^6.0.10", "json2csv": "6.0.0-alpha.2", "jsonwebtoken": "^8.5.1", "jwt-decode": "^4.0.0", "knex": "^3.1.0", "p-limit": "^6.2.0", "pg": "^8.14.1", "rate-limit-redis": "^4.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "winston-loki": "^6.1.3", "zod": "^3.24.3"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.19", "@types/pg": "^8.15.2", "@vitejs/plugin-react": "^4.5.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "turbo": "^1.10.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "pnpm": {"overrides": {"express": "4.21.2", "pg": "8.16.0", "zod": "3.25.7"}, "onlyBuiltDependencies": ["javascript-obfuscator"]}}