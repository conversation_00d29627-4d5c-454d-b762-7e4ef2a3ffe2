-- Database migration for operator callback functionality
-- Creates tables for transaction tracking, audit logging, and callback failures
-- Run date: December 3, 2024

-- =====================================================
-- OPERATOR TRANSACTIONS TABLE
-- =====================================================
-- Main table for tracking all operator callback transactions
CREATE TABLE IF NOT EXISTS data.operator_transactions (
    transaction_id VARCHAR(255) PRIMARY KEY,
    round_id UUID,
    session_id UUID NOT NULL,
    user_id UUID NOT NULL,
    operator_id VARCHAR(36) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('bet', 'win', 'promo_win')),
    amount NUMERIC(12,4) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    game_code VARCHAR(12),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    operator_response JSONB,
    operator_transaction_id VARCHAR(255),
    new_balance NUMERIC(12,4),
    related_transaction_id VARCHAR(255), -- For win transactions, references the bet transaction
    promotion_type VARCHAR(20), -- For promo_win transactions
    tournament_id VARCHAR(255), -- For tournament-related promo wins
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_operator_transactions_session 
        FOREIGN KEY (session_id) REFERENCES data.game_sessions(session_id),
    CONSTRAINT fk_operator_transactions_round 
        FOREIGN KEY (round_id) REFERENCES data.game_rounds(round_id)
);

-- Indexes for performance and monitoring
CREATE INDEX IF NOT EXISTS idx_operator_transactions_session_type 
ON data.operator_transactions(session_id, transaction_type, created_at);

CREATE INDEX IF NOT EXISTS idx_operator_transactions_operator_status 
ON data.operator_transactions(operator_id, status, created_at);

CREATE INDEX IF NOT EXISTS idx_operator_transactions_status_retry 
ON data.operator_transactions(status, retry_count, created_at) 
WHERE status = 'pending';

CREATE INDEX IF NOT EXISTS idx_operator_transactions_transaction_id 
ON data.operator_transactions(transaction_id);

CREATE INDEX IF NOT EXISTS idx_operator_transactions_related 
ON data.operator_transactions(related_transaction_id) 
WHERE related_transaction_id IS NOT NULL;

-- =====================================================
-- ENHANCED AUDIT TABLES
-- =====================================================

-- Financial events audit table (enhanced)
CREATE TABLE IF NOT EXISTS audit.financial_events (
    event_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL, -- 'bet_placed', 'win_paid', 'promo_win_paid'
    round_id UUID,
    session_id UUID NOT NULL,
    user_id UUID NOT NULL,
    operator_id VARCHAR(36) NOT NULL,
    amount NUMERIC(12,4) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    game_code VARCHAR(12),
    transaction_id VARCHAR(255),
    related_transaction_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Indexes for performance and compliance queries
    INDEX idx_financial_events_user_created (user_id, created_at),
    INDEX idx_financial_events_operator_type_created (operator_id, event_type, created_at),
    INDEX idx_financial_events_transaction_id (transaction_id),
    INDEX idx_financial_events_session_created (session_id, created_at)
);

-- Security events audit table
CREATE TABLE IF NOT EXISTS audit.security_events (
    event_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    operator_id VARCHAR(36),
    session_id UUID,
    user_id UUID,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    risk_score INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Indexes for security monitoring
    INDEX idx_security_events_operator_type_created (operator_id, event_type, created_at),
    INDEX idx_security_events_ip_created (ip_address, created_at),
    INDEX idx_security_events_failed_attempts (success, created_at) WHERE success = false,
    INDEX idx_security_events_risk_score (risk_score, created_at) WHERE risk_score > 50
);

-- Callback failures tracking table
CREATE TABLE IF NOT EXISTS audit.callback_failures (
    failure_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    operator_id VARCHAR(36) NOT NULL,
    transaction_id VARCHAR(255),
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT,
    request_data JSONB,
    retry_count INTEGER DEFAULT 0,
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    resolved_at TIMESTAMP,
    
    -- Indexes for monitoring and alerting
    INDEX idx_callback_failures_operator_created (operator_id, created_at),
    INDEX idx_callback_failures_unresolved (resolved, created_at) WHERE resolved = false,
    INDEX idx_callback_failures_error_type (error_type, created_at)
);

-- Player activity log for compliance (GDPR-ready)
CREATE TABLE IF NOT EXISTS audit.player_activity_log (
    activity_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    session_id UUID,
    operator_id VARCHAR(36) NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    game_code VARCHAR(12),
    details JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    retention_until TIMESTAMP NOT NULL, -- For GDPR compliance
    
    -- Indexes for compliance queries
    INDEX idx_player_activity_user_created (user_id, created_at),
    INDEX idx_player_activity_retention (retention_until),
    INDEX idx_player_activity_operator_type (operator_id, activity_type, created_at)
);

-- Provably fair evidence storage
CREATE TABLE IF NOT EXISTS audit.provably_fair_evidence (
    evidence_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    round_id UUID NOT NULL,
    server_seed_hash VARCHAR(64) NOT NULL, -- Store hash, not actual seed
    client_seed VARCHAR(64),
    nonce INTEGER NOT NULL,
    result_hash VARCHAR(64) NOT NULL,
    game_code VARCHAR(12) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    retention_until TIMESTAMP NOT NULL, -- 7 years for regulatory compliance
    
    -- Indexes for provably fair verification
    INDEX idx_provably_fair_round (round_id),
    INDEX idx_provably_fair_game_created (game_code, created_at),
    INDEX idx_provably_fair_retention (retention_until)
);

-- =====================================================
-- PERFORMANCE OPTIMIZATION VIEWS
-- =====================================================

-- View for operator transaction metrics
CREATE OR REPLACE VIEW audit.operator_transaction_metrics AS
SELECT 
    operator_id,
    transaction_type,
    DATE_TRUNC('hour', created_at) as hour_bucket,
    COUNT(*) as total_transactions,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_transactions,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_amount,
    AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (completed_at - created_at)) * 1000 
        ELSE NULL END) as avg_processing_time_ms
FROM data.operator_transactions
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY operator_id, transaction_type, DATE_TRUNC('hour', created_at);

-- View for real-time callback monitoring
CREATE OR REPLACE VIEW audit.callback_monitoring AS
SELECT 
    o.operator_id,
    COUNT(CASE WHEN o.status = 'pending' AND o.created_at > NOW() - INTERVAL '5 minutes' THEN 1 END) as pending_recent,
    COUNT(CASE WHEN o.status = 'failed' AND o.created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as failed_last_hour,
    COUNT(CASE WHEN cf.resolved = false AND cf.created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as unresolved_failures,
    MAX(o.created_at) as last_transaction_time,
    AVG(CASE WHEN o.status = 'completed' AND o.completed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (o.completed_at - o.created_at)) * 1000 
        ELSE NULL END) as avg_response_time_ms
FROM data.operator_transactions o
LEFT JOIN audit.callback_failures cf ON o.operator_id = cf.operator_id
WHERE o.created_at > NOW() - INTERVAL '24 hours'
GROUP BY o.operator_id;

-- =====================================================
-- DATA RETENTION POLICIES
-- =====================================================

-- Function to clean up expired audit data
CREATE OR REPLACE FUNCTION audit.cleanup_expired_data()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Remove expired player activity logs (GDPR compliance)
    DELETE FROM audit.player_activity_log 
    WHERE retention_until < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Archive old financial events (keep for 7 years, then move to archive)
    INSERT INTO audit.financial_events_archive 
    SELECT * FROM audit.financial_events 
    WHERE created_at < NOW() - INTERVAL '1 year';
    
    -- Remove resolved callback failures older than 30 days
    DELETE FROM audit.callback_failures 
    WHERE resolved = true AND resolved_at < NOW() - INTERVAL '30 days';
    
    -- Clean up old security events (keep for 2 years)
    DELETE FROM audit.security_events 
    WHERE created_at < NOW() - INTERVAL '2 years';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

-- Grant appropriate permissions to application user
GRANT SELECT, INSERT, UPDATE ON data.operator_transactions TO rgs_app_user;
GRANT SELECT, INSERT ON audit.financial_events TO rgs_app_user;
GRANT SELECT, INSERT ON audit.security_events TO rgs_app_user;
GRANT SELECT, INSERT, UPDATE ON audit.callback_failures TO rgs_app_user;
GRANT SELECT, INSERT ON audit.player_activity_log TO rgs_app_user;
GRANT SELECT, INSERT ON audit.provably_fair_evidence TO rgs_app_user;

-- Grant read access to monitoring views
GRANT SELECT ON audit.operator_transaction_metrics TO rgs_app_user;
GRANT SELECT ON audit.callback_monitoring TO rgs_app_user;

-- =====================================================
-- INITIAL DATA AND CONFIGURATION
-- =====================================================

-- Insert initial configuration for callback monitoring
INSERT INTO config.system_settings (setting_key, setting_value, description) VALUES
('callback_retry_max_attempts', '3', 'Maximum retry attempts for failed operator callbacks'),
('callback_timeout_seconds', '5', 'Timeout for operator callback requests in seconds'),
('callback_circuit_breaker_threshold', '5', 'Number of failures before opening circuit breaker'),
('callback_circuit_breaker_timeout', '60', 'Circuit breaker timeout in seconds')
ON CONFLICT (setting_key) DO NOTHING;

-- Create notification triggers for critical failures
CREATE OR REPLACE FUNCTION audit.notify_critical_callback_failure()
RETURNS TRIGGER AS $$
BEGIN
    -- Notify application of critical callback failures
    IF NEW.retry_count >= 3 AND NOT NEW.resolved THEN
        PERFORM pg_notify('critical_callback_failure', 
            json_build_object(
                'operator_id', NEW.operator_id,
                'transaction_id', NEW.transaction_id,
                'error_type', NEW.error_type,
                'retry_count', NEW.retry_count
            )::text
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_critical_callback_failure
    AFTER INSERT OR UPDATE ON audit.callback_failures
    FOR EACH ROW
    EXECUTE FUNCTION audit.notify_critical_callback_failure();

-- Migration completed successfully
-- Tables created: operator_transactions, financial_events, security_events, 
--                callback_failures, player_activity_log, provably_fair_evidence
-- Views created: operator_transaction_metrics, callback_monitoring
-- Functions created: cleanup_expired_data, notify_critical_callback_failure
