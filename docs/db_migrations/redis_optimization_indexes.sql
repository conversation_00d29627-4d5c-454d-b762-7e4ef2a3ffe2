-- Redis Cache Optimization Indexes
-- These indexes are specifically designed to optimize Redis cache loading operations
-- Run these after the main schema to improve cache performance

-- ================================
-- Session Management Indexes
-- ================================

-- Fast session authentication lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_sessions_session_status_started 
ON data.game_sessions(session_id, status, started_at) 
WHERE status = 'open';

-- Operator session tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_sessions_operator_status_game 
ON data.game_sessions(operator_id, status, game_code, started_at) 
WHERE status = 'open';

-- User session management
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_sessions_user_status_started 
ON data.game_sessions(user_id, status, started_at) 
WHERE status = 'open';

-- Session metrics calculation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_sessions_metrics_lookup 
ON data.game_sessions(operator_id, play_mode, status, started_at);

-- ================================
-- Operator Configuration Indexes
-- ================================

-- Fast operator credential lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_operator_credentials_operator_env 
ON config.operator_credentials(operator_id, environment);

-- Operator endpoint lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_operator_endpoints_operator_env_type 
ON config.operator_custom_endpoints(operator_id, environment, endpoint_type) 
WHERE is_enabled = true;

-- Game permissions for operators
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_operator_game_settings_operator_enabled 
ON config.operator_game_settings(operator_id, game_code) 
WHERE is_enabled = true;

-- ================================
-- Game Configuration Indexes
-- ================================

-- Active games lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_games_status_globally_enabled 
ON config.games(status, globally_enabled, game_code) 
WHERE status = 'active';

-- Bet limits by game and currency
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_bet_limits_game_currency_enabled 
ON config.bet_limits(game_code, currency, operator_id) 
WHERE is_enabled = true;

-- RTP settings lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_rtp_settings_operator_game_status 
ON config.rtp_settings(operator_id, game_code, status) 
WHERE status = 'active';

-- ================================
-- Round and Event Tracking Indexes
-- ================================

-- Round events for session tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_round_events_session_action_created 
ON data.round_events(session_id, action_type, created_at);

-- Session events for audit and compliance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_session_events_operator_category_status 
ON data.session_events(operator_id, event_category, status, created_at);

-- Failed authentication tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_session_events_failed_auth 
ON data.session_events(operator_id, event_type, created_at) 
WHERE event_type = 'authenticate_failed';

-- ================================
-- Currency and Financial Indexes
-- ================================

-- Operator currency configurations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_operator_currency_config_operator_enabled 
ON config.operator_currency_config(operator_id, currency_code) 
WHERE is_enabled = true;

-- User transactions for balance tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_transactions_session_type_created 
ON data.user_transactions(session_id, type, created_at);

-- ================================
-- Cache Invalidation Support
-- ================================

-- Add cache version tracking columns
ALTER TABLE config.games 
ADD COLUMN IF NOT EXISTS cache_version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS cache_invalidated_at TIMESTAMP;

ALTER TABLE data.operators 
ADD COLUMN IF NOT EXISTS cache_version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS cache_invalidated_at TIMESTAMP;

ALTER TABLE config.operator_game_settings 
ADD COLUMN IF NOT EXISTS cache_version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS cache_invalidated_at TIMESTAMP;

-- Cache invalidation triggers
CREATE OR REPLACE FUNCTION invalidate_cache_version()
RETURNS TRIGGER AS $$
BEGIN
    NEW.cache_version = COALESCE(OLD.cache_version, 0) + 1;
    NEW.cache_invalidated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply cache invalidation triggers
DROP TRIGGER IF EXISTS trg_games_cache_invalidation ON config.games;
CREATE TRIGGER trg_games_cache_invalidation
    BEFORE UPDATE ON config.games
    FOR EACH ROW
    EXECUTE FUNCTION invalidate_cache_version();

DROP TRIGGER IF EXISTS trg_operators_cache_invalidation ON data.operators;
CREATE TRIGGER trg_operators_cache_invalidation
    BEFORE UPDATE ON data.operators
    FOR EACH ROW
    EXECUTE FUNCTION invalidate_cache_version();

DROP TRIGGER IF EXISTS trg_operator_game_settings_cache_invalidation ON config.operator_game_settings;
CREATE TRIGGER trg_operator_game_settings_cache_invalidation
    BEFORE UPDATE ON config.operator_game_settings
    FOR EACH ROW
    EXECUTE FUNCTION invalidate_cache_version();

-- ================================
-- Redis Cache Configuration Table
-- ================================

CREATE TABLE IF NOT EXISTS config.redis_cache_config (
    cache_key VARCHAR(255) PRIMARY KEY,
    ttl_seconds INTEGER NOT NULL DEFAULT 3600,
    auto_refresh BOOLEAN DEFAULT true,
    refresh_interval_seconds INTEGER DEFAULT 600,
    priority INTEGER DEFAULT 1,
    description TEXT,
    last_updated TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Insert default cache configurations
INSERT INTO config.redis_cache_config (cache_key, ttl_seconds, auto_refresh, refresh_interval_seconds, priority, description) VALUES
('sessionById', 86400, true, 300, 1, 'Active game sessions'),
('operatorGamePermissions', 3600, true, 600, 2, 'Operator game permissions'),
('gameAvailability', 1800, true, 300, 1, 'Game availability matrix'),
('gameBetLimits', 7200, true, 1800, 2, 'Game bet limits configuration'),
('sessionMetrics', 3600, true, 600, 3, 'Session metrics for operators'),
('operatorDetails', 7200, true, 1800, 2, 'Operator configuration details')
ON CONFLICT (cache_key) DO NOTHING;

-- ================================
-- Performance Monitoring Views
-- ================================

-- Session performance view
CREATE OR REPLACE VIEW data.v_session_performance AS
SELECT 
    gs.operator_id,
    gs.game_code,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN gs.status = 'open' THEN 1 END) as active_sessions,
    AVG(EXTRACT(EPOCH FROM (COALESCE(gs.ended_at, NOW()) - gs.started_at))) as avg_duration_seconds,
    COUNT(CASE WHEN gs.play_mode = 'real' THEN 1 END) as real_sessions,
    COUNT(CASE WHEN gs.play_mode = 'demo' THEN 1 END) as demo_sessions,
    MAX(gs.started_at) as last_session_start
FROM data.game_sessions gs
WHERE gs.started_at > NOW() - INTERVAL '24 hours'
GROUP BY gs.operator_id, gs.game_code
ORDER BY total_sessions DESC;

-- Cache hit rate monitoring view
CREATE OR REPLACE VIEW data.v_cache_performance AS
SELECT 
    se.operator_id,
    se.event_type,
    COUNT(*) as total_events,
    COUNT(CASE WHEN se.duration_ms < 100 THEN 1 END) as fast_responses,
    COUNT(CASE WHEN se.duration_ms >= 100 THEN 1 END) as slow_responses,
    AVG(se.duration_ms) as avg_duration_ms,
    MAX(se.duration_ms) as max_duration_ms
FROM data.session_events se
WHERE se.created_at > NOW() - INTERVAL '1 hour'
    AND se.event_category = 'authentication'
GROUP BY se.operator_id, se.event_type
ORDER BY avg_duration_ms DESC;

-- ================================
-- Cleanup and Maintenance
-- ================================

-- Function to cleanup old cache entries
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Archive old closed sessions
    INSERT INTO audit.sessions_archive 
    SELECT 
        gen_random_uuid() as archived_session_id,
        session_id,
        operator_id,
        user_id,
        game_code,
        play_mode,
        currency,
        started_at,
        ended_at,
        NOW() as archived_at
    FROM data.game_sessions 
    WHERE status = 'closed' 
        AND ended_at < NOW() - INTERVAL '7 days';
    
    -- Delete old closed sessions
    DELETE FROM data.game_sessions 
    WHERE status = 'closed' 
        AND ended_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-old-sessions', '0 2 * * *', 'SELECT cleanup_old_sessions();');

COMMENT ON TABLE config.redis_cache_config IS 'Configuration for Redis cache TTL and refresh settings';
COMMENT ON FUNCTION cleanup_old_sessions() IS 'Archives and removes old closed sessions to maintain performance';
COMMENT ON VIEW data.v_session_performance IS 'Real-time session performance metrics for monitoring';
COMMENT ON VIEW data.v_cache_performance IS 'Cache performance metrics for optimization';
