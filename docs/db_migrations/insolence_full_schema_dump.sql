--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5 (Homebrew)

-- Started on 2025-06-03 21:16:23 +04

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 7 (class 2615 OID 17034)
-- Name: audit; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA audit;


--
-- TOC entry 5 (class 2615 OID 17032)
-- Name: config; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA config;


--
-- TOC entry 6 (class 2615 OID 17033)
-- Name: data; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA data;


--
-- TOC entry 957 (class 1247 OID 18914)
-- Name: betscale_type; Type: TYPE; Schema: config; Owner: -
--

CREATE TYPE config.betscale_type AS ENUM (
    'dynamic',
    'fixed',
    'exponential',
    'custom'
);


--
-- TOC entry 891 (class 1247 OID 18868)
-- Name: endpoint_type; Type: TYPE; Schema: config; Owner: -
--

CREATE TYPE config.endpoint_type AS ENUM (
    'auth',
    'bet',
    'win',
    'rollback',
    'promoWin'
);


--
-- TOC entry 888 (class 1247 OID 18861)
-- Name: environment_type; Type: TYPE; Schema: config; Owner: -
--

CREATE TYPE config.environment_type AS ENUM (
    'production',
    'staging',
    'development'
);


--
-- TOC entry 996 (class 1247 OID 17827)
-- Name: bet_limit_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.bet_limit_status AS ENUM (
    'active',
    'inactive',
    'disabled'
);


--
-- TOC entry 978 (class 1247 OID 17632)
-- Name: callback_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.callback_status_type AS ENUM (
    'success',
    'failure',
    'pending'
);


--
-- TOC entry 972 (class 1247 OID 17463)
-- Name: game_mode_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.game_mode_type AS ENUM (
    'desktop',
    'mobile'
);


--
-- TOC entry 999 (class 1247 OID 17846)
-- Name: game_settings_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.game_settings_status AS ENUM (
    'active',
    'inactive',
    'disabled'
);


--
-- TOC entry 969 (class 1247 OID 17400)
-- Name: game_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.game_status_type AS ENUM (
    'open',
    'closed',
    'suspended',
    'disabled',
    'active',
    'inactive'
);


--
-- TOC entry 981 (class 1247 OID 17663)
-- Name: operator_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.operator_status_type AS ENUM (
    'active',
    'inactive',
    'suspended'
);


--
-- TOC entry 894 (class 1247 OID 18941)
-- Name: play_mode; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.play_mode AS ENUM (
    'real',
    'demo',
    'sim'
);


--
-- TOC entry 966 (class 1247 OID 17388)
-- Name: play_mode_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.play_mode_type AS ENUM (
    'real',
    'demo'
);


--
-- TOC entry 975 (class 1247 OID 17607)
-- Name: round_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.round_status_type AS ENUM (
    'pending',
    'completed',
    'canceled',
    'open',
    'expired',
    'cancelled'
);


--
-- TOC entry 879 (class 1247 OID 18197)
-- Name: rtp_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.rtp_status_type AS ENUM (
    'active',
    'inactive',
    'disabled'
);


--
-- TOC entry 987 (class 1247 OID 17716)
-- Name: txn_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.txn_status_type AS ENUM (
    'completed',
    'pending',
    'failed'
);


--
-- TOC entry 984 (class 1247 OID 17708)
-- Name: txn_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.txn_type AS ENUM (
    'deposit',
    'withdrawal',
    'refund'
);


--
-- TOC entry 1002 (class 1247 OID 17875)
-- Name: ui_themes_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.ui_themes_status AS ENUM (
    'active',
    'inactive'
);


--
-- TOC entry 990 (class 1247 OID 17745)
-- Name: user_status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.user_status_type AS ENUM (
    'active',
    'inactive',
    'suspended'
);


--
-- TOC entry 261 (class 1255 OID 18971)
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 238 (class 1259 OID 17266)
-- Name: audit_logs; Type: TABLE; Schema: audit; Owner: -
--

CREATE TABLE audit.audit_logs (
    log_id uuid DEFAULT gen_random_uuid() NOT NULL,
    scope character varying(255),
    event character varying(255),
    details jsonb,
    severity character varying(50),
    created_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 240 (class 1259 OID 17289)
-- Name: fraud_flags; Type: TABLE; Schema: audit; Owner: -
--

CREATE TABLE audit.fraud_flags (
    flag_id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    session_id uuid,
    reason character varying(255),
    metadata jsonb,
    flagged_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 239 (class 1259 OID 17275)
-- Name: round_events; Type: TABLE; Schema: audit; Owner: -
--

CREATE TABLE audit.round_events (
    event_id uuid DEFAULT gen_random_uuid() NOT NULL,
    round_id uuid,
    event_type character varying(50),
    data jsonb,
    created_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 241 (class 1259 OID 17308)
-- Name: sessions_archive; Type: TABLE; Schema: audit; Owner: -
--

CREATE TABLE audit.sessions_archive (
    archived_session_id uuid NOT NULL,
    data jsonb,
    archived_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 222 (class 1259 OID 17055)
-- Name: bet_limits; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.bet_limits (
    id integer NOT NULL,
    operator_id character varying(36),
    game_code character varying(12) NOT NULL,
    currency character varying(5) DEFAULT 'USD'::text,
    min_bet numeric(10,2) NOT NULL,
    max_bet numeric(10,2) NOT NULL,
    bet_levels numeric(10,2)[],
    rtp_profile character varying(50),
    status public.bet_limit_status DEFAULT 'active'::public.bet_limit_status,
    created_by character varying(36),
    updated_by character varying(36),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 221 (class 1259 OID 17054)
-- Name: bet_limits_id_seq; Type: SEQUENCE; Schema: config; Owner: -
--

CREATE SEQUENCE config.bet_limits_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 4784 (class 0 OID 0)
-- Dependencies: 221
-- Name: bet_limits_id_seq; Type: SEQUENCE OWNED BY; Schema: config; Owner: -
--

ALTER SEQUENCE config.bet_limits_id_seq OWNED BY config.bet_limits.id;


--
-- TOC entry 228 (class 1259 OID 17117)
-- Name: currencies; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.currencies (
    currency_code character varying(5) NOT NULL,
    symbol character varying(5) NOT NULL,
    pretty_multiplier numeric(10,4) DEFAULT 1.0 NOT NULL,
    is_enabled_by_default boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 227 (class 1259 OID 17101)
-- Name: operator_game_settings; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.operator_game_settings (
    id integer NOT NULL,
    operator_id character varying(36),
    game_code character varying(12) NOT NULL,
    settings jsonb NOT NULL,
    engine_url character varying(255),
    preloader_url character varying(255),
    rtp_profile character varying(50),
    version character varying(50),
    status public.game_settings_status,
    created_by character varying(36),
    updated_by character varying(36),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    display_name character varying,
    engine character varying,
    animation_url character varying,
    preloader_version character varying,
    animation_version character varying,
    theme character varying,
    description text,
    game_type character varying,
    has_bonus_buy boolean,
    volatility character varying,
    min_bet numeric,
    max_bet numeric,
    tags text[],
    certification text,
    jurisdictions text[],
    supported_countries text[],
    allowed_bet_values numeric[],
    betscale_type character varying DEFAULT 'fixed'::character varying,
    bet_step numeric
);


--
-- TOC entry 226 (class 1259 OID 17100)
-- Name: game_settings_id_seq; Type: SEQUENCE; Schema: config; Owner: -
--

CREATE SEQUENCE config.game_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 4785 (class 0 OID 0)
-- Dependencies: 226
-- Name: game_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: config; Owner: -
--

ALTER SEQUENCE config.game_settings_id_seq OWNED BY config.operator_game_settings.id;


--
-- TOC entry 220 (class 1259 OID 17044)
-- Name: games; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.games (
    game_code character varying(12) NOT NULL,
    display_name character varying(100) NOT NULL,
    engine character varying(50) NOT NULL,
    default_rtp numeric(5,2),
    default_theme character varying(50),
    status public.game_status_type DEFAULT 'active'::public.game_status_type,
    engine_url character varying(255),
    preloader_url character varying(255),
    animation_url character varying(255),
    engine_version character varying(50),
    preloader_version character varying(50),
    animation_version character varying(50),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    globally_enabled boolean DEFAULT true,
    description text,
    game_type character varying,
    has_bonus_buy boolean,
    volatility character varying,
    min_bet numeric,
    max_bet numeric,
    tags text[],
    certification text,
    jurisdictions text[],
    supported_countries text[],
    allowed_bet_values numeric[],
    betscale_type character varying DEFAULT 'fixed'::character varying,
    bet_step numeric
);


--
-- TOC entry 242 (class 1259 OID 17808)
-- Name: languages; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.languages (
    language_code character varying(5) NOT NULL,
    language_name character varying(50) NOT NULL,
    is_enabled_by_default boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 246 (class 1259 OID 18834)
-- Name: operator_credentials; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.operator_credentials (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    operator_id character varying(36) NOT NULL,
    environment config.environment_type NOT NULL,
    api_key character varying(64) NOT NULL,
    public_key text NOT NULL,
    ip_whitelist text[] DEFAULT ARRAY[]::text[],
    algorithm character varying(10) DEFAULT 'RS256'::character varying NOT NULL,
    rgs_key_id integer DEFAULT 1 NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT operator_credentials_environment_check CHECK ((environment = ANY (ARRAY['production'::config.environment_type, 'staging'::config.environment_type, 'development'::config.environment_type])))
);


--
-- TOC entry 229 (class 1259 OID 17128)
-- Name: operator_currency_config; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.operator_currency_config (
    operator_id character varying(36) NOT NULL,
    currency_code character varying(5) NOT NULL,
    pretty_multiplier numeric(10,4),
    is_enabled boolean DEFAULT true,
    is_default boolean DEFAULT false
);


--
-- TOC entry 245 (class 1259 OID 18797)
-- Name: operator_custom_endpoints; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.operator_custom_endpoints (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    operator_id character varying(36) NOT NULL,
    endpoint_type config.endpoint_type NOT NULL,
    endpoint_url character varying(255) NOT NULL,
    is_enabled boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    environment config.environment_type
);


--
-- TOC entry 223 (class 1259 OID 17072)
-- Name: operator_default_game_config; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.operator_default_game_config (
    operator_id character varying(36) NOT NULL,
    default_rtp numeric(5,2),
    default_min_bet numeric(10,2),
    default_max_bet numeric(10,2),
    default_currency character varying(5),
    autoplay_enabled boolean DEFAULT false,
    buy_bonus_enabled boolean DEFAULT false,
    status public.game_status_type DEFAULT 'active'::public.game_status_type,
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 244 (class 1259 OID 18626)
-- Name: rgs_keys; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.rgs_keys (
    id integer NOT NULL,
    kid text NOT NULL,
    private_key_path text NOT NULL,
    public_key_pem text NOT NULL,
    algorithm text DEFAULT 'ES256'::text NOT NULL,
    status text DEFAULT 'active'::text NOT NULL,
    created_at timestamp without time zone DEFAULT now()
);


--
-- TOC entry 243 (class 1259 OID 18625)
-- Name: rgs_keys_id_seq; Type: SEQUENCE; Schema: config; Owner: -
--

CREATE SEQUENCE config.rgs_keys_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 4786 (class 0 OID 0)
-- Dependencies: 243
-- Name: rgs_keys_id_seq; Type: SEQUENCE OWNED BY; Schema: config; Owner: -
--

ALTER SEQUENCE config.rgs_keys_id_seq OWNED BY config.rgs_keys.id;


--
-- TOC entry 225 (class 1259 OID 17084)
-- Name: rtp_settings; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.rtp_settings (
    id integer NOT NULL,
    operator_id character varying(36),
    game_code character varying(12) NOT NULL,
    rtp numeric(5,2) NOT NULL,
    profile_name character varying(50),
    engine_url_per_rtp character varying(255),
    preloader_url_per_rtp character varying(255),
    engine_version character varying(50),
    preloader_version character varying(50),
    status public.rtp_status_type DEFAULT 'active'::public.rtp_status_type,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 224 (class 1259 OID 17083)
-- Name: rtp_settings_id_seq; Type: SEQUENCE; Schema: config; Owner: -
--

CREATE SEQUENCE config.rtp_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 4787 (class 0 OID 0)
-- Dependencies: 224
-- Name: rtp_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: config; Owner: -
--

ALTER SEQUENCE config.rtp_settings_id_seq OWNED BY config.rtp_settings.id;


--
-- TOC entry 231 (class 1259 OID 17143)
-- Name: ui_themes; Type: TABLE; Schema: config; Owner: -
--

CREATE TABLE config.ui_themes (
    theme_id integer NOT NULL,
    name character varying(100) NOT NULL,
    game_code character varying(10),
    branding jsonb,
    config jsonb,
    status public.ui_themes_status,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 230 (class 1259 OID 17142)
-- Name: ui_themes_theme_id_seq; Type: SEQUENCE; Schema: config; Owner: -
--

CREATE SEQUENCE config.ui_themes_theme_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- TOC entry 4788 (class 0 OID 0)
-- Dependencies: 230
-- Name: ui_themes_theme_id_seq; Type: SEQUENCE OWNED BY; Schema: config; Owner: -
--

ALTER SEQUENCE config.ui_themes_theme_id_seq OWNED BY config.ui_themes.theme_id;


--
-- TOC entry 235 (class 1259 OID 17206)
-- Name: game_rounds; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.game_rounds (
    round_id uuid DEFAULT gen_random_uuid() NOT NULL,
    session_id uuid,
    user_id uuid,
    game_code character varying(12) NOT NULL,
    bet_amount numeric(12,4) NOT NULL,
    win_amount numeric(10,2) DEFAULT 0 NOT NULL,
    outcome character varying(20) NOT NULL,
    server_seed character varying(255) NOT NULL,
    result_data jsonb,
    metadata jsonb,
    status public.round_status_type NOT NULL,
    demo_mode boolean DEFAULT false,
    start_time timestamp with time zone DEFAULT now() NOT NULL,
    end_time timestamp with time zone,
    client_seed character varying(255),
    nonce integer,
    layout_hash text NOT NULL,
    multiplier numeric(10,4) DEFAULT 0 NOT NULL,
    play_mode public.play_mode DEFAULT 'real'::public.play_mode,
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 234 (class 1259 OID 17188)
-- Name: game_sessions; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.game_sessions (
    session_id uuid DEFAULT gen_random_uuid() NOT NULL,
    operator_id character varying(36) NOT NULL,
    user_id uuid NOT NULL,
    game_code character varying(12) NOT NULL,
    play_mode public.play_mode_type,
    game_mode public.game_mode_type,
    currency character varying(5) NOT NULL,
    balance numeric(12,4),
    language character varying(5) DEFAULT 'en_gb'::text,
    ip character varying(39),
    country character varying(2),
    lobby_url character varying(255),
    cashier_url character varying(255),
    device_info character varying(255),
    user_agent character varying(255),
    status public.game_status_type DEFAULT 'open'::public.game_status_type,
    engine_path character varying(255),
    animation_path character varying(255),
    started_at timestamp with time zone DEFAULT now(),
    ended_at timestamp with time zone,
    platform character varying(10),
    jurisdiction character varying(3),
    metadata jsonb,
    promo_eligible boolean DEFAULT true NOT NULL,
    token character varying(255),
    CONSTRAINT check_status CHECK ((status = ANY (ARRAY['open'::public.game_status_type, 'closed'::public.game_status_type, 'suspended'::public.game_status_type, 'disabled'::public.game_status_type])))
);


--
-- TOC entry 237 (class 1259 OID 17251)
-- Name: operator_callbacks; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.operator_callbacks (
    callback_id uuid DEFAULT gen_random_uuid() NOT NULL,
    operator_id character varying(36),
    endpoint character varying(255),
    request jsonb,
    response jsonb,
    status public.callback_status_type,
    retries integer DEFAULT 0,
    "timestamp" timestamp with time zone DEFAULT now()
);


--
-- TOC entry 232 (class 1259 OID 17154)
-- Name: operators; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.operators (
    operator_id character varying(36) NOT NULL,
    name character varying(25) NOT NULL,
    domain character varying(255),
    status public.operator_status_type,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    default_currency character varying(5) DEFAULT 'USD'::text,
    default_jurisdiction character varying(5),
    lobby_url character varying(128),
    cashier_url character varying(128),
    base_api_url character varying(255)
);


--
-- TOC entry 247 (class 1259 OID 18954)
-- Name: round_events; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.round_events (
    event_id uuid DEFAULT gen_random_uuid() NOT NULL,
    round_id uuid NOT NULL,
    session_id uuid NOT NULL,
    game_code text NOT NULL,
    play_mode text NOT NULL,
    action_type text NOT NULL,
    step integer DEFAULT 0,
    tile_index integer,
    bet_amount numeric(12,4),
    multiplier numeric(12,4),
    win_amount numeric(12,4),
    state jsonb NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT round_events_action_type_check CHECK ((action_type = ANY (ARRAY['start'::text, 'reveal'::text, 'drop'::text, 'roll'::text, 'cashout'::text, 'bomb'::text, 'finish'::text, 'timeout'::text, 'rollback'::text]))),
    CONSTRAINT round_events_play_mode_check CHECK ((play_mode = ANY (ARRAY['real'::text, 'demo'::text, 'sim'::text])))
);


--
-- TOC entry 248 (class 1259 OID 18986)
-- Name: session_events; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.session_events (
    event_id uuid DEFAULT gen_random_uuid() NOT NULL,
    session_id uuid NOT NULL,
    operator_id character varying(36) NOT NULL,
    user_id uuid,
    event_type character varying(50) NOT NULL,
    event_category character varying(20) DEFAULT 'session'::character varying NOT NULL,
    event_data jsonb DEFAULT '{}'::jsonb,
    request_data jsonb,
    response_data jsonb,
    status character varying(20) DEFAULT 'success'::character varying,
    error_message text,
    ip_address inet,
    user_agent text,
    duration_ms integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT chk_session_events_category CHECK (((event_category)::text = ANY ((ARRAY['session'::character varying, 'authentication'::character varying, 'transaction'::character varying, 'operator'::character varying, 'compliance'::character varying])::text[]))),
    CONSTRAINT chk_session_events_event_type CHECK (((event_type)::text = ANY ((ARRAY['authenticate'::character varying, 'authenticate_failed'::character varying, 'session_created'::character varying, 'session_closed'::character varying, 'operator_validate'::character varying, 'operator_validate_failed'::character varying, 'balance_update'::character varying, 'balance_inquiry'::character varying, 'transaction_bet'::character varying, 'transaction_win'::character varying, 'transaction_failed'::character varying, 'api_call'::character varying, 'api_error'::character varying, 'fraud_check'::character varying, 'compliance_check'::character varying])::text[]))),
    CONSTRAINT chk_session_events_status CHECK (((status)::text = ANY ((ARRAY['success'::character varying, 'failed'::character varying, 'pending'::character varying, 'timeout'::character varying, 'error'::character varying])::text[])))
);


--
-- TOC entry 4789 (class 0 OID 0)
-- Dependencies: 248
-- Name: TABLE session_events; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON TABLE data.session_events IS 'Audit log for session-level events including authentication, operator interactions, and compliance events';


--
-- TOC entry 4790 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.event_type; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.event_type IS 'Specific type of session event (authenticate, balance_update, etc.)';


--
-- TOC entry 4791 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.event_category; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.event_category IS 'High-level category for grouping events (session, authentication, transaction, etc.)';


--
-- TOC entry 4792 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.event_data; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.event_data IS 'JSON data containing event-specific information and metadata';


--
-- TOC entry 4793 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.request_data; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.request_data IS 'JSON data of the original request that triggered this event';


--
-- TOC entry 4794 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.response_data; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.response_data IS 'JSON data of the response/result from this event';


--
-- TOC entry 4795 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.status; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.status IS 'Event outcome status (success, failed, pending, etc.)';


--
-- TOC entry 4796 (class 0 OID 0)
-- Dependencies: 248
-- Name: COLUMN session_events.duration_ms; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON COLUMN data.session_events.duration_ms IS 'Event processing duration in milliseconds for performance monitoring';


--
-- TOC entry 236 (class 1259 OID 17222)
-- Name: user_transactions; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.user_transactions (
    txn_id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    session_id uuid,
    round_id uuid,
    type public.txn_type NOT NULL,
    amount numeric(10,2) NOT NULL,
    balance_after numeric(10,2),
    currency character varying(5) DEFAULT 'USD'::text,
    currency_symbol character varying(10) DEFAULT '$'::text,
    token_address character varying(255),
    status public.txn_status_type,
    demo_mode boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 233 (class 1259 OID 17167)
-- Name: users; Type: TABLE; Schema: data; Owner: -
--

CREATE TABLE data.users (
    user_id uuid DEFAULT gen_random_uuid() NOT NULL,
    operator_id character varying(36) NOT NULL,
    external_user_id text NOT NULL,
    currency character varying(5) DEFAULT 'USD'::text NOT NULL,
    wallet_address character varying(255),
    metadata jsonb,
    status public.user_status_type,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- TOC entry 249 (class 1259 OID 19026)
-- Name: v_session_authentication_events; Type: VIEW; Schema: data; Owner: -
--

CREATE VIEW data.v_session_authentication_events AS
 SELECT se.event_id,
    se.session_id,
    se.operator_id,
    se.user_id,
    se.event_type,
    se.status,
    se.error_message,
    se.duration_ms,
    se.created_at,
    gs.game_code,
    gs.play_mode,
    gs.currency,
    o.name AS operator_name
   FROM ((data.session_events se
     JOIN data.game_sessions gs ON ((se.session_id = gs.session_id)))
     JOIN data.operators o ON (((se.operator_id)::text = (o.operator_id)::text)))
  WHERE ((se.event_category)::text = 'authentication'::text)
  ORDER BY se.created_at DESC;


--
-- TOC entry 4797 (class 0 OID 0)
-- Dependencies: 249
-- Name: VIEW v_session_authentication_events; Type: COMMENT; Schema: data; Owner: -
--

COMMENT ON VIEW data.v_session_authentication_events IS 'View for easy querying of session authentication events with related session and operator data';


--
-- TOC entry 4435 (class 2604 OID 17058)
-- Name: bet_limits id; Type: DEFAULT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.bet_limits ALTER COLUMN id SET DEFAULT nextval('config.bet_limits_id_seq'::regclass);


--
-- TOC entry 4448 (class 2604 OID 17104)
-- Name: operator_game_settings id; Type: DEFAULT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_game_settings ALTER COLUMN id SET DEFAULT nextval('config.game_settings_id_seq'::regclass);


--
-- TOC entry 4499 (class 2604 OID 18629)
-- Name: rgs_keys id; Type: DEFAULT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rgs_keys ALTER COLUMN id SET DEFAULT nextval('config.rgs_keys_id_seq'::regclass);


--
-- TOC entry 4444 (class 2604 OID 17087)
-- Name: rtp_settings id; Type: DEFAULT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rtp_settings ALTER COLUMN id SET DEFAULT nextval('config.rtp_settings_id_seq'::regclass);


--
-- TOC entry 4458 (class 2604 OID 17146)
-- Name: ui_themes theme_id; Type: DEFAULT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.ui_themes ALTER COLUMN theme_id SET DEFAULT nextval('config.ui_themes_theme_id_seq'::regclass);


--
-- TOC entry 4566 (class 2606 OID 17274)
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (log_id);


--
-- TOC entry 4570 (class 2606 OID 17297)
-- Name: fraud_flags fraud_flags_pkey; Type: CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.fraud_flags
    ADD CONSTRAINT fraud_flags_pkey PRIMARY KEY (flag_id);


--
-- TOC entry 4568 (class 2606 OID 17283)
-- Name: round_events round_events_pkey; Type: CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.round_events
    ADD CONSTRAINT round_events_pkey PRIMARY KEY (event_id);


--
-- TOC entry 4572 (class 2606 OID 17315)
-- Name: sessions_archive sessions_archive_pkey; Type: CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.sessions_archive
    ADD CONSTRAINT sessions_archive_pkey PRIMARY KEY (archived_session_id);


--
-- TOC entry 4532 (class 2606 OID 17066)
-- Name: bet_limits bet_limits_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.bet_limits
    ADD CONSTRAINT bet_limits_pkey PRIMARY KEY (id);


--
-- TOC entry 4540 (class 2606 OID 18260)
-- Name: currencies currencies_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.currencies
    ADD CONSTRAINT currencies_pkey PRIMARY KEY (currency_code);


--
-- TOC entry 4538 (class 2606 OID 17111)
-- Name: operator_game_settings game_settings_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_game_settings
    ADD CONSTRAINT game_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4530 (class 2606 OID 18385)
-- Name: games games_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.games
    ADD CONSTRAINT games_pkey PRIMARY KEY (game_code);


--
-- TOC entry 4574 (class 2606 OID 18170)
-- Name: languages languages_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.languages
    ADD CONSTRAINT languages_pkey PRIMARY KEY (language_code);


--
-- TOC entry 4585 (class 2606 OID 18847)
-- Name: operator_credentials operator_credentials_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_credentials
    ADD CONSTRAINT operator_credentials_pkey PRIMARY KEY (id);


--
-- TOC entry 4542 (class 2606 OID 18177)
-- Name: operator_currency_config operator_currency_config_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_currency_config
    ADD CONSTRAINT operator_currency_config_pkey PRIMARY KEY (operator_id, currency_code);


--
-- TOC entry 4580 (class 2606 OID 18805)
-- Name: operator_custom_endpoints operator_custom_endpoints_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_custom_endpoints
    ADD CONSTRAINT operator_custom_endpoints_pkey PRIMARY KEY (id);


--
-- TOC entry 4534 (class 2606 OID 18418)
-- Name: operator_default_game_config operator_default_game_config_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_default_game_config
    ADD CONSTRAINT operator_default_game_config_pkey PRIMARY KEY (operator_id);


--
-- TOC entry 4576 (class 2606 OID 18638)
-- Name: rgs_keys rgs_keys_kid_key; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rgs_keys
    ADD CONSTRAINT rgs_keys_kid_key UNIQUE (kid);


--
-- TOC entry 4578 (class 2606 OID 18636)
-- Name: rgs_keys rgs_keys_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rgs_keys
    ADD CONSTRAINT rgs_keys_pkey PRIMARY KEY (id);


--
-- TOC entry 4536 (class 2606 OID 17094)
-- Name: rtp_settings rtp_settings_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rtp_settings
    ADD CONSTRAINT rtp_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4544 (class 2606 OID 17153)
-- Name: ui_themes ui_themes_pkey; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.ui_themes
    ADD CONSTRAINT ui_themes_pkey PRIMARY KEY (theme_id);


--
-- TOC entry 4583 (class 2606 OID 18892)
-- Name: operator_custom_endpoints uniq_operator_endpoint_type_env; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_custom_endpoints
    ADD CONSTRAINT uniq_operator_endpoint_type_env UNIQUE (operator_id, environment, endpoint_type);


--
-- TOC entry 4587 (class 2606 OID 18903)
-- Name: operator_credentials uniq_operator_env; Type: CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_credentials
    ADD CONSTRAINT uniq_operator_env UNIQUE (operator_id, environment);


--
-- TOC entry 4560 (class 2606 OID 17216)
-- Name: game_rounds game_rounds_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.game_rounds
    ADD CONSTRAINT game_rounds_pkey PRIMARY KEY (round_id);


--
-- TOC entry 4554 (class 2606 OID 17200)
-- Name: game_sessions game_sessions_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.game_sessions
    ADD CONSTRAINT game_sessions_pkey PRIMARY KEY (session_id);


--
-- TOC entry 4564 (class 2606 OID 17260)
-- Name: operator_callbacks operator_callbacks_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.operator_callbacks
    ADD CONSTRAINT operator_callbacks_pkey PRIMARY KEY (callback_id);


--
-- TOC entry 4546 (class 2606 OID 17689)
-- Name: operators operators_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.operators
    ADD CONSTRAINT operators_pkey PRIMARY KEY (operator_id);


--
-- TOC entry 4592 (class 2606 OID 18966)
-- Name: round_events round_events_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.round_events
    ADD CONSTRAINT round_events_pkey PRIMARY KEY (event_id);


--
-- TOC entry 4604 (class 2606 OID 19000)
-- Name: session_events session_events_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.session_events
    ADD CONSTRAINT session_events_pkey PRIMARY KEY (event_id);


--
-- TOC entry 4562 (class 2606 OID 17235)
-- Name: user_transactions user_transactions_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.user_transactions
    ADD CONSTRAINT user_transactions_pkey PRIMARY KEY (txn_id);


--
-- TOC entry 4548 (class 2606 OID 18121)
-- Name: users users_operator_id_external_user_id_currency_key; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.users
    ADD CONSTRAINT users_operator_id_external_user_id_currency_key UNIQUE (operator_id, external_user_id, currency);


--
-- TOC entry 4550 (class 2606 OID 17178)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- TOC entry 4552 (class 2606 OID 18123)
-- Name: users users_wallet_address_key; Type: CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.users
    ADD CONSTRAINT users_wallet_address_key UNIQUE (wallet_address);


--
-- TOC entry 4581 (class 1259 OID 18893)
-- Name: uniq_operator_endpoint_type; Type: INDEX; Schema: config; Owner: -
--

CREATE UNIQUE INDEX uniq_operator_endpoint_type ON config.operator_custom_endpoints USING btree (operator_id, endpoint_type);


--
-- TOC entry 4555 (class 1259 OID 18652)
-- Name: idx_game_sessions_game_code; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_game_sessions_game_code ON data.game_sessions USING btree (game_code);


--
-- TOC entry 4556 (class 1259 OID 18089)
-- Name: idx_game_sessions_operator_id; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_game_sessions_operator_id ON data.game_sessions USING btree (operator_id);


--
-- TOC entry 4557 (class 1259 OID 17419)
-- Name: idx_game_sessions_status; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_game_sessions_status ON data.game_sessions USING btree (status);


--
-- TOC entry 4558 (class 1259 OID 18653)
-- Name: idx_game_sessions_user_game; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_game_sessions_user_game ON data.game_sessions USING btree (user_id, game_code);


--
-- TOC entry 4588 (class 1259 OID 18968)
-- Name: idx_round_events_game_code; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_round_events_game_code ON data.round_events USING btree (game_code);


--
-- TOC entry 4589 (class 1259 OID 18967)
-- Name: idx_round_events_round_id; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_round_events_round_id ON data.round_events USING btree (round_id);


--
-- TOC entry 4590 (class 1259 OID 18969)
-- Name: idx_round_events_session_id; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_round_events_session_id ON data.round_events USING btree (session_id);


--
-- TOC entry 4593 (class 1259 OID 19022)
-- Name: idx_session_events_created_at; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_created_at ON data.session_events USING btree (created_at);


--
-- TOC entry 4594 (class 1259 OID 19020)
-- Name: idx_session_events_event_category; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_event_category ON data.session_events USING btree (event_category);


--
-- TOC entry 4595 (class 1259 OID 19019)
-- Name: idx_session_events_event_type; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_event_type ON data.session_events USING btree (event_type);


--
-- TOC entry 4596 (class 1259 OID 19025)
-- Name: idx_session_events_failed_events; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_failed_events ON data.session_events USING btree (session_id, event_type, created_at) WHERE ((status)::text = ANY ((ARRAY['failed'::character varying, 'error'::character varying, 'timeout'::character varying])::text[]));


--
-- TOC entry 4597 (class 1259 OID 19024)
-- Name: idx_session_events_operator_category_created; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_operator_category_created ON data.session_events USING btree (operator_id, event_category, created_at);


--
-- TOC entry 4598 (class 1259 OID 19017)
-- Name: idx_session_events_operator_id; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_operator_id ON data.session_events USING btree (operator_id);


--
-- TOC entry 4599 (class 1259 OID 19016)
-- Name: idx_session_events_session_id; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_session_id ON data.session_events USING btree (session_id);


--
-- TOC entry 4600 (class 1259 OID 19023)
-- Name: idx_session_events_session_type_created; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_session_type_created ON data.session_events USING btree (session_id, event_type, created_at);


--
-- TOC entry 4601 (class 1259 OID 19021)
-- Name: idx_session_events_status; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_status ON data.session_events USING btree (status);


--
-- TOC entry 4602 (class 1259 OID 19018)
-- Name: idx_session_events_user_id; Type: INDEX; Schema: data; Owner: -
--

CREATE INDEX idx_session_events_user_id ON data.session_events USING btree (user_id);


--
-- TOC entry 4631 (class 2620 OID 18972)
-- Name: game_rounds trg_update_game_rounds_updated_at; Type: TRIGGER; Schema: data; Owner: -
--

CREATE TRIGGER trg_update_game_rounds_updated_at BEFORE UPDATE ON data.game_rounds FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- TOC entry 4623 (class 2606 OID 17303)
-- Name: fraud_flags fraud_flags_session_id_fkey; Type: FK CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.fraud_flags
    ADD CONSTRAINT fraud_flags_session_id_fkey FOREIGN KEY (session_id) REFERENCES data.game_sessions(session_id);


--
-- TOC entry 4624 (class 2606 OID 17298)
-- Name: fraud_flags fraud_flags_user_id_fkey; Type: FK CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.fraud_flags
    ADD CONSTRAINT fraud_flags_user_id_fkey FOREIGN KEY (user_id) REFERENCES data.users(user_id);


--
-- TOC entry 4622 (class 2606 OID 17284)
-- Name: round_events round_events_round_id_fkey; Type: FK CONSTRAINT; Schema: audit; Owner: -
--

ALTER TABLE ONLY audit.round_events
    ADD CONSTRAINT round_events_round_id_fkey FOREIGN KEY (round_id) REFERENCES data.game_rounds(round_id);


--
-- TOC entry 4605 (class 2606 OID 18452)
-- Name: bet_limits bet_limits_game_code_fkey; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.bet_limits
    ADD CONSTRAINT bet_limits_game_code_fkey FOREIGN KEY (game_code) REFERENCES config.games(game_code);


--
-- TOC entry 4606 (class 2606 OID 18470)
-- Name: bet_limits fk_currency; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.bet_limits
    ADD CONSTRAINT fk_currency FOREIGN KEY (currency) REFERENCES config.currencies(currency_code) ON DELETE SET NULL;


--
-- TOC entry 4610 (class 2606 OID 18386)
-- Name: operator_game_settings fk_game_code; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_game_settings
    ADD CONSTRAINT fk_game_code FOREIGN KEY (game_code) REFERENCES config.games(game_code) ON DELETE CASCADE;


--
-- TOC entry 4607 (class 2606 OID 18419)
-- Name: operator_default_game_config fk_operator; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_default_game_config
    ADD CONSTRAINT fk_operator FOREIGN KEY (operator_id) REFERENCES data.operators(operator_id) ON DELETE CASCADE;


--
-- TOC entry 4625 (class 2606 OID 18806)
-- Name: operator_custom_endpoints fk_operator; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_custom_endpoints
    ADD CONSTRAINT fk_operator FOREIGN KEY (operator_id) REFERENCES data.operators(operator_id) ON DELETE CASCADE;


--
-- TOC entry 4626 (class 2606 OID 18850)
-- Name: operator_credentials fk_operator; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_credentials
    ADD CONSTRAINT fk_operator FOREIGN KEY (operator_id) REFERENCES data.operators(operator_id) ON DELETE CASCADE;


--
-- TOC entry 4612 (class 2606 OID 18271)
-- Name: operator_currency_config fk_operator_currency; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_currency_config
    ADD CONSTRAINT fk_operator_currency FOREIGN KEY (currency_code) REFERENCES config.currencies(currency_code) ON DELETE CASCADE;


--
-- TOC entry 4627 (class 2606 OID 18855)
-- Name: operator_credentials fk_rgs_key; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_credentials
    ADD CONSTRAINT fk_rgs_key FOREIGN KEY (rgs_key_id) REFERENCES config.rgs_keys(id);


--
-- TOC entry 4608 (class 2606 OID 18436)
-- Name: rtp_settings fk_rtp_game_code; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rtp_settings
    ADD CONSTRAINT fk_rtp_game_code FOREIGN KEY (game_code) REFERENCES config.games(game_code) ON DELETE CASCADE;


--
-- TOC entry 4611 (class 2606 OID 18391)
-- Name: operator_game_settings game_settings_game_code_fkey; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_game_settings
    ADD CONSTRAINT game_settings_game_code_fkey FOREIGN KEY (game_code) REFERENCES config.games(game_code);


--
-- TOC entry 4613 (class 2606 OID 18276)
-- Name: operator_currency_config operator_currency_config_currency_code_fkey; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.operator_currency_config
    ADD CONSTRAINT operator_currency_config_currency_code_fkey FOREIGN KEY (currency_code) REFERENCES config.currencies(currency_code);


--
-- TOC entry 4609 (class 2606 OID 18441)
-- Name: rtp_settings rtp_settings_game_code_fkey; Type: FK CONSTRAINT; Schema: config; Owner: -
--

ALTER TABLE ONLY config.rtp_settings
    ADD CONSTRAINT rtp_settings_game_code_fkey FOREIGN KEY (game_code) REFERENCES config.games(game_code);


--
-- TOC entry 4615 (class 2606 OID 18266)
-- Name: game_sessions fk_game_sessions_currency; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.game_sessions
    ADD CONSTRAINT fk_game_sessions_currency FOREIGN KEY (currency) REFERENCES config.currencies(currency_code) ON DELETE SET NULL;


--
-- TOC entry 4616 (class 2606 OID 18171)
-- Name: game_sessions fk_game_sessions_language; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.game_sessions
    ADD CONSTRAINT fk_game_sessions_language FOREIGN KEY (language) REFERENCES config.languages(language_code) ON DELETE SET NULL;


--
-- TOC entry 4628 (class 2606 OID 19006)
-- Name: session_events fk_session_events_operator_id; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.session_events
    ADD CONSTRAINT fk_session_events_operator_id FOREIGN KEY (operator_id) REFERENCES data.operators(operator_id) ON DELETE CASCADE;


--
-- TOC entry 4629 (class 2606 OID 19001)
-- Name: session_events fk_session_events_session_id; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.session_events
    ADD CONSTRAINT fk_session_events_session_id FOREIGN KEY (session_id) REFERENCES data.game_sessions(session_id) ON DELETE CASCADE;


--
-- TOC entry 4630 (class 2606 OID 19011)
-- Name: session_events fk_session_events_user_id; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.session_events
    ADD CONSTRAINT fk_session_events_user_id FOREIGN KEY (user_id) REFERENCES data.users(user_id) ON DELETE SET NULL;


--
-- TOC entry 4617 (class 2606 OID 17435)
-- Name: game_sessions game_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.game_sessions
    ADD CONSTRAINT game_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES data.users(user_id);


--
-- TOC entry 4621 (class 2606 OID 18108)
-- Name: operator_callbacks operator_callbacks_operator_id_fkey; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.operator_callbacks
    ADD CONSTRAINT operator_callbacks_operator_id_fkey FOREIGN KEY (operator_id) REFERENCES data.operators(operator_id);


--
-- TOC entry 4618 (class 2606 OID 17246)
-- Name: user_transactions user_transactions_round_id_fkey; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.user_transactions
    ADD CONSTRAINT user_transactions_round_id_fkey FOREIGN KEY (round_id) REFERENCES data.game_rounds(round_id);


--
-- TOC entry 4619 (class 2606 OID 17241)
-- Name: user_transactions user_transactions_session_id_fkey; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.user_transactions
    ADD CONSTRAINT user_transactions_session_id_fkey FOREIGN KEY (session_id) REFERENCES data.game_sessions(session_id);


--
-- TOC entry 4620 (class 2606 OID 17236)
-- Name: user_transactions user_transactions_user_id_fkey; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.user_transactions
    ADD CONSTRAINT user_transactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES data.users(user_id);


--
-- TOC entry 4614 (class 2606 OID 18124)
-- Name: users users_operator_id_fkey; Type: FK CONSTRAINT; Schema: data; Owner: -
--

ALTER TABLE ONLY data.users
    ADD CONSTRAINT users_operator_id_fkey FOREIGN KEY (operator_id) REFERENCES data.operators(operator_id);


--
-- TOC entry 4783 (class 0 OID 0)
-- Dependencies: 8
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: -
--

GRANT USAGE ON SCHEMA public TO metrics_exporter;


-- Completed on 2025-06-03 21:16:36 +04

--
-- PostgreSQL database dump complete
--

