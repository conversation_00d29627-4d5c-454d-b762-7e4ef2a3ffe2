-- Database Structure Analysis Queries
-- Run these queries against your PostgreSQL database to gather comprehensive schema information

-- =====================================================
-- 1. LIST ALL SCHEMAS
-- =====================================================
SELECT schema_name 
FROM information_schema.schemata 
WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
ORDER BY schema_name;

-- =====================================================
-- 2. LIST ALL TABLES WITH BASIC INFO
-- =====================================================
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY schemaname, tablename;

-- =====================================================
-- 3. DETAILED TABLE STRUCTURE
-- =====================================================
SELECT 
    t.table_schema,
    t.table_name,
    c.column_name,
    c.ordinal_position,
    c.column_default,
    c.is_nullable,
    c.data_type,
    c.character_maximum_length,
    c.numeric_precision,
    c.numeric_scale,
    c.datetime_precision,
    c.udt_name
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY t.table_schema, t.table_name, c.ordinal_position;

-- =====================================================
-- 4. PRIMARY KEYS
-- =====================================================
SELECT 
    tc.table_schema,
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
    ON tc.constraint_name = kcu.constraint_name 
    AND tc.table_schema = kcu.table_schema
WHERE tc.constraint_type = 'PRIMARY KEY'
    AND tc.table_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY tc.table_schema, tc.table_name;

-- =====================================================
-- 5. FOREIGN KEYS
-- =====================================================
SELECT 
    tc.table_schema,
    tc.table_name,
    kcu.column_name,
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY tc.table_schema, tc.table_name;

-- =====================================================
-- 6. INDEXES
-- =====================================================
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY schemaname, tablename, indexname;

-- =====================================================
-- 7. CHECK CONSTRAINTS
-- =====================================================
SELECT 
    tc.table_schema,
    tc.table_name,
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.constraint_type = 'CHECK'
    AND tc.table_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY tc.table_schema, tc.table_name;

-- =====================================================
-- 8. VIEWS
-- =====================================================
SELECT 
    table_schema,
    table_name,
    view_definition
FROM information_schema.views
WHERE table_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY table_schema, table_name;

-- =====================================================
-- 9. FUNCTIONS AND PROCEDURES
-- =====================================================
SELECT 
    routine_schema,
    routine_name,
    routine_type,
    data_type,
    routine_definition
FROM information_schema.routines
WHERE routine_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY routine_schema, routine_name;

-- =====================================================
-- 10. TRIGGERS
-- =====================================================
SELECT 
    trigger_schema,
    trigger_name,
    event_manipulation,
    event_object_schema,
    event_object_table,
    action_statement,
    action_timing
FROM information_schema.triggers
WHERE trigger_schema NOT IN ('information_schema', 'pg_catalog')
ORDER BY trigger_schema, event_object_table, trigger_name;

-- =====================================================
-- 11. SPECIFIC CHECKS FOR OPERATOR CALLBACK TABLES
-- =====================================================

-- Check if operator_transactions table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'data' 
    AND table_name = 'operator_transactions'
) as operator_transactions_exists;

-- Check operator_transactions structure if it exists
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns
WHERE table_schema = 'data' 
    AND table_name = 'operator_transactions'
ORDER BY ordinal_position;

-- Check if audit tables exist
SELECT 
    table_name,
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'audit' 
        AND table_name = t.table_name
    ) as table_exists
FROM (VALUES 
    ('financial_events'),
    ('security_events'),
    ('callback_failures'),
    ('player_activity_log'),
    ('provably_fair_evidence')
) AS t(table_name);

-- Check indexes on operator_transactions
SELECT 
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'data' 
    AND tablename = 'operator_transactions';

-- =====================================================
-- 12. TABLE SIZES AND ROW COUNTS
-- =====================================================
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_stat_get_tuples_returned(c.oid) as row_count_estimate
FROM pg_tables pt
JOIN pg_class c ON c.relname = pt.tablename
JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = pt.schemaname
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- =====================================================
-- 13. RECENT TABLE MODIFICATIONS (if available)
-- =====================================================
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
ORDER BY schemaname, tablename;
