# Database Schema Documentation

**Database**: Insolence RGS PostgreSQL Database  
**Environment**: Production (DigitalOcean Managed)  
**Last Updated**: December 3, 2024  
**Documentation Status**: Template - Requires Live Database Analysis

## Overview

This document provides comprehensive documentation of the Insolence RGS database schema, including all tables, relationships, indexes, and constraints across multiple schemas.

## Database Schemas

### 1. `data` Schema
Primary operational data for the RGS system.

### 2. `config` Schema  
Configuration data for operators, games, and system settings.

### 3. `audit` Schema
Audit trails, compliance logging, and security events.

### 4. `public` Schema
Default PostgreSQL schema (if used).

---

## Schema: `data`

### Core Operational Tables

#### `game_sessions`
**Purpose**: Tracks active and historical game sessions for players.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| session_id | UUID | NO | gen_random_uuid() | Primary key, unique session identifier |
| user_id | UUID | NO | - | Player identifier from operator system |
| operator_id | VARCHAR(36) | NO | - | Operator identifier |
| game_code | VARCHAR(12) | YES | - | Current game being played |
| status | VARCHAR(20) | NO | 'active' | Session status (active, closed, expired) |
| currency | VARCHAR(3) | NO | - | Session currency (ISO 4217) |
| balance | NUMERIC(12,4) | YES | - | Current session balance |
| created_at | TIMESTAMP | NO | NOW() | Session creation timestamp |
| ended_at | TIMESTAMP | YES | - | Session end timestamp |
| ip_address | INET | YES | - | Player IP address |
| user_agent | TEXT | YES | - | Player browser/client info |

**Indexes**:
- PRIMARY KEY: `session_id`
- INDEX: `idx_game_sessions_user_operator` (user_id, operator_id, created_at)
- INDEX: `idx_game_sessions_status_created` (status, created_at)

**Foreign Keys**: None (external references)

---

#### `game_rounds`
**Purpose**: Individual game rounds within sessions.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| round_id | UUID | NO | gen_random_uuid() | Primary key, unique round identifier |
| session_id | UUID | NO | - | Reference to game_sessions |
| game_code | VARCHAR(12) | NO | - | Game identifier |
| bet_amount | NUMERIC(12,4) | NO | - | Amount wagered |
| win_amount | NUMERIC(12,4) | NO | 0 | Amount won |
| currency | VARCHAR(3) | NO | - | Round currency |
| round_data | JSONB | YES | - | Game-specific round data |
| created_at | TIMESTAMP | NO | NOW() | Round start timestamp |
| completed_at | TIMESTAMP | YES | - | Round completion timestamp |

**Indexes**:
- PRIMARY KEY: `round_id`
- INDEX: `idx_game_rounds_session_created` (session_id, created_at)
- INDEX: `idx_game_rounds_game_created` (game_code, created_at)

**Foreign Keys**:
- `session_id` → `game_sessions(session_id)`

---

#### `operator_transactions` ⭐ *Recently Added*
**Purpose**: Tracks all operator callback transactions for financial operations.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| transaction_id | VARCHAR(255) | NO | - | Primary key, unique transaction ID |
| round_id | UUID | YES | - | Associated game round |
| session_id | UUID | NO | - | Associated session |
| user_id | UUID | NO | - | Player identifier |
| operator_id | VARCHAR(36) | NO | - | Operator identifier |
| transaction_type | VARCHAR(20) | NO | - | Type: bet, win, promo_win |
| amount | NUMERIC(12,4) | NO | - | Transaction amount |
| currency | VARCHAR(3) | NO | - | Transaction currency |
| game_code | VARCHAR(12) | YES | - | Associated game |
| status | VARCHAR(20) | NO | 'pending' | Transaction status |
| operator_response | JSONB | YES | - | Response from operator API |
| operator_transaction_id | VARCHAR(255) | YES | - | Operator's transaction ID |
| new_balance | NUMERIC(12,4) | YES | - | Updated balance from operator |
| related_transaction_id | VARCHAR(255) | YES | - | For wins, references bet transaction |
| promotion_type | VARCHAR(20) | YES | - | For promo wins |
| tournament_id | VARCHAR(255) | YES | - | For tournament wins |
| retry_count | INTEGER | NO | 0 | Number of retry attempts |
| created_at | TIMESTAMP | NO | NOW() | Transaction creation time |
| completed_at | TIMESTAMP | YES | - | Transaction completion time |

**Indexes**:
- PRIMARY KEY: `transaction_id`
- INDEX: `idx_operator_transactions_session_type` (session_id, transaction_type, created_at)
- INDEX: `idx_operator_transactions_operator_status` (operator_id, status, created_at)
- INDEX: `idx_operator_transactions_status_retry` (status, retry_count, created_at) WHERE status = 'pending'

**Foreign Keys**:
- `session_id` → `game_sessions(session_id)`
- `round_id` → `game_rounds(round_id)`

**Check Constraints**:
- `transaction_type IN ('bet', 'win', 'promo_win')`
- `status IN ('pending', 'completed', 'failed', 'cancelled')`

---

#### `session_events`
**Purpose**: Event log for session activities and state changes.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| event_id | UUID | NO | gen_random_uuid() | Primary key |
| session_id | UUID | NO | - | Associated session |
| event_type | VARCHAR(50) | NO | - | Type of event |
| event_data | JSONB | YES | - | Event-specific data |
| created_at | TIMESTAMP | NO | NOW() | Event timestamp |

**Indexes**:
- PRIMARY KEY: `event_id`
- INDEX: `idx_session_events_session_created` (session_id, created_at)
- INDEX: `idx_session_events_type_created` (event_type, created_at)

**Foreign Keys**:
- `session_id` → `game_sessions(session_id)`

---

## Schema: `config`

### Configuration Tables

#### `operator_credentials`
**Purpose**: Operator authentication and configuration data.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| operator_id | VARCHAR(36) | NO | - | Primary key, operator identifier |
| operator_name | VARCHAR(255) | NO | - | Display name |
| api_key | VARCHAR(255) | NO | - | Operator API key |
| public_key | TEXT | YES | - | Public key for signature verification |
| private_key_path | VARCHAR(500) | YES | - | Path to private key file |
| algorithm | VARCHAR(10) | NO | 'RS256' | Signing algorithm |
| environment | VARCHAR(20) | NO | 'production' | Environment (production, staging, etc.) |
| base_api_url | VARCHAR(500) | YES | - | Operator's API base URL |
| is_active | BOOLEAN | NO | true | Whether operator is active |
| created_at | TIMESTAMP | NO | NOW() | Creation timestamp |
| updated_at | TIMESTAMP | NO | NOW() | Last update timestamp |

**Indexes**:
- PRIMARY KEY: `operator_id`
- INDEX: `idx_operator_credentials_active` (is_active, environment)

---

#### `rgs_keys`
**Purpose**: RGS signing keys for response authentication.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| id | SERIAL | NO | - | Primary key |
| algorithm | VARCHAR(10) | NO | - | Key algorithm (RS256, ES256) |
| private_key_path | VARCHAR(500) | NO | - | Path to private key |
| public_key_pem | TEXT | NO | - | Public key in PEM format |
| kid | VARCHAR(50) | NO | - | Key identifier |
| is_default | BOOLEAN | NO | false | Whether this is the default key |
| created_at | TIMESTAMP | NO | NOW() | Key creation timestamp |

**Indexes**:
- PRIMARY KEY: `id`
- UNIQUE: `kid`
- INDEX: `idx_rgs_keys_default` (is_default) WHERE is_default = true

---

## Schema: `audit`

### Audit and Compliance Tables

#### `financial_events` ⭐ *Recently Enhanced*
**Purpose**: Comprehensive financial transaction audit trail.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| event_id | UUID | NO | gen_random_uuid() | Primary key |
| event_type | VARCHAR(50) | NO | - | Event type (bet_placed, win_paid, etc.) |
| round_id | UUID | YES | - | Associated round |
| session_id | UUID | NO | - | Associated session |
| user_id | UUID | NO | - | Player identifier |
| operator_id | VARCHAR(36) | NO | - | Operator identifier |
| amount | NUMERIC(12,4) | NO | - | Transaction amount |
| currency | VARCHAR(3) | NO | - | Transaction currency |
| game_code | VARCHAR(12) | YES | - | Associated game |
| transaction_id | VARCHAR(255) | YES | - | Transaction identifier |
| related_transaction_id | VARCHAR(255) | YES | - | Related transaction reference |
| metadata | JSONB | NO | '{}' | Additional event data |
| created_at | TIMESTAMP | NO | NOW() | Event timestamp |

**Indexes**:
- PRIMARY KEY: `event_id`
- INDEX: `idx_financial_events_user_created` (user_id, created_at)
- INDEX: `idx_financial_events_operator_type_created` (operator_id, event_type, created_at)
- INDEX: `idx_financial_events_transaction_id` (transaction_id)

---

#### `security_events` ⭐ *Recently Added*
**Purpose**: Security-related events for monitoring and compliance.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| event_id | UUID | NO | gen_random_uuid() | Primary key |
| event_type | VARCHAR(50) | NO | - | Security event type |
| operator_id | VARCHAR(36) | YES | - | Associated operator |
| session_id | UUID | YES | - | Associated session |
| user_id | UUID | YES | - | Associated user |
| ip_address | INET | YES | - | Source IP address |
| user_agent | TEXT | YES | - | User agent string |
| success | BOOLEAN | NO | - | Whether event was successful |
| failure_reason | TEXT | YES | - | Reason for failure |
| risk_score | INTEGER | NO | 0 | Risk assessment score |
| metadata | JSONB | NO | '{}' | Additional event data |
| created_at | TIMESTAMP | NO | NOW() | Event timestamp |

**Indexes**:
- PRIMARY KEY: `event_id`
- INDEX: `idx_security_events_operator_type_created` (operator_id, event_type, created_at)
- INDEX: `idx_security_events_ip_created` (ip_address, created_at)
- INDEX: `idx_security_events_failed_attempts` (success, created_at) WHERE success = false

---

## Database Analysis Instructions

To complete this documentation with live database data, please run the queries in `docs/database/analysis-queries.sql` and provide the results. This will allow me to:

1. **Verify Table Existence**: Confirm all tables from our operator callback implementation exist
2. **Validate Schema Structure**: Ensure columns, types, and constraints match our specifications  
3. **Check Index Performance**: Verify all performance indexes are properly created
4. **Document Relationships**: Map all foreign key relationships and dependencies
5. **Identify Gaps**: Find any missing components or optimization opportunities

## Next Steps

1. Run the analysis queries against your database
2. Share the results for comprehensive documentation
3. Validate the operator callback implementation is properly deployed
4. Identify any performance optimization opportunities
5. Update this documentation with live schema information
