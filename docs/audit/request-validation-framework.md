# Request Validation Framework Assessment

## Current Validation State

### Middleware Stack Analysis
**Current Order**: Raw logging → Global signing → CORS → Body parsing → Auth → Request logging → Routes → Error handling

**Assessment**: Good foundation but missing critical validation layers

### Zod Schema Coverage Analysis

**✅ Covered Endpoints**:
- `/api/verify` - Operator verification
- `/api/session/create` - Session creation
- `/api/session/authenticate` - Session authentication  
- `/api/session/:session_id/close` - Session closure
- `/api/interact/play` - Game interactions
- `/api/config/operator-games` - Operator games config
- `/api/config/game-details` - Game details config

**❌ Missing Validation**:
- Operator callback endpoints (bet, win, promo-win)
- Health check endpoints
- File upload endpoints (if any)
- Admin/management endpoints
- Webhook endpoints

## Enhanced Validation Framework

### 1. Comprehensive Schema Registry
```typescript
// apps/backendApi/src/types/zod/index.ts
export const schemaRegistry = {
  // Existing schemas
  '/api/verify': operatorVerifySchema,
  '/api/session/create': sessionCreateSchema,
  '/api/session/authenticate': sessionAuthenticateSchema,
  '/api/session/:session_id/close': sessionCloseSchema,
  '/api/interact/play': gameInteractionSchema,
  '/api/config/operator-games': operatorGamesSchema,
  '/api/config/game-details': gameDetailsSchema,
  
  // New operator callback schemas
  '/api/operator/callback/bet': betCallbackSchema,
  '/api/operator/callback/win': winCallbackSchema,
  '/api/operator/callback/promo-win': promoWinCallbackSchema,
  
  // Admin schemas
  '/api/admin/operator/create': adminOperatorCreateSchema,
  '/api/admin/game/configure': adminGameConfigSchema,
  
  // Webhook schemas
  '/api/webhook/operator/:operator_id': webhookSchema,
} as const;
```

### 2. Enhanced Schema Validator Middleware
```typescript
// apps/backendApi/src/middleware/enhancedSchemaValidator.ts
import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { schemaRegistry } from '@backendApiTypes/zod';

interface ValidationOptions {
  validateParams?: boolean;
  validateQuery?: boolean;
  validateHeaders?: boolean;
  sanitizeInput?: boolean;
  logValidationErrors?: boolean;
}

export const enhancedSchemaValidator = (
  route: string, 
  options: ValidationOptions = {}
) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const {
      validateParams = false,
      validateQuery = false,
      validateHeaders = false,
      sanitizeInput = true,
      logValidationErrors = true
    } = options;

    try {
      const schema = schemaRegistry[route as keyof typeof schemaRegistry];
      
      if (!schema) {
        return next({
          status: 500,
          code: 'SCHEMA_NOT_CONFIGURED',
          message: `No validation schema configured for route ${route}`,
        });
      }

      // Inject URL parameters into body for validation
      if (validateParams && req.params) {
        req.body = { ...req.body, ...req.params };
      }

      // Validate request body
      const bodyResult = schema.safeParse(req.body);
      if (!bodyResult.success) {
        if (logValidationErrors) {
          console.warn('[Enhanced Validation] Body validation failed:', {
            route,
            errors: bodyResult.error.errors,
            body: req.body
          });
        }
        
        return next(createValidationError(bodyResult.error, 'body'));
      }

      // Sanitize input if enabled
      if (sanitizeInput) {
        req.body = sanitizeObject(bodyResult.data);
      } else {
        req.body = bodyResult.data;
      }

      // Validate query parameters if requested
      if (validateQuery && req.query) {
        // Implement query validation logic
      }

      // Validate headers if requested
      if (validateHeaders) {
        // Implement header validation logic
      }

      next();

    } catch (error) {
      next({
        status: 500,
        code: 'VALIDATION_SYSTEM_ERROR',
        message: 'Internal validation system error',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
};

function createValidationError(zodError: ZodError, source: string) {
  const errorDetails = zodError.errors.map((e) => ({
    path: e.path.join('.'),
    message: e.message,
    code: e.code,
    source
  }));

  return {
    status: 400,
    code: 'VALIDATION_ERROR',
    message: `Validation failed for ${source}`,
    details: errorDetails,
  };
}

function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    // Remove potential XSS vectors
    return obj
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
  
  if (typeof obj === 'object' && obj !== null) {
    const sanitized: any = Array.isArray(obj) ? [] : {};
    for (const key in obj) {
      sanitized[key] = sanitizeObject(obj[key]);
    }
    return sanitized;
  }
  
  return obj;
}
```

### 3. Request/Response Data Transformation
```typescript
// apps/backendApi/src/middleware/dataTransformer.ts
export const requestTransformer = (req: Request, res: Response, next: NextFunction) => {
  // Transform incoming data formats
  if (req.body) {
    // Convert string numbers to actual numbers where appropriate
    req.body = transformNumericFields(req.body);
    
    // Normalize date formats
    req.body = normalizeDateFields(req.body);
    
    // Trim whitespace from strings
    req.body = trimStringFields(req.body);
  }
  
  next();
};

export const responseTransformer = (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json.bind(res);
  
  res.json = (body: any) => {
    // Ensure consistent response format
    const transformedBody = {
      success: body.success !== false,
      data: body.data || body,
      timestamp: new Date().toISOString(),
      request_id: req.headers['x-request-id'] || generateRequestId(),
      ...body
    };
    
    return originalJson(transformedBody);
  };
  
  next();
};
```

### 4. Standardized Error Response Format
```typescript
// apps/backendApi/src/middleware/standardizedErrorHandler.ts
interface StandardErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any[];
    timestamp: string;
    request_id: string;
    trace_id?: string;
  };
}

export const standardizedErrorHandler = async (
  err: unknown,
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const errorObj = typeof err === 'object' && err !== null ? (err as any) : {};
  
  const standardError: StandardErrorResponse = {
    success: false,
    error: {
      code: errorObj.code || 'INTERNAL_ERROR',
      message: errorObj.message || 'An internal error occurred',
      details: errorObj.details || undefined,
      timestamp: new Date().toISOString(),
      request_id: req.headers['x-request-id'] as string || generateRequestId(),
      trace_id: req.headers['x-trace-id'] as string || undefined,
    }
  };

  // Log error with context
  logger.error('Request error', {
    error: errorObj,
    request: {
      method: req.method,
      url: req.originalUrl,
      operator: req.operator?.operatorId || 'unknown',
      user_agent: req.headers['user-agent'],
      ip: req.ip
    },
    response: standardError
  });

  // Sign error response if operator context exists
  try {
    const operatorId = req.body?.operator_id || req.operator?.operatorId;
    if (operatorId) {
      const { signature, kid, signedAt } = await signPayload(standardError, operatorId);
      res.setHeader('x-signature', signature);
      res.setHeader('x-signature-kid', kid);
      res.setHeader('x-signed-at', signedAt);
    }
  } catch (signingError) {
    console.warn('[Error Response Signing Failed]', signingError);
  }

  res.status(errorObj.status || 500).json(standardError);
};
```

## Validation Completeness Assessment

### Current Coverage: 70%
- ✅ Core session management endpoints
- ✅ Game interaction endpoints  
- ✅ Configuration endpoints
- ❌ Operator callback endpoints
- ❌ Admin/management endpoints
- ❌ Webhook endpoints

### Target Coverage: 95%
**Missing Validations to Implement**:

1. **Operator Callback Validation** (High Priority)
2. **Admin Endpoint Validation** (Medium Priority)  
3. **Webhook Validation** (Medium Priority)
4. **File Upload Validation** (Low Priority - if applicable)

## Implementation Recommendations

### Phase 1: Core Validation Enhancement (Week 1-2)
1. Implement enhanced schema validator with sanitization
2. Add missing operator callback schemas
3. Implement standardized error handling
4. Add request/response transformation

### Phase 2: Advanced Validation Features (Week 3-4)
1. Add query parameter and header validation
2. Implement validation performance monitoring
3. Add validation rule caching
4. Create validation testing framework

### Phase 3: Monitoring and Optimization (Week 5-6)
1. Add validation metrics collection
2. Implement validation performance optimization
3. Create validation error analytics
4. Add automated validation testing
