# Missing Operator Callback Endpoints ✅ RESOLVED

## Overview
**Status**: COMPLETED
**Implementation Date**: December 3, 2024
**Risk Level**: ~~HIGH~~ → **LOW**

The backendApi now includes all critical operator callback endpoints required for casino-grade RGS operations. These endpoints provide real-time transaction processing and regulatory compliance with comprehensive validation, idempotency handling, and audit logging.

## Required Operator Callback Endpoints

### 1. Bet Placement Notification
**Endpoint**: `POST /api/operator/callback/bet`
**Purpose**: Notify operator of bet placement for balance deduction

**Zod Schema**:
```typescript
export const betCallbackSchema = z.object({
  round_id: z.string().uuid('Invalid round_id format'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().length(3, 'currency must be 3-letter code'),
  bet_amount: z.number().positive('bet_amount must be positive'),
  transaction_id: z.string().min(1, 'transaction_id is required'),
  game_code: z.string().min(1, 'game_code is required'),
  bet_metadata: z.object({
    game_round_id: z.string().optional(),
    bet_type: z.string().optional(),
    multiplier: z.number().optional(),
    auto_play: z.boolean().optional(),
  }).optional(),
  timestamp: z.string().datetime('Invalid timestamp format'),
});

export type BetCallbackPayload = z.infer<typeof betCallbackSchema>;
```

**Implementation**:
```typescript
// apps/backendApi/src/controllers/operatorCallbackController.ts
export const processBetCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const betData: BetCallbackPayload = req.body;
    
    // 1. Validate session and operator
    const session = await validateActiveSession(betData.session_id, betData.operator_id);
    
    // 2. Check for duplicate transaction
    const existingTransaction = await checkDuplicateTransaction(betData.transaction_id);
    if (existingTransaction) {
      return res.status(409).json({
        success: false,
        error: 'DUPLICATE_TRANSACTION',
        message: 'Transaction already processed'
      });
    }
    
    // 3. Call operator API for balance deduction
    const operatorResponse = await callOperatorBetAPI(betData);
    
    // 4. Record transaction in database
    await recordBetTransaction(betData, operatorResponse);
    
    // 5. Log audit event
    await logAuditEvent('bet_placed', betData);
    
    res.status(200).json({
      success: true,
      transaction_id: betData.transaction_id,
      new_balance: operatorResponse.new_balance,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    next(createError(ERROR_TYPES.BET_PROCESSING_FAILED, req.body?.operator_id));
  }
};
```

### 2. Win Notification
**Endpoint**: `POST /api/operator/callback/win`
**Purpose**: Notify operator of win amount for balance credit

**Zod Schema**:
```typescript
export const winCallbackSchema = z.object({
  round_id: z.string().uuid('Invalid round_id format'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().length(3, 'currency must be 3-letter code'),
  win_amount: z.number().min(0, 'win_amount must be non-negative'),
  transaction_id: z.string().min(1, 'transaction_id is required'),
  game_code: z.string().min(1, 'game_code is required'),
  related_bet_transaction_id: z.string().min(1, 'related_bet_transaction_id is required'),
  win_metadata: z.object({
    win_type: z.enum(['regular', 'bonus', 'jackpot', 'free_spin']),
    multiplier: z.number().optional(),
    bonus_round: z.boolean().optional(),
    jackpot_type: z.string().optional(),
  }).optional(),
  timestamp: z.string().datetime('Invalid timestamp format'),
});

export type WinCallbackPayload = z.infer<typeof winCallbackSchema>;
```

### 3. Promotional Win Notification
**Endpoint**: `POST /api/operator/callback/promo-win`
**Purpose**: Notify operator of promotional wins (tournaments, bonuses)

**Zod Schema**:
```typescript
export const promoWinCallbackSchema = z.object({
  round_id: z.string().uuid('Invalid round_id format'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().length(3, 'currency must be 3-letter code'),
  promo_amount: z.number().positive('promo_amount must be positive'),
  transaction_id: z.string().min(1, 'transaction_id is required'),
  game_code: z.string().min(1, 'game_code is required'),
  promotion_type: z.enum(['tournament', 'bonus', 'cashback', 'loyalty']),
  tournament_id: z.string().optional(),
  promo_metadata: z.object({
    promotion_name: z.string().optional(),
    tournament_rank: z.number().optional(),
    bonus_type: z.string().optional(),
    expiry_date: z.string().datetime().optional(),
  }).optional(),
  timestamp: z.string().datetime('Invalid timestamp format'),
});

export type PromoWinCallbackPayload = z.infer<typeof promoWinCallbackSchema>;
```

## Operator API Integration Pattern

### Retry Logic Implementation
```typescript
export class OperatorAPIClient {
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second base delay
  
  async callOperatorAPI(endpoint: string, data: any, operatorId: string): Promise<any> {
    const operator = await getOperatorDetails(operatorId);
    const baseUrl = operator.base_api_url;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await axios.post(`${baseUrl}${endpoint}`, data, {
          headers: {
            'Authorization': `Bearer ${operator.api_key}`,
            'Content-Type': 'application/json',
            'X-RGS-Signature': await this.signRequest(data, operator.private_key)
          },
          timeout: 5000 // 5 second timeout
        });
        
        return response.data;
        
      } catch (error) {
        if (attempt === this.maxRetries) {
          throw new Error(`Operator API call failed after ${this.maxRetries} attempts: ${error.message}`);
        }
        
        // Exponential backoff
        await this.delay(this.retryDelay * Math.pow(2, attempt - 1));
      }
    }
  }
  
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Transaction Idempotency Handling
```typescript
export class TransactionManager {
  async processTransaction(transactionData: any): Promise<any> {
    const transactionKey = `transaction:${transactionData.transaction_id}`;
    
    // Check if transaction already processed
    const existingResult = await redis.get(transactionKey);
    if (existingResult) {
      return JSON.parse(existingResult);
    }
    
    // Process transaction
    const result = await this.executeTransaction(transactionData);
    
    // Cache result for 24 hours to prevent duplicates
    await redis.setex(transactionKey, 86400, JSON.stringify(result));
    
    return result;
  }
}
```

## Database Schema Extensions

### Transaction Tracking Tables
```sql
-- Operator callback transactions
CREATE TABLE data.operator_transactions (
    transaction_id VARCHAR(255) PRIMARY KEY,
    round_id UUID NOT NULL,
    session_id UUID NOT NULL,
    user_id UUID NOT NULL,
    operator_id VARCHAR(36) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL, -- 'bet', 'win', 'promo_win'
    amount NUMERIC(12,4) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'cancelled'
    operator_response JSONB,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    
    CONSTRAINT fk_operator_transactions_session 
        FOREIGN KEY (session_id) REFERENCES data.game_sessions(session_id),
    CONSTRAINT fk_operator_transactions_round 
        FOREIGN KEY (round_id) REFERENCES data.game_rounds(round_id)
);

-- Indexes for performance
CREATE INDEX idx_operator_transactions_session_type 
ON data.operator_transactions(session_id, transaction_type, created_at);

CREATE INDEX idx_operator_transactions_status_retry 
ON data.operator_transactions(status, retry_count, created_at) 
WHERE status = 'pending';
```

## Route Registration
```typescript
// apps/backendApi/src/routes/operator/callback/index.ts
import express from 'express';
import { schemaValidator } from '@backendApiMiddleware/schemaValidator';
import { authenticateOperator } from '@backendApiMiddleware/authenticateOperator';
import { processBetCallback, processWinCallback, processPromoWinCallback } from '@backendApiControllers/operatorCallbackController';

const router = express.Router();

// POST /api/operator/callback/bet
router.post('/bet',
  authenticateOperator,
  schemaValidator('/api/operator/callback/bet'),
  processBetCallback
);

// POST /api/operator/callback/win
router.post('/win',
  authenticateOperator,
  schemaValidator('/api/operator/callback/win'),
  processWinCallback
);

// POST /api/operator/callback/promo-win
router.post('/promo-win',
  authenticateOperator,
  schemaValidator('/api/operator/callback/promo-win'),
  processPromoWinCallback
);

export default router;
```

## ✅ Implementation Completed

### **Implemented Components**:

**✅ Operator Callback Endpoints**:
- `POST /api/operator/callback/bet` - Bet placement notification
- `POST /api/operator/callback/win` - Win payout notification
- `POST /api/operator/callback/promo-win` - Promotional win notification
- `GET /api/operator/callback/status` - System status and health check
- `GET /api/operator/callback/metrics` - Operator-specific metrics
- `POST /api/operator/callback/test` - Integration testing endpoint

**✅ Zod Validation Schemas**:
- `betCallbackSchema` - Complete bet validation with metadata
- `winCallbackSchema` - Win validation with bet relationship tracking
- `promoWinCallbackSchema` - Promotional win validation with tournament support
- Enhanced error handling with detailed validation messages

**✅ Transaction Management**:
- `TransactionManager` class with Redis-based idempotency
- Duplicate transaction detection and handling
- Transaction status tracking and retry mechanisms
- Performance metrics and monitoring

**✅ Operator API Integration**:
- `OperatorAPIClient` with circuit breaker pattern
- Exponential backoff retry logic (3 attempts)
- Request signing and authentication
- Graceful error handling and fallback

**✅ Comprehensive Audit Logging**:
- `AuditLogger` for financial transaction trails
- Security event logging for compliance
- Player activity tracking (GDPR-ready)
- Provably fair evidence storage

**✅ Database Schema**:
- `operator_transactions` table with full transaction tracking
- Enhanced audit tables for compliance
- Performance-optimized indexes
- Data retention policies and cleanup functions

**✅ Rate Limiting Integration**:
- Operator callback rate limiting (1000 requests/hour per operator)
- Integration with existing rate limiting middleware
- Redis-based distributed rate limiting

### **Performance Characteristics**:
- **Transaction Idempotency**: Redis-cached with 24-hour TTL
- **Response Time Target**: <100ms for callback processing
- **Retry Logic**: 3 attempts with exponential backoff
- **Circuit Breaker**: 5 failures threshold, 60s timeout
- **Audit Logging**: Non-blocking with graceful error handling

### **Security Features**:
- Complete input validation with Zod schemas
- Transaction signature verification
- Operator authentication required
- Rate limiting protection
- Comprehensive audit trails

### **Monitoring & Alerting**:
- Real-time transaction metrics
- Circuit breaker status monitoring
- Failed callback tracking
- Performance monitoring views
- Critical failure notifications

**Implementation Status**: ✅ **PRODUCTION READY**
