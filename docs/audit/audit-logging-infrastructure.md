# Audit and Logging Infrastructure Assessment

## Current Logging State

### Existing Logging Components
1. **Raw Request Logger**: Captures all incoming requests before processing
2. **Formatted Request Logger**: Logs processed requests with timing
3. **Session Events Logger**: Records session-related events in database
4. **Error Handler**: Logs errors with context
5. **<PERSON> Logger**: File-based logging with rotation

### Current Database Audit Tables
- `audit.audit_logs` - General audit events
- `audit.fraud_flags` - Fraud detection events  
- `audit.round_events` - Game round events
- `audit.sessions_archive` - Archived session data
- `data.session_events` - Real-time session events
- `data.round_events` - Game round tracking

## Critical Gaps in Audit Logging

### 1. Missing Security Event Logging
**Current State**: Basic authentication logging exists
**Missing Events**:
- Failed authentication attempts with pattern analysis
- Suspicious request patterns (rate limiting violations)
- API key usage patterns and anomalies
- Session hijacking attempts
- Operator signature validation failures

### 2. Incomplete Financial Transaction Logging
**Current State**: Basic round events logged
**Missing Events**:
- Bet placement confirmations
- Win payout confirmations  
- Balance update events
- Transaction rollback events
- Operator callback failures

### 3. Regulatory Compliance Gaps
**Current State**: Basic session tracking
**Missing Requirements**:
- Complete player activity trails
- Provably fair gaming evidence
- Operator integration audit trails
- Data retention compliance tracking
- GDPR compliance logging

## Enhanced Audit Logging Framework

### 1. Comprehensive Security Event Logging
```typescript
// apps/backendApi/src/services/audit/securityAuditLogger.ts
export class SecurityAuditLogger {
  async logAuthenticationAttempt(event: {
    operator_id: string;
    ip_address: string;
    user_agent: string;
    success: boolean;
    failure_reason?: string;
    session_id?: string;
  }): Promise<void> {
    await db.query(`
      INSERT INTO audit.security_events 
      (event_type, operator_id, ip_address, user_agent, success, failure_reason, 
       session_id, created_at, metadata)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), $8)
    `, [
      'authentication_attempt',
      event.operator_id,
      event.ip_address,
      event.user_agent,
      event.success,
      event.failure_reason || null,
      event.session_id || null,
      JSON.stringify({ timestamp: new Date().toISOString() })
    ]);
  }

  async logSuspiciousActivity(event: {
    operator_id: string;
    session_id?: string;
    activity_type: string;
    risk_score: number;
    details: Record<string, any>;
  }): Promise<void> {
    await db.query(`
      INSERT INTO audit.fraud_flags 
      (user_id, session_id, reason, metadata, flagged_at, risk_score)
      VALUES ($1, $2, $3, $4, NOW(), $5)
    `, [
      event.session_id ? await this.getUserIdFromSession(event.session_id) : null,
      event.session_id,
      event.activity_type,
      JSON.stringify(event.details),
      event.risk_score
    ]);
  }
}
```

### 2. Financial Transaction Audit Trail
```typescript
// apps/backendApi/src/services/audit/financialAuditLogger.ts
export class FinancialAuditLogger {
  async logBetPlacement(transaction: {
    round_id: string;
    session_id: string;
    user_id: string;
    operator_id: string;
    bet_amount: number;
    currency: string;
    game_code: string;
    transaction_id: string;
  }): Promise<void> {
    await db.query(`
      INSERT INTO audit.financial_events 
      (event_type, round_id, session_id, user_id, operator_id, amount, currency,
       game_code, transaction_id, created_at, metadata)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
    `, [
      'bet_placed',
      transaction.round_id,
      transaction.session_id,
      transaction.user_id,
      transaction.operator_id,
      transaction.bet_amount,
      transaction.currency,
      transaction.game_code,
      transaction.transaction_id,
      JSON.stringify({
        timestamp: new Date().toISOString(),
        source: 'rgs_backend'
      })
    ]);
  }

  async logWinPayout(transaction: {
    round_id: string;
    session_id: string;
    user_id: string;
    operator_id: string;
    win_amount: number;
    currency: string;
    game_code: string;
    transaction_id: string;
    related_bet_transaction_id: string;
  }): Promise<void> {
    await db.query(`
      INSERT INTO audit.financial_events 
      (event_type, round_id, session_id, user_id, operator_id, amount, currency,
       game_code, transaction_id, related_transaction_id, created_at, metadata)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), $11)
    `, [
      'win_paid',
      transaction.round_id,
      transaction.session_id,
      transaction.user_id,
      transaction.operator_id,
      transaction.win_amount,
      transaction.currency,
      transaction.game_code,
      transaction.transaction_id,
      transaction.related_bet_transaction_id,
      JSON.stringify({
        timestamp: new Date().toISOString(),
        source: 'rgs_backend'
      })
    ]);
  }
}
```

### 3. Regulatory Compliance Logging
```typescript
// apps/backendApi/src/services/audit/complianceAuditLogger.ts
export class ComplianceAuditLogger {
  async logPlayerActivity(activity: {
    user_id: string;
    session_id: string;
    operator_id: string;
    activity_type: string;
    game_code?: string;
    details: Record<string, any>;
  }): Promise<void> {
    await db.query(`
      INSERT INTO audit.player_activity_log 
      (user_id, session_id, operator_id, activity_type, game_code, 
       details, created_at, retention_until)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW() + INTERVAL '7 years')
    `, [
      activity.user_id,
      activity.session_id,
      activity.operator_id,
      activity.activity_type,
      activity.game_code || null,
      JSON.stringify(activity.details)
    ]);
  }

  async logProvablyFairEvidence(evidence: {
    round_id: string;
    server_seed: string;
    client_seed: string;
    nonce: number;
    result_hash: string;
    game_code: string;
  }): Promise<void> {
    await db.query(`
      INSERT INTO audit.provably_fair_evidence 
      (round_id, server_seed_hash, client_seed, nonce, result_hash, 
       game_code, created_at, retention_until)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW() + INTERVAL '7 years')
    `, [
      evidence.round_id,
      this.hashSeed(evidence.server_seed), // Store hash, not actual seed
      evidence.client_seed,
      evidence.nonce,
      evidence.result_hash,
      evidence.game_code
    ]);
  }
}
```

## Required Database Schema Extensions

### 1. Security Events Table
```sql
CREATE TABLE audit.security_events (
    event_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    operator_id VARCHAR(36),
    session_id UUID,
    user_id UUID,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    risk_score INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Indexes for performance
    INDEX idx_security_events_operator_type_created (operator_id, event_type, created_at),
    INDEX idx_security_events_ip_created (ip_address, created_at),
    INDEX idx_security_events_failed_attempts (success, created_at) WHERE success = false
);
```

### 2. Financial Events Table
```sql
CREATE TABLE audit.financial_events (
    event_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL, -- 'bet_placed', 'win_paid', 'balance_updated'
    round_id UUID,
    session_id UUID NOT NULL,
    user_id UUID NOT NULL,
    operator_id VARCHAR(36) NOT NULL,
    amount NUMERIC(12,4) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    game_code VARCHAR(12),
    transaction_id VARCHAR(255),
    related_transaction_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_financial_events_session 
        FOREIGN KEY (session_id) REFERENCES data.game_sessions(session_id),
    CONSTRAINT fk_financial_events_round 
        FOREIGN KEY (round_id) REFERENCES data.game_rounds(round_id),
        
    -- Indexes for performance and compliance queries
    INDEX idx_financial_events_user_created (user_id, created_at),
    INDEX idx_financial_events_operator_type_created (operator_id, event_type, created_at),
    INDEX idx_financial_events_transaction_id (transaction_id)
);
```

### 3. Player Activity Log Table
```sql
CREATE TABLE audit.player_activity_log (
    activity_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    session_id UUID,
    operator_id VARCHAR(36) NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    game_code VARCHAR(12),
    details JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    retention_until TIMESTAMP NOT NULL, -- For GDPR compliance
    
    -- Partitioning by month for performance
    PARTITION BY RANGE (created_at),
    
    -- Indexes
    INDEX idx_player_activity_user_created (user_id, created_at),
    INDEX idx_player_activity_retention (retention_until)
);
```

## Performance Impact Analysis

### Current Logging Performance
- **Raw Request Logging**: ~2ms overhead per request
- **Session Event Logging**: ~5-10ms overhead per session operation
- **Database Audit Logging**: ~3-8ms overhead per audit event

### Enhanced Logging Performance Impact
- **Security Event Logging**: +3-5ms per authentication
- **Financial Event Logging**: +5-8ms per transaction
- **Compliance Logging**: +2-4ms per player activity

### Optimization Strategies
1. **Async Logging**: Use background queues for non-critical audit events
2. **Batch Inserts**: Group multiple audit events into single database operations
3. **Partitioned Tables**: Use monthly partitions for large audit tables
4. **Selective Logging**: Configure logging levels based on environment

## Data Privacy Compliance (GDPR)

### Data Retention Policies
```typescript
// Automated data retention cleanup
export class DataRetentionManager {
  async cleanupExpiredData(): Promise<void> {
    // Remove expired player activity logs
    await db.query(`
      DELETE FROM audit.player_activity_log 
      WHERE retention_until < NOW()
    `);
    
    // Archive old financial events (keep for 7 years)
    await db.query(`
      INSERT INTO audit.financial_events_archive 
      SELECT * FROM audit.financial_events 
      WHERE created_at < NOW() - INTERVAL '1 year'
    `);
    
    // Anonymize old session data
    await db.query(`
      UPDATE data.game_sessions 
      SET user_agent = 'ANONYMIZED', ip = NULL 
      WHERE ended_at < NOW() - INTERVAL '2 years'
    `);
  }
}
```

## Implementation Timeline

**Week 1**: Security event logging and database schema
**Week 2**: Financial transaction audit trail
**Week 3**: Compliance logging and data retention
**Week 4**: Performance optimization and monitoring
**Week 5**: GDPR compliance features and testing
