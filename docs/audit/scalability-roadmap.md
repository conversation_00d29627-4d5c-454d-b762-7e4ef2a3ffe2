# Architecture Scalability Roadmap

## Current Architecture Assessment

### Current Capacity
- **Database**: Single PostgreSQL instance, 20 connection pool
- **Redis**: Single instance, basic caching
- **Application**: Single Node.js process
- **Load Balancing**: None (single instance)

### Target Scale Requirements
- **Games**: 100+ concurrent games
- **Operators**: 1,000+ operators  
- **Users**: 100,000+ total users (10,000 concurrent)
- **Requests**: 10,000+ requests/minute peak
- **Data**: 1TB+ database, 100GB+ Redis cache

## Critical Bottlenecks Analysis

### 1. Database Scalability Bottlenecks
**Current Issues**:
- Single database instance (SPOF)
- Limited connection pool (20 connections)
- No read/write separation
- No horizontal scaling capability

**Impact at Target Scale**:
- Connection pool exhaustion at ~500 concurrent users
- Query performance degradation with large datasets
- Database becomes primary bottleneck

### 2. Redis Cache Bottlenecks  
**Current Issues**:
- Single Redis instance
- No clustering or sharding
- Limited memory capacity
- No failover mechanism

**Impact at Target Scale**:
- Memory exhaustion with 1,000+ operators
- C<PERSON> miss storms during high load
- Single point of failure for session data

### 3. Application Layer Bottlenecks
**Current Issues**:
- Single Node.js process
- No horizontal scaling
- Memory-bound session management
- CPU-bound cryptographic operations

**Impact at Target Scale**:
- CPU saturation at ~2,000 concurrent requests
- Memory exhaustion with large session counts
- No fault tolerance

## Scalability Roadmap

### Phase 1: Database Scaling (Weeks 1-3)

#### 1.1 Database Connection Pooling Enhancement
```typescript
// Enhanced database configuration
export const scalableDbConfig = {
  // Master database for writes
  master: new Pool({
    host: process.env.DB_MASTER_HOST,
    port: Number(process.env.DB_MASTER_PORT),
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    max: 50, // Increased from 20
    min: 10, // Minimum connections
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
    application_name: 'rgs-backend-master',
  }),
  
  // Read replicas for queries
  replicas: [
    new Pool({
      host: process.env.DB_REPLICA_1_HOST,
      max: 30,
      min: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
      application_name: 'rgs-backend-replica-1',
    }),
    new Pool({
      host: process.env.DB_REPLICA_2_HOST,
      max: 30,
      min: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
      application_name: 'rgs-backend-replica-2',
    })
  ]
};

// Smart query router
export class DatabaseRouter {
  async query(sql: string, params?: any[]): Promise<any> {
    // Route reads to replicas, writes to master
    if (this.isReadQuery(sql)) {
      const replica = this.selectReplica();
      return replica.query(sql, params);
    } else {
      return scalableDbConfig.master.query(sql, params);
    }
  }
  
  private isReadQuery(sql: string): boolean {
    const readPatterns = /^\s*(SELECT|WITH)/i;
    return readPatterns.test(sql.trim());
  }
  
  private selectReplica(): Pool {
    // Round-robin or health-based selection
    return scalableDbConfig.replicas[
      Math.floor(Math.random() * scalableDbConfig.replicas.length)
    ];
  }
}
```

#### 1.2 Database Partitioning Strategy
```sql
-- Partition large tables by date for performance
CREATE TABLE data.game_sessions_2024_01 PARTITION OF data.game_sessions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE data.game_sessions_2024_02 PARTITION OF data.game_sessions
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- Partition audit tables by operator for compliance
CREATE TABLE audit.financial_events_operator_1 PARTITION OF audit.financial_events
FOR VALUES WITH (modulus 10, remainder 0);

CREATE TABLE audit.financial_events_operator_2 PARTITION OF audit.financial_events
FOR VALUES WITH (modulus 10, remainder 1);
```

### Phase 2: Redis Clustering (Weeks 4-6)

#### 2.1 Redis Cluster Configuration
```typescript
// Redis cluster setup
import { Cluster } from 'ioredis';

export const redisCluster = new Cluster([
  { host: 'redis-node-1', port: 7000 },
  { host: 'redis-node-2', port: 7000 },
  { host: 'redis-node-3', port: 7000 },
  { host: 'redis-node-4', port: 7000 },
  { host: 'redis-node-5', port: 7000 },
  { host: 'redis-node-6', port: 7000 },
], {
  enableOfflineQueue: false,
  redisOptions: {
    password: process.env.REDIS_PASSWORD,
  },
  scaleReads: 'slave', // Read from slaves when possible
  maxRetriesPerRequest: 3,
});

// Enhanced cache manager with clustering
export class ClusteredCacheManager extends CacheManager {
  private cluster: Cluster;
  
  constructor() {
    super();
    this.cluster = redisCluster;
  }
  
  async get(key: string): Promise<any> {
    try {
      const result = await this.cluster.get(key);
      return result ? JSON.parse(result) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null; // Graceful degradation
    }
  }
  
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      if (ttl) {
        await this.cluster.setex(key, ttl, serialized);
      } else {
        await this.cluster.set(key, serialized);
      }
    } catch (error) {
      console.error('Cache set error:', error);
      // Continue without caching rather than failing
    }
  }
}
```

#### 2.2 Cache Sharding Strategy
```typescript
// Intelligent cache key distribution
export class CacheShardingManager {
  private getShardKey(key: string): string {
    // Distribute based on key type and operator
    if (key.startsWith('session:')) {
      const sessionId = key.split(':')[1];
      return `{session:${this.getShardId(sessionId)}}:${key}`;
    }
    
    if (key.startsWith('operator:')) {
      const operatorId = key.split(':')[1];
      return `{operator:${this.getShardId(operatorId)}}:${key}`;
    }
    
    return key;
  }
  
  private getShardId(id: string): string {
    // Consistent hashing for even distribution
    const hash = this.simpleHash(id);
    return (hash % 16).toString(); // 16 shards
  }
}
```

### Phase 3: Application Scaling (Weeks 7-9)

#### 3.1 Horizontal Application Scaling
```typescript
// Load balancer configuration (nginx.conf)
upstream rgs_backend {
    least_conn;
    server rgs-backend-1:3100 weight=1 max_fails=3 fail_timeout=30s;
    server rgs-backend-2:3100 weight=1 max_fails=3 fail_timeout=30s;
    server rgs-backend-3:3100 weight=1 max_fails=3 fail_timeout=30s;
    server rgs-backend-4:3100 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    location /api/ {
        proxy_pass http://rgs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # Session affinity for stateful operations
        ip_hash;
    }
}
```

#### 3.2 Microservices Decomposition
```typescript
// Service decomposition strategy
export const serviceArchitecture = {
  // Authentication service
  authService: {
    responsibilities: ['operator authentication', 'session validation'],
    endpoints: ['/api/auth/*'],
    scaling: 'stateless horizontal'
  },
  
  // Game interaction service  
  gameService: {
    responsibilities: ['game logic', 'round management'],
    endpoints: ['/api/interact/*'],
    scaling: 'stateless horizontal'
  },
  
  // Session management service
  sessionService: {
    responsibilities: ['session lifecycle', 'user management'],
    endpoints: ['/api/session/*'],
    scaling: 'stateful with sticky sessions'
  },
  
  // Operator callback service
  callbackService: {
    responsibilities: ['operator notifications', 'transaction processing'],
    endpoints: ['/api/operator/callback/*'],
    scaling: 'queue-based processing'
  }
};
```

### Phase 4: Monitoring & Alerting (Weeks 10-12)

#### 4.1 Comprehensive Monitoring Stack
```typescript
// Performance monitoring
export class ScalabilityMonitor {
  private metrics = {
    requestsPerSecond: new Map<string, number>(),
    responseTime: new Map<string, number[]>(),
    errorRate: new Map<string, number>(),
    activeConnections: 0,
    cacheHitRate: 0,
    databaseConnections: 0
  };
  
  async recordMetrics(): Promise<void> {
    // Database connection monitoring
    const dbStats = await this.getDatabaseStats();
    this.metrics.databaseConnections = dbStats.activeConnections;
    
    // Redis cluster monitoring
    const redisStats = await this.getRedisClusterStats();
    this.metrics.cacheHitRate = redisStats.hitRate;
    
    // Application metrics
    const appStats = await this.getApplicationStats();
    this.metrics.activeConnections = appStats.activeConnections;
    
    // Alert if thresholds exceeded
    await this.checkAlertThresholds();
  }
  
  private async checkAlertThresholds(): Promise<void> {
    if (this.metrics.databaseConnections > 80) {
      await this.sendAlert('Database connection pool near capacity');
    }
    
    if (this.metrics.cacheHitRate < 0.9) {
      await this.sendAlert('Cache hit rate below 90%');
    }
    
    if (this.getAverageResponseTime() > 200) {
      await this.sendAlert('Average response time above 200ms');
    }
  }
}
```

## Service Decomposition Recommendations

### Immediate Decomposition (Phase 3)
1. **Authentication Service**: Handle all operator/session authentication
2. **Game Service**: Process game interactions and logic
3. **Callback Service**: Manage operator callbacks and notifications

### Future Decomposition (Phase 5+)
1. **Analytics Service**: Handle reporting and analytics
2. **Configuration Service**: Manage game and operator configurations  
3. **Audit Service**: Process audit logging and compliance

## Infrastructure Requirements

### Database Infrastructure
- **Master**: 8 CPU, 32GB RAM, 1TB SSD
- **Replicas**: 4 CPU, 16GB RAM, 500GB SSD (2 instances)
- **Backup**: Automated daily backups with point-in-time recovery

### Redis Infrastructure  
- **Cluster**: 6 nodes, 4 CPU, 16GB RAM each
- **Total Memory**: 96GB available cache memory
- **Persistence**: RDB snapshots + AOF for durability

### Application Infrastructure
- **Load Balancer**: 2 CPU, 4GB RAM (HA pair)
- **App Servers**: 4 CPU, 8GB RAM (4+ instances)
- **Monitoring**: 2 CPU, 8GB RAM (Prometheus + Grafana)

## Implementation Timeline

**Weeks 1-3**: Database scaling and read replicas
**Weeks 4-6**: Redis clustering and cache optimization  
**Weeks 7-9**: Application horizontal scaling
**Weeks 10-12**: Monitoring and alerting implementation
**Weeks 13-16**: Load testing and optimization
**Weeks 17-20**: Microservices decomposition (if needed)
