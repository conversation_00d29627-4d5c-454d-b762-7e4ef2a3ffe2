// redisTTL.ts
// TTL policies for Redis keys to control cache expiration and persistence

export const redisTTL = {
    // Game Config Cache
    allGames: 600, // 10 minutes
    gameByCode: 600, // 10 minutes
    gameBetLimits: 3600, // 1 hour
    gameRTP: 3600, // 1 hour
    gameEngine: 3600, // 1 hour
    gameAnimation: 3600, // 1 hour
    gameCertifications: 43200, // 12 hours
    operatorGameConfig: 600, // 10 minutes

    // Currency Config
    allCurrencies: 86400, // 24 hours
    currencyByCode: 86400, // 24 hours
    operatorCurrencyOverride: 86400, // 24 hours
    operatorCurrencyList: 86400, // 24 hours
    currencyValidationMatrix: 3600, // 1 hour (frequently accessed)

    // Operators
    allOperators: 86400, // 24 hours
    operatorById: 86400, // 24 hours
    operatorRTPSettings: 86400, // 24 hours
    operatorAuthPublicKey: 0, // persistent
    operatorCallbackURL: 86400, // 24 hours
    operatorGamePermissions: 86400, // 24 hours

    // API Keys
    apiKeys: 900, // Every 15 minutes
    apiKeyMeta: 10800, // 3 hours
    defaultApiKey: 10800, // 3 hours

    // Users
    allUsers: 86400, // 24 hours
    userById: 0, // persistent
    userBalance: 0, // persistent
    userTransactionHistory: 604800, // 7 days
    userFraudFlags: 0, // persistent
    userLastSeen: 2592000, // 30 days
    userSession: 86400, // 24 hours

    // Sessions (Enhanced)
    sessionById: 86400, // 24 hours
    sessionMeta: 86400, // 24 hours
    userActiveSessions: 86400, // 24 hours
    sessionAuthentication: 3600, // 1 hour (shorter for security)
    sessionBalance: 1800, // 30 minutes (frequent updates)
    sessionActivity: 21600, // 6 hours
    activeSessionsByGame: 3600, // 1 hour (dynamic data)
    sessionMetrics: 86400, // 24 hours
    operatorActiveSessions: 7200, // 2 hours

    // Game Rounds
    allRounds: 86400, // 24 hours
    gameRoundById: 604800, // 7 days
    userRounds: 604800, // 7 days
    operatorRounds: 604800, // 7 days

    // Enhanced Cache Keys
    gameBetLimits: 7200, // 2 hours
    gameAvailability: 1800, // 30 minutes

    // Meta / Diagnostics
    cacheVersion: 0, // persistent
    cacheLastUpdated: 0, // persistent
    cacheMetrics: 0, // persistent
    preloadErrors: 0, // persistent
};

export function getTTL(ttlKey: keyof typeof redisTTL, fallback: number = 600): number {
    const ttl = redisTTL[ttlKey];
    return typeof ttl === 'number' && ttl > 0 ? ttl : fallback;
}