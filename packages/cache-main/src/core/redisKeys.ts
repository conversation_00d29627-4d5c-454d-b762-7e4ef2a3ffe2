// redisKeys.ts
// 🎯 Centralized Redis Key Generator for CacheManager
// ⚙️ Supports fast lookups, partial overrides, and frontend API access

export const redisKeys = {
    // ================================
    // 🎮 Game Config Cache
    // ================================
    allGames: 'games:index:all', // [gameCode...]
    gameByCode: (gameCode: string) => `games:${gameCode}`,
    gameBetLimits: (gameCode: string) => `games:${gameCode}:betLimits`,
    gameRTP: (gameCode: string, rtpTier: string) => `games:${gameCode}:rtp:${rtpTier}`,
    gameConfiguration: (gameCode: string) => `games:${gameCode}:config`,
    gameAvailability: (operatorId: string, gameCode: string) => `games:availability:${operatorId}:${gameCode}`,
    gameEngine: (gameCode: string) => `games:${gameCode}:engine`,
    gameAnimation: (gameCode: string) => `games:${gameCode}:animation`,
    gameCertifications: (gameCode: string) => `games:${gameCode}:certs`,
    operatorGameConfig: (operatorId: string, gameCode: string) =>
        `operators:${operatorId}:game:${gameCode}`,

    // ================================
    // 💱 Currency Config
    // ================================
    allCurrencies: 'currencies:index:all',
    currencyByCode: (code: string) => `currencies:${code}`,
    operatorCurrencyOverride: (operatorId: string, currencyCode: string) =>
        `operators:${operatorId}:currency:${currencyCode}`,
    operatorCurrencyList: (operatorId: string) => `operators:${operatorId}:currencies`,
    currencyValidationMatrix: 'currencies:validation:matrix',

    // ================================
    // 🧑‍💼 Operators
    // ================================
    allOperators: 'operators:index:all',
    operatorById: (operatorId: string) => `operators:${operatorId}`,
    operatorRTPSettings: (operatorId: string) => `operators:${operatorId}:rtpSettings`,
    operatorAuthPublicKey: (operatorId: string) => `auth:operator:${operatorId}:pubKey`,
    operatorCallbackURL: (operatorId: string) => `operators:${operatorId}:callbackUrl`,
    operatorGamePermissions: (operatorId: string) => `operators:${operatorId}:gamePermissions`,

    // ================================
    // 🔐 API Keys
    // ================================
    apiKeys: 'auth:apiKeys',
    apiKeyMeta: (apiKey: string) => `auth:apiKey:${apiKey}`,
    defaultApiKey: () => 'auth:apiKey:default',

    // ================================
    // 👤 Users
    // ================================
    allUsers: 'users:index:all',
    userById: (userId: string) => `user:${userId}`,
    userBalance: (userId: string) => `user:${userId}:balance`,
    userTransactionHistory: (userId: string) => `user:${userId}:txHistory`,
    userFraudFlags: (userId: string) => `user:${userId}:fraudFlags`,
    userLastSeen: (userId: string) => `user:${userId}:lastSeen`,
    userSession: (userId: string) => `user:${userId}:session`,

    // ================================
    // 🧑‍💻 Sessions (Enhanced)
    // ================================
    sessionById: (sessionId: string) => `session:${sessionId}`,
    sessionMeta: (sessionId: string) => `session:${sessionId}:meta`,
    userActiveSessions: (userId: string) => `user:${userId}:sessions`,
    sessionAuthentication: (sessionId: string) => `session:${sessionId}:auth`,
    sessionBalance: (sessionId: string) => `session:${sessionId}:balance`,
    sessionActivity: (sessionId: string) => `session:${sessionId}:activity`,
    activeSessionsByGame: (gameCode: string) => `sessions:game:${gameCode}:active`,
    sessionMetrics: (operatorId: string) => `sessions:metrics:${operatorId}`,
    operatorActiveSessions: (operatorId: string) => `operator:${operatorId}:sessions:active`,

    // ================================
    // 🧾 Game Rounds
    // ================================
    allRounds: 'rounds:index:all',
    gameRoundById: (roundId: string) => `round:${roundId}`,
    userRounds: (userId: string) => `user:${userId}:rounds`,
    operatorRounds: (operatorId: string) => `operators:${operatorId}:rounds`,

    // ================================
    // 🧠 Meta / Diagnostics
    // ================================
    cacheVersion: 'meta:cacheVersion',
    cacheLastUpdated: (keyspace: string) => `meta:lastUpdated:${keyspace}`,
    cacheMetrics: 'meta:cacheMetrics',
    preloadErrors: 'meta:preloadErrors'
};

