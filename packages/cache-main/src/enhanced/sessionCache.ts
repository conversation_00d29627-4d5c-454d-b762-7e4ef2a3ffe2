/**
 * Enhanced Session Cache Management
 * Provides high-performance session caching for casino-grade operations
 */

import { redis } from '../core/redisClient';
import { redisKeys } from '../core/redisKeys';
import { redisTTL } from '../core/redisTTL';

export interface CachedSession {
  session_id: string;
  operator_id: string;
  user_id: string;
  game_code: string;
  currency: string;
  play_mode: 'real' | 'demo';
  status: 'open' | 'closed' | 'suspended';
  balance?: number;
  metadata?: any;
  created_at: string;
  last_activity: string;
}

export interface CachedOperatorDetails {
  operator_id: string;
  name: string;
  status: 'active' | 'inactive' | 'suspended';
  environment: 'production' | 'staging' | 'development';
  api_key: string;
  public_key: string;
  endpoints: Record<string, { url: string; enabled: boolean }>;
  game_permissions: string[];
  currency_settings: Record<string, any>;
  default_settings: any;
}

export class SessionCache {
  /**
   * Cache active session with optimized structure
   */
  static async cacheSession(session: CachedSession): Promise<void> {
    const sessionKey = redisKeys.sessionById(session.session_id);
    const userSessionsKey = redisKeys.userActiveSessions(session.user_id);
    const operatorSessionsKey = redisKeys.operatorRounds(session.operator_id);

    // Use pipeline for atomic operations
    const pipeline = redis.pipeline();
    
    // Cache session details
    pipeline.hset(sessionKey, {
      session_id: session.session_id,
      operator_id: session.operator_id,
      user_id: session.user_id,
      game_code: session.game_code,
      currency: session.currency,
      play_mode: session.play_mode,
      status: session.status,
      balance: session.balance || 0,
      metadata: JSON.stringify(session.metadata || {}),
      created_at: session.created_at,
      last_activity: session.last_activity
    });
    
    // Add to user's active sessions
    pipeline.sadd(userSessionsKey, session.session_id);
    
    // Add to operator's session tracking
    pipeline.sadd(operatorSessionsKey, session.session_id);
    
    // Set TTLs
    pipeline.expire(sessionKey, redisTTL.sessionById);
    pipeline.expire(userSessionsKey, redisTTL.userActiveSessions);
    pipeline.expire(operatorSessionsKey, redisTTL.operatorRounds);
    
    await pipeline.exec();
  }

  /**
   * Get cached session with fallback to database
   */
  static async getSession(sessionId: string): Promise<CachedSession | null> {
    const sessionKey = redisKeys.sessionById(sessionId);
    const sessionData = await redis.hgetall(sessionKey);
    
    if (!sessionData || !sessionData.session_id) {
      return null;
    }
    
    return {
      session_id: sessionData.session_id,
      operator_id: sessionData.operator_id,
      user_id: sessionData.user_id,
      game_code: sessionData.game_code,
      currency: sessionData.currency,
      play_mode: sessionData.play_mode as 'real' | 'demo',
      status: sessionData.status as 'open' | 'closed' | 'suspended',
      balance: parseFloat(sessionData.balance || '0'),
      metadata: sessionData.metadata ? JSON.parse(sessionData.metadata) : {},
      created_at: sessionData.created_at,
      last_activity: sessionData.last_activity
    };
  }

  /**
   * Update session activity timestamp
   */
  static async updateActivity(sessionId: string): Promise<void> {
    const sessionKey = redisKeys.sessionById(sessionId);
    await redis.hset(sessionKey, 'last_activity', new Date().toISOString());
  }

  /**
   * Close session and cleanup
   */
  static async closeSession(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) return;

    const sessionKey = redisKeys.sessionById(sessionId);
    const userSessionsKey = redisKeys.userActiveSessions(session.user_id);
    const operatorSessionsKey = redisKeys.operatorRounds(session.operator_id);

    const pipeline = redis.pipeline();
    
    // Update session status
    pipeline.hset(sessionKey, {
      status: 'closed',
      last_activity: new Date().toISOString()
    });
    
    // Remove from active sessions
    pipeline.srem(userSessionsKey, sessionId);
    pipeline.srem(operatorSessionsKey, sessionId);
    
    // Reduce TTL for closed sessions
    pipeline.expire(sessionKey, 3600); // 1 hour for closed sessions
    
    await pipeline.exec();
  }

  /**
   * Get user's active sessions
   */
  static async getUserActiveSessions(userId: string): Promise<string[]> {
    const userSessionsKey = redisKeys.userActiveSessions(userId);
    return await redis.smembers(userSessionsKey);
  }

  /**
   * Bulk session validation for operator
   */
  static async validateOperatorSessions(operatorId: string): Promise<{
    active: number;
    total: number;
    sessions: string[];
  }> {
    const operatorSessionsKey = redisKeys.operatorRounds(operatorId);
    const sessionIds = await redis.smembers(operatorSessionsKey);
    
    let activeCount = 0;
    const activeSessions: string[] = [];
    
    // Use pipeline for efficient bulk checking
    const pipeline = redis.pipeline();
    sessionIds.forEach(sessionId => {
      pipeline.hget(redisKeys.sessionById(sessionId), 'status');
    });
    
    const results = await pipeline.exec();
    
    results?.forEach((result, index) => {
      if (result && result[1] === 'open') {
        activeCount++;
        activeSessions.push(sessionIds[index]);
      }
    });
    
    return {
      active: activeCount,
      total: sessionIds.length,
      sessions: activeSessions
    };
  }
}

/**
 * Enhanced Operator Cache Management
 */
export class OperatorCache {
  /**
   * Cache complete operator details with all related data
   */
  static async cacheOperatorDetails(operator: CachedOperatorDetails): Promise<void> {
    const operatorKey = redisKeys.operatorById(operator.operator_id);
    const gamePermissionsKey = redisKeys.operatorGamePermissions(operator.operator_id);
    
    const pipeline = redis.pipeline();
    
    // Cache operator details
    pipeline.hset(operatorKey, {
      operator_id: operator.operator_id,
      name: operator.name,
      status: operator.status,
      environment: operator.environment,
      api_key: operator.api_key,
      public_key: operator.public_key,
      endpoints: JSON.stringify(operator.endpoints),
      currency_settings: JSON.stringify(operator.currency_settings),
      default_settings: JSON.stringify(operator.default_settings)
    });
    
    // Cache game permissions as set
    if (operator.game_permissions.length > 0) {
      pipeline.sadd(gamePermissionsKey, ...operator.game_permissions);
    }
    
    // Set TTLs
    pipeline.expire(operatorKey, redisTTL.operatorById);
    pipeline.expire(gamePermissionsKey, redisTTL.operatorGamePermissions);
    
    await pipeline.exec();
  }

  /**
   * Get cached operator details
   */
  static async getOperatorDetails(operatorId: string): Promise<CachedOperatorDetails | null> {
    const operatorKey = redisKeys.operatorById(operatorId);
    const gamePermissionsKey = redisKeys.operatorGamePermissions(operatorId);
    
    const [operatorData, gamePermissions] = await Promise.all([
      redis.hgetall(operatorKey),
      redis.smembers(gamePermissionsKey)
    ]);
    
    if (!operatorData || !operatorData.operator_id) {
      return null;
    }
    
    return {
      operator_id: operatorData.operator_id,
      name: operatorData.name,
      status: operatorData.status as 'active' | 'inactive' | 'suspended',
      environment: operatorData.environment as 'production' | 'staging' | 'development',
      api_key: operatorData.api_key,
      public_key: operatorData.public_key,
      endpoints: operatorData.endpoints ? JSON.parse(operatorData.endpoints) : {},
      game_permissions: gamePermissions,
      currency_settings: operatorData.currency_settings ? JSON.parse(operatorData.currency_settings) : {},
      default_settings: operatorData.default_settings ? JSON.parse(operatorData.default_settings) : {}
    };
  }

  /**
   * Check if operator supports specific game
   */
  static async isGameSupported(operatorId: string, gameCode: string): Promise<boolean> {
    const gamePermissionsKey = redisKeys.operatorGamePermissions(operatorId);
    return await redis.sismember(gamePermissionsKey, gameCode) === 1;
  }

  /**
   * Get operator's supported games
   */
  static async getSupportedGames(operatorId: string): Promise<string[]> {
    const gamePermissionsKey = redisKeys.operatorGamePermissions(operatorId);
    return await redis.smembers(gamePermissionsKey);
  }
}
