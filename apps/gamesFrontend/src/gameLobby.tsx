import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { discoverAvailableGames, GameInfo } from './utils/gameDiscovery';
import { LobbySessionManager } from './services/lobbySessionManager';

const GameLobby = () => {
    const [games, setGames] = useState<GameInfo[]>([]);
    const [loading, setLoading] = useState(true);
    const [sessionAuthenticated, setSessionAuthenticated] = useState(false);
    const [sessionData, setSessionData] = useState<any>(null);
    const [operatorGames, setOperatorGames] = useState<string[]>([]);

    const lobbySessionManager = LobbySessionManager.getInstance();

    useEffect(() => {
        const initializeLobby = async () => {
            try {
                // Initialize session and authenticate
                const authenticated = await lobbySessionManager.authenticateSession();
                setSessionAuthenticated(authenticated);
                setSessionData(lobbySessionManager.getSessionData());

                // Load operator-specific games
                const allowedGames = await lobbySessionManager.loadOperatorGames();
                setOperatorGames(allowedGames);

                // Discover available games
                const availableGames = await discoverAvailableGames();

                // Filter games based on operator permissions
                const filteredGames = availableGames.filter(game =>
                    allowedGames.includes(game.id)
                );

                setGames(filteredGames);
            } catch (error) {
                console.error('Failed to initialize lobby:', error);
            } finally {
                setLoading(false);
            }
        };

        initializeLobby();
    }, []);

    const handleEnterGame = (gameId: string) => {
        try {
            // Generate game launch URL with session context
            const launchUrl = lobbySessionManager.generateGameLaunchUrl(gameId);
            console.log(`🚀 Launching game ${gameId} with session context:`, launchUrl);
            window.location.href = launchUrl;
        } catch (error) {
            console.error(`❌ Failed to launch game ${gameId}:`, error);
            // Fallback to basic game launch
            window.location.href = `/${gameId}.html`;
        }
    };

    return (
        <div
            style={{
                width: '100vw',
                height: '100vh',
                backgroundColor: '#111',
                color: '#fff',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '30px',
                fontFamily: 'sans-serif',
            }}
        >
            <h1 style={{ fontSize: '2.5rem' }}>🎮 Insolence Games Lobby</h1>

            {/* Session Status Display */}
            {sessionData && (
                <div style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    padding: '15px',
                    borderRadius: '10px',
                    marginBottom: '20px',
                    fontSize: '0.9rem',
                    color: '#333',
                    border: '1px solid #ddd'
                }}>
                    <div>🎯 Operator: {sessionData.operator_id}</div>
                    <div>💰 Currency: {sessionData.currency}</div>
                    <div>🎮 Mode: {sessionData.play_mode}</div>
                    {sessionData.balance && <div>💵 Balance: {sessionData.balance}</div>}
                    <div>🔐 Status: {sessionAuthenticated ? '✅ Authenticated' : '⚠️ Not Authenticated'}</div>
                </div>
            )}

            {loading ? (
                <div style={{ color: '#aaa', fontSize: '1.2rem' }}>
                    🔍 Discovering available games...
                </div>
            ) : games.length === 0 ? (
                <div style={{ color: '#ff4444', fontSize: '1.2rem' }}>
                    ❌ No games available
                </div>
            ) : (
                games.map((game) => (
                    <button
                        key={game.id}
                        onClick={() => handleEnterGame(game.id)}
                        style={{
                            padding: '20px 40px',
                            fontSize: '1.3rem',
                            borderRadius: '30px',
                            backgroundColor: game.color,
                            color: '#fff',
                            border: 'none',
                            cursor: 'pointer',
                            boxShadow: `0 4px 8px ${game.color}33`,
                            transition: 'all 0.2s ease',
                            position: 'relative',
                            overflow: 'hidden',
                        }}
                        onMouseOver={(e) => {
                            const lighterColor = game.color + '33';
                            e.currentTarget.style.backgroundColor = lighterColor.replace('33', '66');
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = `0 6px 12px ${game.color}66`;
                        }}
                        onMouseOut={(e) => {
                            e.currentTarget.style.backgroundColor = game.color;
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = `0 4px 8px ${game.color}33`;
                        }}
                    >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <span>{game.icon} {game.name}</span>
                            {game.category && (
                                <span style={{
                                    background: game.category === '3D' ? '#4CAF50' : '#2196F3',
                                    color: '#fff',
                                    fontSize: '0.7rem',
                                    padding: '2px 6px',
                                    borderRadius: '8px',
                                    fontWeight: 'bold'
                                }}>
                                    {game.category}
                                </span>
                            )}
                        </div>

                        {game.technology && (
                            <div style={{
                                fontSize: '0.8rem',
                                opacity: 0.7,
                                marginTop: '2px'
                            }}>
                                {game.technology}
                            </div>
                        )}

                        {game.isNew && (
                            <span style={{
                                position: 'absolute',
                                top: '5px',
                                right: '10px',
                                background: '#ff4444',
                                color: '#fff',
                                fontSize: '0.7rem',
                                padding: '2px 6px',
                                borderRadius: '10px',
                                fontWeight: 'bold'
                            }}>
                                NEW
                            </span>
                        )}
                        {game.version && (
                            <span style={{
                                position: 'absolute',
                                bottom: '5px',
                                right: '10px',
                                fontSize: '0.8rem',
                                opacity: 0.6
                            }}>
                                v{game.version}
                            </span>
                        )}
                    </button>
                ))
            )}

            <div style={{
                fontSize: '0.9rem',
                color: '#aaa',
                textAlign: 'center',
                maxWidth: '600px',
                lineHeight: '1.4'
            }}>
                ✨ Dynamic multi-game architecture with automatic game discovery and true independence
                {games.length > 0 && (
                    <div style={{ marginTop: '10px', fontSize: '0.8rem' }}>
                        Found {games.length} available game{games.length !== 1 ? 's' : ''}
                    </div>
                )}
            </div>

            <button
                onClick={() => window.location.href = '/ins_lootbox.html'}
                style={{
                    padding: '12px 24px',
                    fontSize: '1rem',
                    borderRadius: '20px',
                    backgroundColor: '#666',
                    color: '#fff',
                    border: 'none',
                    cursor: 'pointer',
                    opacity: 0.7,
                }}
            >
                🚪 Legacy Lootbox Game (v1.x)
            </button>
        </div>
    );
};

export default GameLobby;