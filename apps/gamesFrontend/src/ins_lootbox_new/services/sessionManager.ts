/**
 * Session Manager for ins_lootbox_base
 * Handles URL parameter parsing and casino-grade session management
 */

export interface GameLaunchParams {
  game_code: string;
  session_id?: string;
  user_id?: string;
  operator_id?: string;
  currency?: string;
  play_mode: 'real' | 'demo';
  game_mode?: 'desktop' | 'mobile';
  language?: string;
  lobby_url?: string;
  bet_amount?: number;
}

export interface SessionData {
  session_id: string;
  user_id: string;
  operator_id: string;
  currency: string;
  play_mode: 'real' | 'demo';
  game_mode: 'desktop' | 'mobile';
  language: string;
  lobby_url?: string;
  created_at: string;
  is_from_url: boolean;
  authenticated?: boolean;
  balance?: number;
  player_details?: any;
}

class SessionManager {
  private static instance: SessionManager;
  private sessionData: SessionData | null = null;
  private readonly STORAGE_KEY = 'ins_lootbox_new_session_v1';

  private constructor() {
    this.initializeSession();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Initialize session from URL parameters or create new demo session
   */
  private initializeSession(): void {
    console.log('🔧 Initializing lootbox session manager...');
    
    // Clear any old format session data
    this.clearOldSessionData();
    
    // Parse URL parameters
    const urlParams = this.parseURLParameters();
    
    if (urlParams && this.isValidLaunchParams(urlParams)) {
      console.log('✅ Valid launch parameters found in URL:', urlParams);
      this.sessionData = this.createSessionFromURL(urlParams);
    } else {
      console.log('🎮 No valid URL parameters, creating demo session');
      this.sessionData = this.createDemoSession();
    }

    // Save to storage
    this.saveSession();
    
    console.log('✅ Lootbox session initialized:', this.sessionData);
  }

  /**
   * Clear old format session data to prevent UUID validation errors
   */
  private clearOldSessionData(): void {
    const oldKeys = [
      'lootbox_session_id',
      'lootbox_user_id', 
      'ins_lootbox_session',
      'ins_lootbox_base_session'
    ];
    
    oldKeys.forEach(key => {
      if (sessionStorage.getItem(key)) {
        console.log(`🧹 Clearing old session data: ${key}`);
        sessionStorage.removeItem(key);
      }
      if (localStorage.getItem(key)) {
        console.log(`🧹 Clearing old local storage: ${key}`);
        localStorage.removeItem(key);
      }
    });
  }

  /**
   * Parse URL parameters for game launch
   */
  private parseURLParameters(): GameLaunchParams | null {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      
      if (urlParams.size === 0) {
        return null;
      }

      const params: Partial<GameLaunchParams> = {};
      
      // Required parameters
      params.game_code = urlParams.get('game_code') || 'ins_lootbox';
      params.play_mode = (urlParams.get('play_mode') as 'real' | 'demo') || 'demo';
      
      // Optional parameters
      if (urlParams.get('session_id')) params.session_id = urlParams.get('session_id')!;
      if (urlParams.get('user_id')) params.user_id = urlParams.get('user_id')!;
      if (urlParams.get('operator_id')) params.operator_id = urlParams.get('operator_id')!;
      if (urlParams.get('currency')) params.currency = urlParams.get('currency')!;
      if (urlParams.get('game_mode')) params.game_mode = urlParams.get('game_mode') as 'desktop' | 'mobile';
      if (urlParams.get('language')) params.language = urlParams.get('language')!;
      if (urlParams.get('lobby_url')) params.lobby_url = urlParams.get('lobby_url')!;
      if (urlParams.get('bet_amount')) params.bet_amount = parseFloat(urlParams.get('bet_amount')!);

      return params as GameLaunchParams;
    } catch (error) {
      console.error('❌ Error parsing URL parameters:', error);
      return null;
    }
  }

  /**
   * Validate launch parameters
   */
  private isValidLaunchParams(params: GameLaunchParams): boolean {
    // Validate required fields
    if (!params.game_code || !params.play_mode) {
      return false;
    }

    // Validate play_mode
    if (!['real', 'demo'].includes(params.play_mode)) {
      return false;
    }

    // Validate UUIDs if provided
    if (params.session_id && !this.isValidUUID(params.session_id)) {
      console.error('❌ Invalid session_id UUID format:', params.session_id);
      return false;
    }

    if (params.user_id && !this.isValidUUID(params.user_id)) {
      console.error('❌ Invalid user_id UUID format:', params.user_id);
      return false;
    }

    return true;
  }

  /**
   * Create session from URL parameters
   */
  private createSessionFromURL(params: GameLaunchParams): SessionData {
    return {
      session_id: params.session_id || this.generateUUID(),
      user_id: params.user_id || this.generateUUID(),
      operator_id: params.operator_id || 'demo_operator',
      currency: params.currency || 'USD',
      play_mode: params.play_mode,
      game_mode: params.game_mode || 'desktop',
      language: params.language || 'en',
      lobby_url: params.lobby_url,
      created_at: new Date().toISOString(),
      is_from_url: true
    };
  }

  /**
   * Create demo session with defaults
   */
  private createDemoSession(): SessionData {
    return {
      session_id: this.generateUUID(),
      user_id: this.generateUUID(),
      operator_id: 'demo_operator',
      currency: 'USD',
      play_mode: 'demo',
      game_mode: 'desktop',
      language: 'en',
      created_at: new Date().toISOString(),
      is_from_url: false
    };
  }

  /**
   * Generate proper UUID v4
   */
  private generateUUID(): string {
    // Use crypto.randomUUID() if available (modern browsers)
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    
    // Fallback UUID v4 generation for older browsers
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Validate UUID format
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Save session to storage
   */
  private saveSession(): void {
    if (this.sessionData) {
      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.sessionData));
    }
  }

  /**
   * Load session from storage
   */
  private loadSession(): SessionData | null {
    try {
      const stored = sessionStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('❌ Error loading session from storage:', error);
    }
    return null;
  }

  /**
   * Get current session data
   */
  public getSession(): SessionData {
    if (!this.sessionData) {
      // Try to load from storage first
      this.sessionData = this.loadSession();
      if (!this.sessionData) {
        // Create new session if none exists
        this.initializeSession();
      }
    }
    return this.sessionData!;
  }

  /**
   * Get session for API calls
   */
  public getAPISessionData(): {
    session_id: string;
    user_id: string;
    operator_id: string;
    currency: string;
    play_mode: 'real' | 'demo';
  } {
    const session = this.getSession();
    return {
      session_id: session.session_id,
      user_id: session.user_id,
      operator_id: session.operator_id,
      currency: session.currency,
      play_mode: session.play_mode
    };
  }

  /**
   * Check if session is authenticated
   */
  public isAuthenticated(): boolean {
    const session = this.getSession();
    return session.authenticated === true;
  }

  /**
   * Get player balance
   */
  public getBalance(): number {
    const session = this.getSession();
    return session.balance || 0;
  }
}

export const sessionManager = SessionManager.getInstance();
