/**
 * Lootbox interaction helpers
 * Handles API communication for lootbox gameplay
 */

import { API_ENDPOINTS } from '../config/api.config';

export type PlayLootboxInput = {
    gameCode: string;
    action: 'play';
    sessionId: string;
    userId: string;
    operatorId: string;
    currency: string;
    playMode: 'real' | 'demo';
    meta?: Record<string, any>;
};

/**
 * Rarity levels for lootbox prizes (matches backend)
 */
export type RarityLevel = 'common' | 'uncommon' | 'rare' | 'legendary' | 'epic';

/**
 * Animation hints from backend for rarity-based effects
 */
export interface AnimationHints {
    rarityEffects: string[];
    duration: number;
}

/**
 * Enhanced lootbox result with rarity and animation metadata
 */
export type LootboxResult = {
    type: string;
    prize: string;
    rarity: RarityLevel;
    seed: string;
    clientSeed?: string;
    nonce?: number;
    timestamp: number;
    metadata?: {
        animationHints?: AnimationHints;
    };
};

/**
 * Play a lootbox and get the result
 * Uses BASE_API_URL + '/interact/play' for the API request
 */
export async function playLootbox(input: PlayLootboxInput): Promise<LootboxResult> {
    const res = await fetch(API_ENDPOINTS.INTERACT_PLAY, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            game_code: input.gameCode,
            action: input.action,
            session_id: input.sessionId,
            user_id: input.userId,
            operator_id: input.operatorId,
            currency: input.currency,
            play_mode: input.playMode,
            meta: input.meta ?? {}
        }),
    });

    const json = await res.json();

    if (!res.ok || json?.success === false) {
        const errorMessage = json?.payload?.message || json?.message || 'Failed to play lootbox';
        throw new Error(errorMessage);
    }

    return json.data || json.payload;
}