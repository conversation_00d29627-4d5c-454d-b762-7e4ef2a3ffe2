import React, { useEffect, useRef, useState, useCallback } from 'react';
import { LootboxFactory, LootboxFactoryConfig, LootboxInstance } from './core/LootboxFactory';
import { playLootbox, type LootboxResult, type RarityLevel } from './helpers/interactions';
import { DEFAULT_CONFIG, createConfigPreset } from './config';
import { RarityDisplay } from './components/RarityDisplay';
import { TransformationControls } from './components/TransformationControls';
import { initializeGlobalDebugger } from './debug/TransformationDebugger';
import './debug/SystemValidator'; // Initialize global validator
import './debug/QuickTest'; // Initialize quick test functions
import './debug/ValidationScript'; // Initialize validation functions
import './debug/AssetDebugger'; // Initialize asset debugging functions
import './debug/AssetLoadingTest'; // Initialize asset loading tests
import './debug/DebuggerValidation'; // Initialize debugger validation
import './debug/MultiGameAssetTester'; // Initialize multi-game asset testing

/**
 * Props for the refactored LootboxScene component
 */
export interface LootboxSceneProps {
  /** Custom configuration overrides */
  config?: Record<string, any>;
  /** Lootbox variant to use */
  variant?: string;
  /** Enable debug mode */
  debug?: boolean;
  /** Performance preset */
  preset?: 'performance' | 'quality' | 'mobile';
  /** Custom CSS styles for container */
  style?: React.CSSProperties;
  /** Custom CSS class for container */
  className?: string;
  /** Callback when lootbox is opened */
  onLootboxOpened?: (result: any) => void;
  /** Callback when error occurs */
  onError?: (error: Error) => void;
  /** Callback when factory is ready */
  onReady?: () => void;
}

/**
 * Refactored LootboxScene component using the new modular architecture
 */
const LootboxScene: React.FC<LootboxSceneProps> = ({
  config: customConfig,
  variant = 'common',
  debug = false, // CRITICAL FIX: Keep debug disabled by default to reduce excessive logging
  preset,
  style,
  className,
  onLootboxOpened,
  onError,
  onReady,
}) => {
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const factoryRef = useRef<LootboxFactory | null>(null);
  const currentLootboxRef = useRef<LootboxInstance | null>(null);
  
  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [gameResult, setGameResult] = useState('');
  const [currentResult, setCurrentResult] = useState<LootboxResult | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [fps, setFPS] = useState(0);

  // Transformation state
  const [isTransforming, setIsTransforming] = useState(false);
  const [transformationProgress, setTransformationProgress] = useState(0);
  const [targetRarity, setTargetRarity] = useState<RarityLevel | undefined>();
  
  // Animation state
  const isAnimatingRef = useRef(false);
  
  // ===== INITIALIZATION =====
  useEffect(() => {
    // CRITICAL FIX: Enhanced double initialization prevention
    if (!containerRef.current || factoryRef.current || isInitialized) {
      console.log('🛑 Preventing duplicate factory initialization', {
        hasContainer: !!containerRef.current,
        hasFactory: !!factoryRef.current,
        isInitialized
      });
      return;
    }

    const initializeFactory = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🚀 Starting factory initialization...');

        // Merge configurations
        let finalConfig = { ...DEFAULT_CONFIG };

        // Apply preset if specified
        if (preset) {
          const presetConfig = createConfigPreset(preset);
          finalConfig = { ...finalConfig, ...presetConfig };
        }

        // Apply custom config
        if (customConfig) {
          finalConfig = { ...finalConfig, ...customConfig };
        }

        // Create factory configuration
        const factoryConfig: LootboxFactoryConfig = {
          container: containerRef.current!,
          config: finalConfig,
          debug,
          enablePerformanceMonitoring: true,
        };

        // Create and initialize factory
        const factory = new LootboxFactory(factoryConfig);
        factoryRef.current = factory;

        // Setup event listeners BEFORE initialization
        factory.on('ready', handleFactoryReady);
        factory.on('error', handleFactoryError);
        factory.on('performanceChanged', handlePerformanceChanged);
        factory.on('lootboxCreated', handleLootboxCreated);

        console.log('🔧 Initializing factory...');
        console.log('🔍 DEBUG: About to call factory.initialize()');
        // Initialize factory
        await factory.initialize();
        console.log('🔍 DEBUG: factory.initialize() completed successfully');

        console.log('▶️ Starting factory...');
        console.log('🔍 DEBUG: About to call factory.start()');
        // Start factory
        factory.start();
        console.log('🔍 DEBUG: factory.start() completed successfully');

        console.log('✅ Factory initialization complete');
        console.log('🔍 DEBUG: Factory initialization sequence finished');

      } catch (err) {
        const error = err as Error;
        console.error('❌ Factory initialization failed:', error);
        setError(error);
        setIsLoading(false);
        onError?.(error);
      }
    };

    initializeFactory();

    // Cleanup on unmount
    return () => {
      if (factoryRef.current) {
        console.log('🧹 Cleaning up factory...');
        factoryRef.current.shutdown();
        factoryRef.current = null;
      }
    };
  }, []); // Remove dependencies to prevent re-initialization
  
  // ===== EVENT HANDLERS =====
  const handleFactoryReady = useCallback(() => {
    console.log('🎮 [MAIN SCENE] Lootbox factory ready');

    try {
      // Initialize global debugger for transformation testing
      if (factoryRef.current) {
        console.log('🔧 [MAIN SCENE] Checking factory scene access...');

        // Check if getScene method exists
        if (typeof factoryRef.current.getScene === 'function') {
          const scene = factoryRef.current.getScene();
          if (scene) {
            console.log('✅ [MAIN SCENE] Scene access confirmed, initializing debugger...');
            initializeGlobalDebugger(scene);
            console.log('🔧 [DEBUG] Global transformation debugger initialized');
            console.log('🔧 [DEBUG] Available debug commands:');
            console.log('  - window.lootboxDebugger.testRarityTransformation("epic")');
            console.log('  - window.lootboxDebugger.testAllRarities()');
            console.log('  - window.lootboxDebugger.testAssetLoading()');
            console.log('  - window.lootboxDebugger.validateSystem()');
            console.log('  - window.validateLootboxSystem()');
          } else {
            console.error('❌ [MAIN SCENE] Scene is null or undefined');
          }
        } else {
          console.error('❌ [MAIN SCENE] getScene method not found on factory');
          console.log('🔍 [MAIN SCENE] Available factory methods:', Object.getOwnPropertyNames(factoryRef.current));
        }
      } else {
        console.error('❌ [MAIN SCENE] Factory reference is null');
      }
    } catch (error) {
      console.error('❌ [MAIN SCENE] Error during debugger initialization:', error);
    }

    setIsInitialized(true);
    setIsLoading(false);
    onReady?.();

    // Spawn initial lootbox after a short delay to ensure everything is ready
    console.log('🔍 DEBUG: Setting timeout for initial lootbox spawn');
    setTimeout(() => {
      console.log('🔍 DEBUG: Timeout triggered, calling spawnInitialLootbox');
      spawnInitialLootbox();
    }, 100);
  }, [onReady]);
  
  const handleFactoryError = useCallback((error: Error) => {
    setError(error);
    onError?.(error);
  }, [onError]);
  
  const handlePerformanceChanged = useCallback((newFPS: number) => {
    setFPS(newFPS);
  }, []);
  
  const handleLootboxCreated = useCallback((instance: LootboxInstance) => {
    console.log('🔍 DEBUG: handleLootboxCreated called with instance:', instance.id);
    console.log('🔍 DEBUG: Instance object:', !!instance.object);
    console.log('🔍 DEBUG: Instance object children count:', instance.object?.children?.length || 0);

    currentLootboxRef.current = instance;

    // For initial lootbox: Auto-play entry animation only - keep lid closed until user clicks
    // For subsequent lootboxes: This will be handled in handlePlayClick
    if (!isAnimatingRef.current) {
      console.log('🎬 Playing entry animation for initial lootbox');
      console.log('🔍 DEBUG: About to call instance.playEntry()');
      instance.playEntry().then(() => {
        console.log('🔍 DEBUG: Entry animation completed successfully');
      }).catch((error) => {
        console.error('🔍 DEBUG: Entry animation failed:', error);
      });
    }
  }, []);
  
  // ===== LOOTBOX MANAGEMENT =====
  const spawnInitialLootbox = useCallback(async () => {
    console.log('🔍 DEBUG: spawnInitialLootbox called');
    console.log('🔍 DEBUG: factoryRef.current =', !!factoryRef.current);

    if (!factoryRef.current) {
      console.error('❌ Factory not available for lootbox spawning');
      console.log('🔍 DEBUG: Factory is null, cannot spawn lootbox');
      return;
    }

    try {
      console.log('🎁 Spawning initial lootbox...');
      console.log('🔍 DEBUG: Calling factory.createLootbox with variant:', variant || 'common');

      const instance = await factoryRef.current.createLootbox({
        variant: variant || 'common',
        autoStart: true,
      });

      console.log('✅ Initial lootbox spawned:', instance.id);
      console.log('🔍 DEBUG: Instance created successfully, ID:', instance.id);
    } catch (error) {
      console.error('❌ Failed to spawn initial lootbox:', error);
      console.log('🔍 DEBUG: Error details:', error);
    }
  }, []);
  
  const handlePlayClick = useCallback(async () => {
    console.log('🎯 Play button clicked');
    console.log('🔍 DEBUG: Button click handler triggered');
    console.log('🔍 DEBUG: isAnimatingRef.current =', isAnimatingRef.current);
    console.log('🔍 DEBUG: factoryRef.current =', !!factoryRef.current);
    console.log('🔍 DEBUG: currentLootboxRef.current =', !!currentLootboxRef.current);

    // Prevent multiple clicks during animation
    if (isAnimatingRef.current) {
      console.log('⏳ Animation already in progress, ignoring click');
      return;
    }

    if (!factoryRef.current) {
      console.error('❌ Factory not available');
      return;
    }

    if (!currentLootboxRef.current) {
      console.error('❌ No lootbox instance available');
      return;
    }

    try {
      isAnimatingRef.current = true;
      setGameResult(''); // Clear previous result
      setCurrentResult(null); // Clear previous result data
      setIsTransforming(false); // Reset transformation state
      setTransformationProgress(0);
      setTargetRarity(undefined);

      // Import session manager at the top of the file
      const { sessionManager } = await import('./services/sessionManager');
      const sessionData = sessionManager.getAPISessionData();

      const result = await playLootbox({
        gameCode: 'ins_lootbox',
        action: 'play',
        sessionId: sessionData.session_id,
        userId: sessionData.user_id,
        operatorId: sessionData.operator_id,
        currency: sessionData.currency,
        playMode: sessionData.play_mode,
        meta: {
          platform: 'GPL_DESKTOP',
        },
      });

      await currentLootboxRef.current.playExit();
      factoryRef.current.destroyLootbox(currentLootboxRef.current.id);
      currentLootboxRef.current = null;

      const newInstance = await factoryRef.current.createLootbox({
        variant: 'common',
        autoStart: false,
        rarity: 'common',
      });

      currentLootboxRef.current = newInstance;

      try {
        const animationController = currentLootboxRef.current.controllerManager?.getController('AnimationController') as any;
        if (animationController && currentLootboxRef.current) {
          const modelController = currentLootboxRef.current.controllerManager?.getController('ModelController') as any;
          const lid = modelController?.getLootboxLid();

          if (lid) {
            setTargetRarity(result.rarity);
            setIsTransforming(result.rarity !== 'common');

            let progressInterval: NodeJS.Timeout | null = null;
            if (result.rarity !== 'common') {
              progressInterval = setInterval(() => {
                if (currentLootboxRef.current) {
                  const currentObject = animationController.getCurrentObjectReference(currentLootboxRef.current.object);
                  const progress = animationController.getTransformationProgress(currentObject);
                  setTransformationProgress(progress);

                  if (progress >= 1) {
                    setIsTransforming(false);
                    if (progressInterval) {
                      clearInterval(progressInterval);
                    }
                  }
                }
              }, 50);
            }

            // CRITICAL: Use universal animation sequence for ALL rarity levels
            const finalObject = await animationController.animateCompleteEntryWithTransformation(
              currentLootboxRef.current.getCurrentObject(),
              lid,
              result.rarity,
              {
                skippable: true,
                animationHints: result.metadata?.animationHints,
                onSkip: () => {
                  setIsTransforming(false);
                  setTransformationProgress(1);
                  if (progressInterval) {
                    clearInterval(progressInterval);
                  }
                }
              }
            );

            // VALIDATION: Verify object reference synchronization worked correctly
            const currentObject = currentLootboxRef.current.getCurrentObject();
            if (finalObject !== currentObject && result.rarity !== 'common') {
              console.warn('Object reference synchronization may have failed', {
                rarity: result.rarity,
                finalObjectId: finalObject.uuid,
                currentObjectId: currentObject.uuid
              });
            }

            if (progressInterval) {
              clearInterval(progressInterval);
            }
            setIsTransforming(false);

            if (result.rarity && animationController) {
              try {
                await animationController.applyRarityAnimation(
                  finalObject,
                  result.rarity,
                  result.metadata?.animationHints
                );
              } catch (error) {
                console.error('Error applying final rarity effects:', error);
              }
            }

            const prizeText = result?.prize || 'No prize returned';
            setCurrentResult(result);
            setGameResult(prizeText);

            onLootboxOpened?.(result);
          } else {
            console.error('Could not find lid component');
          }
        } else {
          console.error('Could not access animation controller');
        }

      } catch (error) {
        console.error('Error in animation sequence:', error);
      } finally {
        isAnimatingRef.current = false;
      }

    } catch (err) {
      console.error('❌ Failed during lootbox sequence:', err);
      setGameResult('Error retrieving prize');
      isAnimatingRef.current = false;
    }
  }, [variant, onLootboxOpened]);

  // ===== RENDER =====
  if (error) {
    return (
      <div
        style={{
          width: '100vw',
          height: '100vh',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#000',
          color: '#fff',
          flexDirection: 'column',
          ...style,
        }}
        className={className}
      >
        <h2>Error Loading Lootbox</h2>
        <p>{error.message}</p>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '10px 20px',
            fontSize: '16px',
            backgroundColor: '#ff6600',
            color: '#fff',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            marginTop: '20px',
          }}
        >
          Reload
        </button>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div
        style={{
          width: '100vw',
          height: '100vh',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#000',
          color: '#fff',
          ...style,
        }}
        className={className}
      >
        <div>
          <h2>Loading Lootbox...</h2>
          <div
            style={{
              width: '200px',
              height: '4px',
              backgroundColor: '#333',
              borderRadius: '2px',
              overflow: 'hidden',
              marginTop: '20px',
            }}
          >
            <div
              style={{
                width: '100%',
                height: '100%',
                backgroundColor: '#ff6600',
                animation: 'loading 2s infinite',
              }}
            />
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div
      style={{
        width: '100vw',
        height: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#000',
        position: 'relative',
        ...style,
      }}
      className={className}
    >
      {/* 3D Scene Container */}
      <div
        ref={containerRef}
        style={{
          position: 'relative',
          width: '100%',
          maxWidth: '100%',
          aspectRatio: '16 / 9',
        }}
      />
      
      {/* UI Overlay */}
      {isInitialized && (
        <>
          {/* Play Button */}
          <button
            onClick={handlePlayClick}
            disabled={isAnimatingRef.current}
            style={{
              position: 'absolute',
              bottom: '40px',
              left: '50%',
              transform: 'translateX(-50%)',
              padding: '16px 32px',
              fontSize: '22px',
              fontWeight: 'bold',
              backgroundColor: isAnimatingRef.current ? '#666' : '#ff6600',
              color: '#ffffff',
              borderRadius: '30px',
              border: '3px solid #fff',
              cursor: isAnimatingRef.current ? 'not-allowed' : 'pointer',
              zIndex: 10,
              transition: 'all 0.3s ease',
              boxShadow: isAnimatingRef.current ? 'none' : '0 4px 15px rgba(255, 102, 0, 0.4)',
              textShadow: '0 1px 2px rgba(0,0,0,0.5)',
            }}
            onMouseEnter={(e) => {
              if (!isAnimatingRef.current) {
                e.currentTarget.style.backgroundColor = '#ff8833';
                e.currentTarget.style.transform = 'translateX(-50%) translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 6px 20px rgba(255, 102, 0, 0.6)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isAnimatingRef.current) {
                e.currentTarget.style.backgroundColor = '#ff6600';
                e.currentTarget.style.transform = 'translateX(-50%) translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 102, 0, 0.4)';
              }
            }}
          >
            {isAnimatingRef.current ? '...forging' : 'Open Lootbox'}
          </button>
          
          {/* Transformation Controls */}
          <TransformationControls
            isTransforming={isTransforming}
            targetRarity={targetRarity}
            progress={transformationProgress}
            canSkip={true}
            onSkip={() => {
              if (currentLootboxRef.current) {
                const animationController = currentLootboxRef.current.controllerManager?.getController('AnimationController') as any;
                if (animationController) {
                  animationController.skipRarityTransformation(currentLootboxRef.current.object);
                }
              }
            }}
            showProgress={true}
            showRarityPreview={true}
          />

          {/* Result Display */}
          {gameResult && currentResult && (
            <div
              style={{
                position: 'absolute',
                bottom: '100px',
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 10,
                maxWidth: '400px',
                width: '100%',
                padding: '0 20px',
              }}
            >
              <RarityDisplay
                rarity={currentResult.rarity}
                prize={gameResult}
                showBadge={true}
                showGlow={true}
                animationDuration={800}
              />
            </div>
          )}
          
          {/* Debug Info */}
          {debug && (
            <div
              style={{
                position: 'absolute',
                top: '10px',
                left: '10px',
                color: '#fff',
                backgroundColor: 'rgba(0,0,0,0.7)',
                padding: '10px',
                borderRadius: '5px',
                fontSize: '12px',
                fontFamily: 'monospace',
                zIndex: 10,
              }}
            >
              <div>FPS: {fps}</div>
              <div>Variant: {variant}</div>
              <div>Preset: {preset || 'none'}</div>
              <div>Animating: {isAnimatingRef.current ? 'Yes' : 'No'}</div>
            </div>
          )}
        </>
      )}
      
      {/* Loading Animation Styles */}
      <style>{`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(0%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
};

export default LootboxScene;
