/**
 * Lobby Session Manager
 * Handles casino-grade session management for game lobby
 * Supports URL parameter parsing, operator validation, and game launch with session context
 */

export interface LobbySessionData {
  session_id?: string;
  user_id?: string;
  operator_id?: string;
  currency?: string;
  play_mode: 'real' | 'demo';
  game_mode?: 'desktop' | 'mobile';
  language?: string;
  lobby_url?: string;
  authenticated?: boolean;
  balance?: number;
  operator_details?: any;
}

export interface GameLaunchConfig {
  game_code: string;
  session_id: string;
  user_id: string;
  operator_id: string;
  currency: string;
  play_mode: 'real' | 'demo';
  game_mode?: 'desktop' | 'mobile';
  language?: string;
  lobby_url?: string;
}

export class LobbySessionManager {
  private static instance: LobbySessionManager;
  private sessionData: LobbySessionData | null = null;
  private operatorGames: string[] = [];

  private constructor() {
    this.initializeSession();
  }

  public static getInstance(): LobbySessionManager {
    if (!LobbySessionManager.instance) {
      LobbySessionManager.instance = new LobbySessionManager();
    }
    return LobbySessionManager.instance;
  }

  /**
   * Initialize session from URL parameters or create demo session
   */
  private initializeSession(): void {
    console.log('🏛️ Initializing lobby session manager...');
    
    // Parse URL parameters for operator launch
    const urlParams = this.parseURLParameters();
    
    if (urlParams && this.isValidOperatorLaunch(urlParams)) {
      console.log('✅ Valid operator launch parameters found:', urlParams);
      this.sessionData = urlParams;
    } else {
      console.log('🎮 No operator parameters, using demo mode');
      this.sessionData = this.createDemoSession();
    }
    
    console.log('✅ Lobby session initialized:', this.sessionData);
  }

  /**
   * Parse URL parameters for operator launch
   */
  private parseURLParameters(): LobbySessionData | null {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      
      if (urlParams.size === 0) {
        return null;
      }

      const params: Partial<LobbySessionData> = {};
      
      // Parse all possible parameters
      if (urlParams.get('session_id')) params.session_id = urlParams.get('session_id')!;
      if (urlParams.get('user_id')) params.user_id = urlParams.get('user_id')!;
      if (urlParams.get('operator_id')) params.operator_id = urlParams.get('operator_id')!;
      if (urlParams.get('currency')) params.currency = urlParams.get('currency')!;
      if (urlParams.get('play_mode')) params.play_mode = urlParams.get('play_mode') as 'real' | 'demo';
      if (urlParams.get('game_mode')) params.game_mode = urlParams.get('game_mode') as 'desktop' | 'mobile';
      if (urlParams.get('language')) params.language = urlParams.get('language')!;
      if (urlParams.get('lobby_url')) params.lobby_url = urlParams.get('lobby_url')!;

      // Set defaults
      params.play_mode = params.play_mode || 'demo';
      params.game_mode = params.game_mode || 'desktop';
      params.language = params.language || 'en';
      params.currency = params.currency || 'USD';

      return params as LobbySessionData;
    } catch (error) {
      console.error('❌ Error parsing URL parameters:', error);
      return null;
    }
  }

  /**
   * Validate operator launch parameters
   */
  private isValidOperatorLaunch(params: LobbySessionData): boolean {
    // For operator launch, we need at least operator_id
    return !!(params.operator_id && params.operator_id !== 'demo_server');
  }

  /**
   * Create demo session for local testing
   */
  private createDemoSession(): LobbySessionData {
    return {
      session_id: this.generateUUID(),
      user_id: this.generateUUID(),
      operator_id: 'demo_server',
      currency: 'USD',
      play_mode: 'demo',
      game_mode: 'desktop',
      language: 'en',
      balance: 1000.00
    };
  }

  /**
   * Generate UUID v4
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Get API base URL from environment
   */
  private getApiBaseUrl(): string {
    if (typeof import.meta !== 'undefined' && (import.meta as any).env) {
      const baseUrl = (import.meta as any).env.VITE_BASE_API_URL;
      if (baseUrl) return baseUrl;
    }

    if (typeof process !== 'undefined' && process.env) {
      const baseUrl = process.env.VITE_BASE_API_URL || process.env.BASE_API_ROUTE;
      if (baseUrl) return baseUrl;
    }

    return 'http://localhost:3100/api';
  }

  /**
   * Authenticate session with backend (for operator launches)
   */
  public async authenticateSession(): Promise<boolean> {
    if (!this.sessionData || this.sessionData.play_mode === 'demo') {
      // Demo mode doesn't require authentication
      return true;
    }

    try {
      console.log('🔐 Authenticating lobby session...');

      const apiBaseUrl = this.getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/session/authenticate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: this.sessionData.session_id,
          operator_id: this.sessionData.operator_id,
          external_user_id: this.sessionData.user_id,
          game_code: 'lobby', // Special game code for lobby
          currency: this.sessionData.currency,
          play_mode: this.sessionData.play_mode
        })
      });

      if (!response.ok) {
        console.error('❌ Lobby authentication failed:', response.status);
        return false;
      }

      const result = await response.json();
      
      if (result.authenticated) {
        this.sessionData.authenticated = true;
        this.sessionData.balance = result.balance;
        this.sessionData.operator_details = result.player_details;
        console.log('✅ Lobby session authenticated successfully');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Lobby authentication error:', error);
      return false;
    }
  }

  /**
   * Load operator-specific games from Redis/Database
   */
  public async loadOperatorGames(): Promise<string[]> {
    try {
      const apiBaseUrl = this.getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/config/operator-games`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operator_id: this.sessionData?.operator_id || 'demo_server'
        })
      });

      if (response.ok) {
        const result = await response.json();
        this.operatorGames = result.games || [];
        return this.operatorGames;
      }
    } catch (error) {
      console.warn('⚠️ Failed to load operator games, using defaults:', error);
    }

    // Default games for demo/fallback
    this.operatorGames = ['ins_mines', 'ins_lootbox_base', 'ins_lootbox_new', 'ins_lootbox_third'];
    return this.operatorGames;
  }

  /**
   * Generate game launch URL with session context
   */
  public generateGameLaunchUrl(gameCode: string): string {
    if (!this.sessionData) {
      throw new Error('No session data available for game launch');
    }

    const launchConfig: GameLaunchConfig = {
      game_code: gameCode,
      session_id: this.sessionData.session_id || this.generateUUID(),
      user_id: this.sessionData.user_id || this.generateUUID(),
      operator_id: this.sessionData.operator_id || 'demo_server',
      currency: this.sessionData.currency || 'USD',
      play_mode: this.sessionData.play_mode,
      game_mode: this.sessionData.game_mode || 'desktop',
      language: this.sessionData.language || 'en',
      lobby_url: this.sessionData.lobby_url
    };

    // Build URL with parameters
    const gameUrl = new URL(`/${gameCode}.html`, window.location.origin);
    
    Object.entries(launchConfig).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        gameUrl.searchParams.set(key, String(value));
      }
    });

    return gameUrl.toString();
  }

  /**
   * Get current session data
   */
  public getSessionData(): LobbySessionData | null {
    return this.sessionData;
  }

  /**
   * Check if session is authenticated
   */
  public isAuthenticated(): boolean {
    return this.sessionData?.authenticated === true || this.sessionData?.play_mode === 'demo';
  }

  /**
   * Get operator-specific games
   */
  public getOperatorGames(): string[] {
    return this.operatorGames;
  }
}
