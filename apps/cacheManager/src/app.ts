import './config/envConfig';
import { preloadCache } from '@cacheManager/loaders/preloadCache';
import { assertDatabaseConnection } from './lifecycle/setupDb';
import { setupShutdownHooks } from './lifecycle/setupShutdown';
import { scheduleCacheRefresh } from './lifecycle/scheduleRefresh';
import { cacheWarmingService } from './services/cacheWarming';

export async function startCacheManager() {
    console.log('🚀 CacheManager booting...');
    await assertDatabaseConnection();

    const wipe = process.env.CACHE_WIPE === 'true';
    console.time('preloadCache');
    await preloadCache({ wipe });
    console.timeEnd('preloadCache');

    // Phase 2: Enhanced cache warming with prioritized loading
    console.log('🔥 Starting enhanced cache warming...');
    const warmingResult = await cacheWarmingService.warmCache();

    if (warmingResult.success) {
        console.log('✅ Cache warming completed successfully');
    } else {
        console.warn('⚠️ Cache warming completed with some failures:', warmingResult.results);
    }

    console.log('✅ CacheManager preload complete.');
    setupShutdownHooks();

    console.log(`🧪 CACHE_PERSIST env value: ${process.env.CACHE_PERSIST}`);
    if (process.env.CACHE_PERSIST === 'true') {
        console.log('🕓 CacheManager will remain running as a Redis vault service...');
        console.log('🔁 Starting scheduleCacheRefresh...');
        scheduleCacheRefresh();
        console.log('✅ scheduleCacheRefresh started successfully.');
    } else {
        process.exit(0);
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    startCacheManager().catch((err) => {
        console.error('💥 Uncaught error in cacheManager startup:', err);
        process.exit(1);
    });
}
