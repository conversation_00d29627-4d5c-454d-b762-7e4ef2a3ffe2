// src/loaders/currencies/currencyConfigs.ts

import { setWithTTL, redisKeys } from '../deps';
import { timeSafe, timeEndSafe } from '../../utils/timing';

async function loadCurrencyData(): Promise<any[]> {
    const { db } = await import('../deps');

    const result = await db.query(`
        SELECT currency_code, symbol, pretty_multiplier, is_enabled_by_default
        FROM config.currencies
        ORDER BY currency_code ASC
    `);

    return result.rows.map((row) => ({
        name: row.currency_code,
        symbol: row.symbol,
        multiplier: row.pretty_multiplier,
        isEnabledByDefault: row.is_enabled_by_default,
    }));
}

async function loadOperatorCurrencyOverrides(): Promise<any[]> {
    const { db } = await import('../deps');

    const result = await db.query(`
        SELECT
            occ.operator_id,
            occ.currency_code,
            occ.pretty_multiplier,
            occ.is_enabled,
            occ.is_default,
            c.symbol,
            c.is_enabled_by_default
        FROM config.operator_currency_config occ
        JOIN config.currencies c ON occ.currency_code = c.currency_code
        ORDER BY occ.operator_id, occ.currency_code ASC
    `);

    return result.rows.map((row) => ({
        operatorId: row.operator_id,
        currencyCode: row.currency_code,
        symbol: row.symbol,
        multiplier: row.pretty_multiplier || 1.0,
        isEnabled: row.is_enabled,
        isDefault: row.is_default,
        globalDefault: row.is_enabled_by_default
    }));
}

async function refreshCurrencyByCode(data: any[]): Promise<void> {
    timeSafe('currencyByCode');
    for (const currency of data) {
        await setWithTTL(redisKeys.currencyByCode(currency.name), currency, 'currencyByCode');
    }
    timeEndSafe('currencyByCode');
}

async function refreshAllCurrencies(data: any[]): Promise<void> {
    timeSafe('allCurrencies');
    await setWithTTL(redisKeys.allCurrencies, data, 'allCurrencies');
    timeEndSafe('allCurrencies');
}

async function refreshOperatorCurrencyOverrides(data: any[]): Promise<void> {
    timeSafe('operatorCurrencyOverride');

    // Group by operator for efficient caching
    const operatorCurrencies: Record<string, any[]> = {};

    for (const override of data) {
        const { operatorId, currencyCode } = override;

        // Cache individual operator-currency override
        const overrideKey = redisKeys.operatorCurrencyOverride(operatorId, currencyCode);
        await setWithTTL(overrideKey, override, 'operatorCurrencyOverride');

        // Build operator currency list
        if (!operatorCurrencies[operatorId]) {
            operatorCurrencies[operatorId] = [];
        }
        operatorCurrencies[operatorId].push({
            currencyCode,
            isEnabled: override.isEnabled,
            isDefault: override.isDefault,
            symbol: override.symbol
        });
    }

    // Cache operator currency lists
    for (const [operatorId, currencies] of Object.entries(operatorCurrencies)) {
        const listKey = redisKeys.operatorCurrencyList(operatorId);
        await setWithTTL(listKey, currencies, 'operatorCurrencyList');
    }

    timeEndSafe('operatorCurrencyOverride');
}

async function refreshCurrencyValidationMatrix(globalCurrencies: any[], operatorOverrides: any[]): Promise<void> {
    timeSafe('currencyValidationMatrix');

    // Create validation matrix for efficient lookups
    const validationMatrix: Record<string, any> = {};

    // Add global currencies
    for (const currency of globalCurrencies) {
        validationMatrix[`global_${currency.name}`] = {
            currencyCode: currency.name,
            isEnabledByDefault: currency.isEnabledByDefault,
            symbol: currency.symbol,
            type: 'global'
        };
    }

    // Add operator-specific overrides
    for (const override of operatorOverrides) {
        const key = `${override.operatorId}_${override.currencyCode}`;
        validationMatrix[key] = {
            operatorId: override.operatorId,
            currencyCode: override.currencyCode,
            isEnabled: override.isEnabled,
            isDefault: override.isDefault,
            symbol: override.symbol,
            globalDefault: override.globalDefault,
            type: 'operator_override'
        };
    }

    // Cache the complete validation matrix
    await setWithTTL(redisKeys.currencyValidationMatrix, validationMatrix, 'currencyValidationMatrix');

    timeEndSafe('currencyValidationMatrix');
}

export const currencyConfigs = {
    id: 'currencies',
    keys: ['currencyByCode', 'allCurrencies', 'operatorCurrencyOverride', 'operatorCurrencyList', 'currencyValidationMatrix', 'cacheLastUpdated'],

    load: async (): Promise<void> => {
        timeSafe('currencyConfigs');
        console.log('💱 Loading Currency Configs into Redis...');

        try {
            // Load global currencies
            const globalCurrencies = await loadCurrencyData();
            console.log(`📊 Loaded ${globalCurrencies.length} global currencies`);

            // Load operator currency overrides
            const operatorOverrides = await loadOperatorCurrencyOverrides();
            console.log(`📊 Loaded ${operatorOverrides.length} operator currency overrides`);

            // Cache global currencies
            await refreshCurrencyByCode(globalCurrencies);
            await refreshAllCurrencies(globalCurrencies);

            // Cache operator currency overrides
            await refreshOperatorCurrencyOverrides(operatorOverrides);

            // Cache currency validation matrix for fast lookups
            await refreshCurrencyValidationMatrix(globalCurrencies, operatorOverrides);

            console.log('✅ Currency configs loaded with operator overrides.');

            const timestamp = new Date().toISOString();
            await setWithTTL(redisKeys.cacheLastUpdated('currencies'), timestamp, 'cacheLastUpdated', false);
        } catch (err) {
            console.error(`❌ Error in currencyConfigs loader:`, {
                error: err instanceof Error ? err.message : String(err),
                stack: err instanceof Error ? err.stack : null,
            });
        } finally {
            timeEndSafe('currencyConfigs');
        }
    },
};
