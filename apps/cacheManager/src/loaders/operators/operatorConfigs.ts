import { setWithTTL, redisKeys } from '../deps';
import { timeSafe, timeEndSafe } from '../../utils/timing';

async function loadOperatorById() {
    try {
        const { db } = await import('../deps');

        // Load operators from database
        const operatorsQuery = `
            SELECT
                o.operator_id,
                o.name,
                o.domain,
                o.status,
                o.default_currency,
                o.default_jurisdiction,
                o.lobby_url,
                o.cashier_url,
                o.base_api_url,
                o.created_at,
                o.updated_at
            FROM data.operators o
            WHERE o.status = 'active'
        `;

        const operatorsResult = await db.query(operatorsQuery);

        // Load operator credentials
        const credentialsQuery = `
            SELECT
                operator_id,
                environment,
                api_key,
                public_key,
                ip_whitelist,
                algorithm,
                rgs_key_id
            FROM config.operator_credentials
        `;

        const credentialsResult = await db.query(credentialsQuery);

        // Load operator endpoints
        const endpointsQuery = `
            SELECT
                operator_id,
                environment,
                endpoint_type,
                endpoint_url,
                is_enabled
            FROM config.operator_custom_endpoints
        `;

        const endpointsResult = await db.query(endpointsQuery);

        // Combine operator data
        for (const operator of operatorsResult.rows) {
            const operatorCredentials = credentialsResult.rows.filter(c => c.operator_id === operator.operator_id);
            const operatorEndpoints = endpointsResult.rows.filter(e => e.operator_id === operator.operator_id);

            for (const cred of operatorCredentials) {
                const { environment } = cred;
                const key = `${operator.operator_id}_${environment}`;

                // Build endpoints object
                const endpoints: Record<string, { url: string; enabled: boolean }> = {};
                for (const ep of operatorEndpoints.filter(e => e.environment === environment)) {
                    endpoints[ep.endpoint_type] = {
                        url: ep.endpoint_url,
                        enabled: ep.is_enabled,
                    };
                }

                const operatorDetails = {
                    ...operator,
                    ...cred,
                    endpoints,
                    environment
                };

                await setWithTTL(redisKeys.operatorById(key), operatorDetails, 'operatorById');
            }
        }

        // Add demo_server operator for development
        const demoOperator = {
            operator_id: 'demo_server',
            name: 'Demo Server',
            domain: 'localhost',
            status: 'active',
            default_currency: 'USD',
            environment: 'demo',
            api_key: 'DEMOGAMES',
            balance: 1000.00,
            endpoints: {
                session_validate: { url: 'http://localhost:3100/api/session/validate', enabled: true }
            }
        };

        await setWithTTL(redisKeys.operatorById('demo_server_demo'), demoOperator, 'operatorById');
        await setWithTTL(redisKeys.operatorById('demo_server_production'), demoOperator, 'operatorById');

        console.log(`✅ Loaded ${operatorsResult.rows.length} operators from database`);

    } catch (error) {
        console.error('❌ Failed to load operators from database, using fallbacks:', error);

        // Fallback operators
        await setWithTTL(redisKeys.operatorById('demo_server_demo'), {
            operator_id: 'demo_server',
            name: 'Demo Server',
            status: 'active',
            environment: 'demo',
            api_key: 'DEMOGAMES',
            balance: 1000.00
        }, 'operatorById');

        await setWithTTL(redisKeys.operatorById('insolence_demo_1_production'), {
            operator_id: 'insolence_demo_1',
            name: 'Insolence Demo 1',
            status: 'active',
            environment: 'production'
        }, 'operatorById');
    }
}

async function loadCacheTimestamp() {
    const timestamp = new Date().toISOString();
    await setWithTTL(redisKeys.cacheLastUpdated('operators'), timestamp, 'cacheLastUpdated', false);
}

export const operatorConfigs = {
    id: 'operators',
    keys: ['operatorById', 'cacheLastUpdated'],

    load: async (key?: string): Promise<void> => {
        timeSafe('operatorConfigs');
        console.log('🏢 Loading Operator Details into Redis...');

        try {
            if (!key || key === 'operatorById') await loadOperatorById();
            if (!key || key === 'cacheLastUpdated') await loadCacheTimestamp();

            console.log('✅ Operator placeholders loaded.');
        } catch (err) {
            console.error(`❌ Error in operatorConfigs loader:`, {
                error: err instanceof Error ? err.message : String(err),
                stack: err instanceof Error ? err.stack : null,
            });
        } finally {
            timeEndSafe('operatorConfigs');
        }
    },
};