// /src/loaders/loaderRegistry.ts
import { apiKeys } from './apiKeys/apiKeys';
import { gamesConfigs } from './games/gamesConfigs';
import { gameEngines } from './games/gamesEngines';
import { rtpConfigs } from './games/rtpConfigs';
import { currencyConfigs } from './currencies/currencyConfigs';
import { operatorConfigs } from './operators/operatorConfigs';
import { activeUsers } from './users/activeUsers';
import { activeSessions } from './sessions/activeSessions';
import { cacheVersion } from './meta/cacheVersion';
import { realTimeLoader } from './enhanced/realTimeLoader';

export const loaderRegistry = {
    apiKeys: {
        load: apiKeys.load,
        loadApiKeyMeta: apiKeys.loadApiKeyMeta,
        loadDefaultApiKey: apiKeys.loadDefaultApiKey,
        keys: ['apiKeys', 'apiKeyMeta', 'defaultApiKey'],
    },
    games: {
        load: gamesConfigs.load,
        keys: ['allGames', 'gameByCode', 'operatorGameConfig', 'cacheLastUpdated'],
    },
    gameEngines: {
        load: gameEngines.load,
        keys: ['gameEngine', 'gameAnimation', 'gameCertifications', 'cacheLastUpdated'],
    },
    rtp: {
        load: rtpConfigs.load,
        keys: ['gameRTP', 'operatorRTPSettings', 'cacheLastUpdated'],
    },
    currencies: {
        load: currencyConfigs.load,
        keys: ['currencyByCode', 'allCurrencies', 'operatorCurrencyOverride', 'operatorCurrencyList'],
    },
    operators: {
        load: operatorConfigs.load,
        keys: ['allOperators', 'operatorById', 'operatorAuthPublicKey', 'operatorCallbackURL', 'operatorGamePermissions'],
    },
    users: {
        load: activeUsers.load,
        keys: ['userById', 'activeUsers', 'cacheLastUpdated'],
    },
    sessions: {
        load: activeSessions.load,
        keys: ['sessionById', 'sessionMeta', 'userActiveSessions', 'cacheLastUpdated'],
    },
    cacheVersion: {
        load: cacheVersion.load,
        keys: ['cacheVersion'],
    },
    realTime: {
        load: realTimeLoader.load,
        loadActiveSessions: realTimeLoader.loadActiveSessions,
        loadOperatorGamePermissions: realTimeLoader.loadOperatorGamePermissions,
        loadGameBetLimits: realTimeLoader.loadGameBetLimits,
        loadSessionMetrics: realTimeLoader.loadSessionMetrics,
        loadGameAvailability: realTimeLoader.loadGameAvailability,
        keys: realTimeLoader.keys,
    },
};