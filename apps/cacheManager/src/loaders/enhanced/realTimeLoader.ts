/**
 * Enhanced Real-Time Cache Loader
 * Loads frequently accessed data for high-performance casino operations
 */

import { setWithTTL, redisKeys } from '../deps';
import { timeSafe, timeEndSafe } from '../../utils/timing';

/**
 * Load active sessions into Redis for fast authentication
 */
async function loadActiveSessions() {
    try {
        const { db } = await import('../deps');
        
        // Load all active sessions from last 24 hours
        const sessionsQuery = `
            SELECT 
                gs.session_id,
                gs.operator_id,
                gs.user_id,
                gs.game_code,
                gs.currency,
                gs.play_mode,
                gs.status,
                gs.balance,
                gs.started_at,
                gs.metadata,
                EXTRACT(EPOCH FROM (NOW() - gs.started_at)) as session_age_seconds
            FROM data.game_sessions gs
            WHERE gs.status = 'open'
                AND gs.started_at > NOW() - INTERVAL '24 hours'
            ORDER BY gs.started_at DESC
        `;
        
        const sessionsResult = await db.query(sessionsQuery);
        
        for (const session of sessionsResult.rows) {
            // Cache session details
            const sessionData = {
                session_id: session.session_id,
                operator_id: session.operator_id,
                user_id: session.user_id,
                game_code: session.game_code,
                currency: session.currency,
                play_mode: session.play_mode,
                status: session.status,
                balance: session.balance || 0,
                metadata: session.metadata || {},
                created_at: session.started_at,
                last_activity: new Date().toISOString(),
                session_age_seconds: session.session_age_seconds
            };
            
            // Cache session with appropriate TTL based on age
            const ttl = Math.max(3600, 86400 - session.session_age_seconds); // At least 1 hour
            await setWithTTL(redisKeys.sessionById(session.session_id), sessionData, 'sessionById', false, ttl);
            
            // Add to user's active sessions
            await setWithTTL(redisKeys.userActiveSessions(session.user_id), [session.session_id], 'userActiveSessions');
            
            // Add to operator's active sessions
            await setWithTTL(redisKeys.operatorActiveSessions(session.operator_id), [session.session_id], 'operatorActiveSessions');
            
            // Add to game's active sessions
            await setWithTTL(redisKeys.activeSessionsByGame(session.game_code), [session.session_id], 'activeSessionsByGame');
        }
        
        console.log(`✅ Loaded ${sessionsResult.rows.length} active sessions into Redis`);
        
    } catch (error) {
        console.error('❌ Failed to load active sessions:', error);
    }
}

/**
 * Load operator game permissions for fast authorization
 */
async function loadOperatorGamePermissions() {
    try {
        const { db } = await import('../deps');
        
        // Load operator game permissions
        const permissionsQuery = `
            SELECT DISTINCT
                o.operator_id,
                g.game_code,
                COALESCE(ogs.is_enabled, true) as is_enabled
            FROM data.operators o
            CROSS JOIN config.games g
            LEFT JOIN config.operator_game_settings ogs 
                ON o.operator_id = ogs.operator_id AND g.game_code = ogs.game_code
            WHERE o.status = 'active' 
                AND g.status = 'active'
                AND COALESCE(ogs.is_enabled, true) = true
        `;
        
        const permissionsResult = await db.query(permissionsQuery);
        
        // Group by operator
        const operatorPermissions: Record<string, string[]> = {};
        
        for (const perm of permissionsResult.rows) {
            if (!operatorPermissions[perm.operator_id]) {
                operatorPermissions[perm.operator_id] = [];
            }
            operatorPermissions[perm.operator_id].push(perm.game_code);
        }
        
        // Cache permissions for each operator
        for (const [operatorId, gameList] of Object.entries(operatorPermissions)) {
            await setWithTTL(redisKeys.operatorGamePermissions(operatorId), gameList, 'operatorGamePermissions');
        }
        
        console.log(`✅ Loaded game permissions for ${Object.keys(operatorPermissions).length} operators`);
        
    } catch (error) {
        console.error('❌ Failed to load operator game permissions:', error);
    }
}

/**
 * Load bet limits for all games
 */
async function loadGameBetLimits() {
    try {
        const { db } = await import('../deps');
        
        const betLimitsQuery = `
            SELECT 
                bl.game_code,
                bl.currency,
                bl.min_bet,
                bl.max_bet,
                bl.operator_id,
                bl.is_enabled
            FROM config.bet_limits bl
            WHERE bl.is_enabled = true
            ORDER BY bl.game_code, bl.currency
        `;
        
        const betLimitsResult = await db.query(betLimitsQuery);
        
        // Group by game and currency
        const gameBetLimits: Record<string, any> = {};
        
        for (const limit of betLimitsResult.rows) {
            const key = `${limit.game_code}_${limit.currency}`;
            if (!gameBetLimits[key]) {
                gameBetLimits[key] = {
                    game_code: limit.game_code,
                    currency: limit.currency,
                    global_limits: null,
                    operator_limits: {}
                };
            }
            
            if (limit.operator_id) {
                gameBetLimits[key].operator_limits[limit.operator_id] = {
                    min_bet: limit.min_bet,
                    max_bet: limit.max_bet
                };
            } else {
                gameBetLimits[key].global_limits = {
                    min_bet: limit.min_bet,
                    max_bet: limit.max_bet
                };
            }
        }
        
        // Cache bet limits
        for (const [key, limits] of Object.entries(gameBetLimits)) {
            await setWithTTL(redisKeys.gameBetLimits(limits.game_code), limits, 'gameBetLimits');
        }
        
        console.log(`✅ Loaded bet limits for ${Object.keys(gameBetLimits).length} game-currency combinations`);
        
    } catch (error) {
        console.error('❌ Failed to load game bet limits:', error);
    }
}

/**
 * Load session metrics for operators
 */
async function loadSessionMetrics() {
    try {
        const { db } = await import('../deps');
        
        const metricsQuery = `
            SELECT 
                gs.operator_id,
                COUNT(*) as total_sessions,
                COUNT(CASE WHEN gs.status = 'open' THEN 1 END) as active_sessions,
                COUNT(CASE WHEN gs.play_mode = 'real' THEN 1 END) as real_sessions,
                COUNT(CASE WHEN gs.play_mode = 'demo' THEN 1 END) as demo_sessions,
                AVG(EXTRACT(EPOCH FROM (COALESCE(gs.ended_at, NOW()) - gs.started_at))) as avg_session_duration,
                MAX(gs.started_at) as last_session_start
            FROM data.game_sessions gs
            WHERE gs.started_at > NOW() - INTERVAL '24 hours'
            GROUP BY gs.operator_id
        `;
        
        const metricsResult = await db.query(metricsQuery);
        
        for (const metric of metricsResult.rows) {
            const sessionMetrics = {
                operator_id: metric.operator_id,
                total_sessions: parseInt(metric.total_sessions),
                active_sessions: parseInt(metric.active_sessions),
                real_sessions: parseInt(metric.real_sessions),
                demo_sessions: parseInt(metric.demo_sessions),
                avg_session_duration: parseFloat(metric.avg_session_duration || '0'),
                last_session_start: metric.last_session_start,
                updated_at: new Date().toISOString()
            };
            
            await setWithTTL(redisKeys.sessionMetrics(metric.operator_id), sessionMetrics, 'sessionMetrics');
        }
        
        console.log(`✅ Loaded session metrics for ${metricsResult.rows.length} operators`);
        
    } catch (error) {
        console.error('❌ Failed to load session metrics:', error);
    }
}

/**
 * Load game availability matrix
 */
async function loadGameAvailability() {
    try {
        const { db } = await import('../deps');
        
        const availabilityQuery = `
            SELECT 
                o.operator_id,
                g.game_code,
                g.status as game_status,
                g.globally_enabled,
                COALESCE(ogs.is_enabled, true) as operator_enabled,
                CASE 
                    WHEN g.status = 'active' AND g.globally_enabled = true AND COALESCE(ogs.is_enabled, true) = true 
                    THEN true 
                    ELSE false 
                END as is_available
            FROM data.operators o
            CROSS JOIN config.games g
            LEFT JOIN config.operator_game_settings ogs 
                ON o.operator_id = ogs.operator_id AND g.game_code = ogs.game_code
            WHERE o.status = 'active'
        `;
        
        const availabilityResult = await db.query(availabilityQuery);
        
        for (const avail of availabilityResult.rows) {
            const availabilityData = {
                operator_id: avail.operator_id,
                game_code: avail.game_code,
                is_available: avail.is_available,
                game_status: avail.game_status,
                globally_enabled: avail.globally_enabled,
                operator_enabled: avail.operator_enabled,
                cached_at: new Date().toISOString()
            };
            
            await setWithTTL(
                redisKeys.gameAvailability(avail.operator_id, avail.game_code), 
                availabilityData, 
                'gameAvailability'
            );
        }
        
        console.log(`✅ Loaded game availability for ${availabilityResult.rows.length} operator-game combinations`);
        
    } catch (error) {
        console.error('❌ Failed to load game availability:', error);
    }
}

export const realTimeLoader = {
    id: 'realTimeLoader',
    
    keys: [
        'sessionById', 'userActiveSessions', 'operatorActiveSessions', 'activeSessionsByGame',
        'operatorGamePermissions', 'gameBetLimits', 'sessionMetrics', 'gameAvailability'
    ],
    
    load: async (): Promise<void> => {
        timeSafe('realTimeLoader');
        console.log('⚡ Loading real-time casino data into Redis...');
        
        try {
            await Promise.all([
                loadActiveSessions(),
                loadOperatorGamePermissions(),
                loadGameBetLimits(),
                loadSessionMetrics(),
                loadGameAvailability()
            ]);
            
            const timestamp = new Date().toISOString();
            await setWithTTL(redisKeys.cacheLastUpdated('realTimeLoader'), timestamp, 'cacheLastUpdated', false);
            
            console.log('✅ Real-time loader completed successfully');
            
        } catch (err) {
            console.error(`❌ Error in realTimeLoader:`, {
                error: err instanceof Error ? err.message : String(err),
                stack: err instanceof Error ? err.stack : null,
            });
        } finally {
            timeEndSafe('realTimeLoader');
        }
    },
    
    // Individual loader functions for targeted updates
    loadActiveSessions,
    loadOperatorGamePermissions,
    loadGameBetLimits,
    loadSessionMetrics,
    loadGameAvailability
};
