/**
 * Cache Warming Service
 * Preloads frequently accessed data during application startup
 */

import { loaderRegistry } from '../loaders/loaderRegistry';
import { timeSafe, timeEndSafe } from '../utils/timing';

interface CacheWarmingConfig {
  priority: number;
  loaderId: string;
  description: string;
  critical: boolean; // If true, failure blocks startup
}

const CACHE_WARMING_SEQUENCE: CacheWarmingConfig[] = [
  {
    priority: 1,
    loaderId: 'currencies',
    description: 'Global currencies and operator overrides',
    critical: true
  },
  {
    priority: 2,
    loaderId: 'operators',
    description: 'Operator configurations and credentials',
    critical: true
  },
  {
    priority: 3,
    loaderId: 'games',
    description: 'Game configurations and availability',
    critical: true
  },
  {
    priority: 4,
    loaderId: 'realTime',
    description: 'Active sessions and real-time data',
    critical: false
  },
  {
    priority: 5,
    loaderId: 'rtpConfigs',
    description: 'RTP settings and configurations',
    critical: false
  }
];

export class CacheWarmingService {
  private static instance: CacheWarmingService;
  private isWarming = false;
  private warmingResults: Record<string, { success: boolean; duration: number; error?: string }> = {};

  static getInstance(): CacheWarmingService {
    if (!CacheWarmingService.instance) {
      CacheWarmingService.instance = new CacheWarmingService();
    }
    return CacheWarmingService.instance;
  }

  /**
   * Warm cache during application startup
   */
  async warmCache(): Promise<{ success: boolean; results: any }> {
    if (this.isWarming) {
      console.log('⚠️ Cache warming already in progress');
      return { success: false, results: this.warmingResults };
    }

    this.isWarming = true;
    this.warmingResults = {};
    
    console.log('🔥 Starting cache warming sequence...');
    timeSafe('cacheWarming');

    try {
      // Sort by priority
      const sortedSequence = CACHE_WARMING_SEQUENCE.sort((a, b) => a.priority - b.priority);
      
      for (const config of sortedSequence) {
        await this.warmLoader(config);
      }

      const totalDuration = timeEndSafe('cacheWarming');
      const successCount = Object.values(this.warmingResults).filter(r => r.success).length;
      const totalCount = Object.keys(this.warmingResults).length;

      console.log(`🔥 Cache warming completed: ${successCount}/${totalCount} loaders successful in ${totalDuration}ms`);
      
      return {
        success: successCount === totalCount,
        results: {
          summary: {
            total: totalCount,
            successful: successCount,
            failed: totalCount - successCount,
            duration: totalDuration
          },
          details: this.warmingResults
        }
      };

    } catch (error) {
      console.error('❌ Cache warming failed:', error);
      return {
        success: false,
        results: {
          error: error instanceof Error ? error.message : String(error),
          details: this.warmingResults
        }
      };
    } finally {
      this.isWarming = false;
    }
  }

  /**
   * Warm individual loader with error handling
   */
  private async warmLoader(config: CacheWarmingConfig): Promise<void> {
    const { loaderId, description, critical } = config;
    
    console.log(`🔥 Warming ${loaderId}: ${description}`);
    const startTime = Date.now();

    try {
      const loader = loaderRegistry[loaderId];
      
      if (!loader) {
        throw new Error(`Loader '${loaderId}' not found in registry`);
      }

      await loader.load();
      
      const duration = Date.now() - startTime;
      this.warmingResults[loaderId] = {
        success: true,
        duration
      };
      
      console.log(`✅ ${loaderId} warmed successfully (${duration}ms)`);

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.warmingResults[loaderId] = {
        success: false,
        duration,
        error: errorMessage
      };

      if (critical) {
        console.error(`❌ Critical loader '${loaderId}' failed: ${errorMessage}`);
        throw new Error(`Critical cache warming failed for ${loaderId}: ${errorMessage}`);
      } else {
        console.warn(`⚠️ Non-critical loader '${loaderId}' failed: ${errorMessage}`);
      }
    }
  }

  /**
   * Warm specific cache keys on demand
   */
  async warmSpecificCache(loaderIds: string[]): Promise<{ success: boolean; results: any }> {
    console.log(`🔥 Warming specific cache loaders: ${loaderIds.join(', ')}`);
    
    const results: Record<string, any> = {};
    let successCount = 0;

    for (const loaderId of loaderIds) {
      try {
        const loader = loaderRegistry[loaderId];
        
        if (!loader) {
          throw new Error(`Loader '${loaderId}' not found`);
        }

        const startTime = Date.now();
        await loader.load();
        const duration = Date.now() - startTime;

        results[loaderId] = { success: true, duration };
        successCount++;
        
        console.log(`✅ ${loaderId} warmed (${duration}ms)`);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        results[loaderId] = { success: false, error: errorMessage };
        console.error(`❌ Failed to warm ${loaderId}: ${errorMessage}`);
      }
    }

    return {
      success: successCount === loaderIds.length,
      results: {
        total: loaderIds.length,
        successful: successCount,
        failed: loaderIds.length - successCount,
        details: results
      }
    };
  }

  /**
   * Get cache warming status
   */
  getWarmingStatus(): { isWarming: boolean; results: any } {
    return {
      isWarming: this.isWarming,
      results: this.warmingResults
    };
  }

  /**
   * Refresh critical cache data
   */
  async refreshCriticalCache(): Promise<void> {
    const criticalLoaders = CACHE_WARMING_SEQUENCE
      .filter(config => config.critical)
      .map(config => config.loaderId);

    console.log('🔄 Refreshing critical cache data...');
    await this.warmSpecificCache(criticalLoaders);
  }
}

// Export singleton instance
export const cacheWarmingService = CacheWarmingService.getInstance();
