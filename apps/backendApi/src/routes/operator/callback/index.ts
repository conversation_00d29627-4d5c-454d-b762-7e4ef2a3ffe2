// apps/backendApi/src/routes/operator/callback/index.ts
// Operator callback routes with rate limiting and validation

import express from 'express';
import { schemaValidator } from '@backendApiMiddleware/schemaValidator';
import { authenticateOperator } from '@backendApiMiddleware/authenticateOperator';
import { operatorCallbackRateLimit } from '@backendApiMiddleware/rateLimiter';
import { 
  processBetCallback, 
  processWinCallback, 
  processPromoWinCallback 
} from '@backendApiControllers/operatorCallbackController';

const router = express.Router();

/**
 * POST /api/operator/callback/bet
 * Process bet placement notification from RGS to operator
 * 
 * Purpose: Notify operator of bet placement for balance deduction
 * Rate Limit: 1000 requests/hour per operator
 * Authentication: Required (operator API key + signature)
 * Validation: betCallbackSchema
 */
router.post('/bet',
  operatorCallbackRateLimit,
  authenticateOperator,
  schemaValidator('/api/operator/callback/bet'),
  processBetCallback
);

/**
 * POST /api/operator/callback/win
 * Process win notification from RGS to operator
 * 
 * Purpose: Notify operator of win amount for balance credit
 * Rate Limit: 1000 requests/hour per operator
 * Authentication: Required (operator API key + signature)
 * Validation: winCallbackSchema
 */
router.post('/win',
  operatorCallbackRateLimit,
  authenticateOperator,
  schemaValidator('/api/operator/callback/win'),
  processWinCallback
);

/**
 * POST /api/operator/callback/promo-win
 * Process promotional win notification from RGS to operator
 * 
 * Purpose: Notify operator of promotional wins (tournaments, bonuses)
 * Rate Limit: 1000 requests/hour per operator
 * Authentication: Required (operator API key + signature)
 * Validation: promoWinCallbackSchema
 */
router.post('/promo-win',
  operatorCallbackRateLimit,
  authenticateOperator,
  schemaValidator('/api/operator/callback/promo-win'),
  processPromoWinCallback
);

/**
 * GET /api/operator/callback/status
 * Get callback system status and metrics
 * 
 * Purpose: Health check and monitoring endpoint for operator callbacks
 * Rate Limit: General rate limiting applies
 * Authentication: Required (operator API key)
 */
router.get('/status',
  authenticateOperator,
  async (req, res): Promise<void> => {
    try {
      const operatorId = req.operator?.operatorId || 'unknown';
      
      // Get basic system status
      const status = {
        system: 'operational',
        timestamp: new Date().toISOString(),
        operator_id: operatorId,
        endpoints: {
          bet: '/api/operator/callback/bet',
          win: '/api/operator/callback/win',
          promo_win: '/api/operator/callback/promo-win'
        },
        rate_limits: {
          callback_endpoints: '1000 requests/hour per operator',
          general_api: '200 requests/5min per IP'
        },
        version: '1.0.0'
      };

      res.status(200).json({
        success: true,
        data: status,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('[CallbackStatus] Error getting status:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to get callback status',
        timestamp: new Date().toISOString()
      });
    }
  }
);

/**
 * GET /api/operator/callback/metrics
 * Get callback metrics for the authenticated operator
 * 
 * Purpose: Provide operator-specific callback metrics and performance data
 * Rate Limit: General rate limiting applies
 * Authentication: Required (operator API key)
 */
router.get('/metrics',
  authenticateOperator,
  async (req, res): Promise<void> => {
    try {
      const operatorId = req.operator?.operatorId;
      
      if (!operatorId) {
        return res.status(400).json({
          success: false,
          error: 'OPERATOR_ID_REQUIRED',
          message: 'Operator ID is required for metrics',
          timestamp: new Date().toISOString()
        });
      }

      const timeframe = (req.query.timeframe as 'hour' | 'day' | 'week') || 'hour';
      
      // This would use the AuditLogger to get actual metrics
      // For now, return mock metrics
      const metrics = {
        timeframe,
        operator_id: operatorId,
        total_callbacks: 150,
        successful_callbacks: 148,
        failed_callbacks: 2,
        success_rate: 98.67,
        breakdown: {
          bet_callbacks: 75,
          win_callbacks: 70,
          promo_callbacks: 5
        },
        performance: {
          avg_response_time_ms: 45,
          p95_response_time_ms: 120,
          p99_response_time_ms: 250
        },
        last_updated: new Date().toISOString()
      };

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('[CallbackMetrics] Error getting metrics:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to get callback metrics',
        timestamp: new Date().toISOString()
      });
    }
  }
);

/**
 * POST /api/operator/callback/test
 * Test endpoint for operator callback integration
 * 
 * Purpose: Allow operators to test their callback integration
 * Rate Limit: General rate limiting applies
 * Authentication: Required (operator API key)
 * Note: Only available in development/staging environments
 */
router.post('/test',
  authenticateOperator,
  async (req, res) => {
    try {
      // Only allow in non-production environments
      if (process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          success: false,
          error: 'TEST_ENDPOINT_DISABLED',
          message: 'Test endpoint is not available in production',
          timestamp: new Date().toISOString()
        });
      }

      const operatorId = req.operator?.operatorId || 'test-operator';
      const testType = req.body.test_type || 'connectivity';

      const testResults = {
        test_type: testType,
        operator_id: operatorId,
        timestamp: new Date().toISOString(),
        results: {
          connectivity: 'success',
          authentication: 'success',
          rate_limiting: 'success',
          validation: 'success'
        },
        message: 'All callback systems operational'
      };

      res.status(200).json({
        success: true,
        data: testResults,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('[CallbackTest] Error running test:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to run callback test',
        timestamp: new Date().toISOString()
      });
    }
  }
);

export default router;
