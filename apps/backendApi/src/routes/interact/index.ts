// /backendApi/routes/interact/index.ts

import express from 'express';
import { gameOrchestrator } from "@gamesLogic/engine/gameOrchestrator";
import { schemaValidator } from '@backendApiMiddleware/schemaValidator';
import { authenticateDemoSession } from '@backendApiMiddleware/authenticateGameSession';
import { gameInteractionRateLimit } from '@backendApiMiddleware/rateLimiter';

const router = express.Router();
const isDebug = process.env.LOG_LEVEL === 'debug';

// POST /api/interact/play - Universal game interaction endpoint
router.post('/play',
  gameInteractionRateLimit,
  schemaValidator('/api/interact/play'),
  authenticateDemoSession,
  async (req, res) => {
    try {
      if (isDebug) {
        console.log('[InteractRoute] 🎮 Game interaction request:', {
          game_code: req.body.game_code,
          action: req.body.action,
          session_id: req.body.session_id,
          play_mode: req.body.play_mode
        });
      }

      const result = await gameOrchestrator(req.body);

      if (isDebug) {
        console.log('[InteractRoute] ✅ Game interaction successful');
      }

      res.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (err) {
      console.error('[InteractRoute] ❌ Game execution error:', err);

      // Determine error type and status code
      let statusCode = 500;
      let errorCode = 'GAME_EXECUTION_FAILED';

      if (err instanceof Error) {
        if (err.message.includes('Engine not found')) {
          statusCode = 404;
          errorCode = 'GAME_NOT_FOUND';
        } else if (err.message.includes('Unsupported action')) {
          statusCode = 400;
          errorCode = 'INVALID_ACTION';
        } else if (err.message.includes('Missing required field')) {
          statusCode = 400;
          errorCode = 'MISSING_REQUIRED_FIELD';
        }
      }

      res.status(statusCode).json({
        success: false,
        error: errorCode,
        message: err instanceof Error ? err.message : 'Unknown error occurred',
        timestamp: new Date().toISOString(),
        ...(isDebug && {
          fullError: JSON.parse(JSON.stringify(err, Object.getOwnPropertyNames(err)))
        })
      });
    }
  }
);

// GET /api/interact/history
router.get('/history', (req, res) => {
    res.json({ message: 'History successful response' });  // Simple response
});

export default router;
