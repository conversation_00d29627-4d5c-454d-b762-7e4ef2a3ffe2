// /backendApi/routes/config/index.ts

import express from 'express';
import { schemaValidator } from '@backendApiMiddleware/schemaValidator';
import { getOperatorGames, getGameDetails, getAllGames } from '@backendApiControllers/configController';

const router = express.Router();

// POST /api/config/operator-games -> Get games allowed for specific operator
router.post('/operator-games',
  schemaValidator('/api/config/operator-games'),
  getOperatorGames
);

// POST /api/config/game-details -> Get detailed configuration for specific game
router.post('/game-details',
  schemaValidator('/api/config/game-details'),
  getGameDetails
);

// GET /api/config/all-games -> Get all available games
router.get('/all-games', getAllGames);

export default router;
