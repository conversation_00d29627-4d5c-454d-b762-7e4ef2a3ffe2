import path from 'path';
import { fileURLToPath } from 'url';
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import fs from 'fs';

import { cacheManager } from '@backendApiUtils/cacheManager';
import { signPayload } from '@backendApiUtils/cryptoUtils';
import requestLogger from '@backendApiMiddleware/loggerFormattedRequest';
import rawRequestLogger from '@backendApiMiddleware/loggerRawRequest';

// Setup __dirname manually for ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Then load envs before anything else uses them
import 'dotenv-flow/config';

// 🔌 Pre-warm the database
import { setupDb } from '@backendApiUtils/setupDb';
await setupDb();

// 🔁 Initialize cache
await cacheManager.initialize();

// Import AFTER dotenv
import { authenticateOperator } from '@backendApiMiddleware/authenticateOperator';
import { errorHandler } from '@backendApiMiddleware/errorHandler';
import { generalRateLimit, authRateLimit } from '@backendApiMiddleware/rateLimiter';
import {
  securityHeaders,
  corsSecurityOptions,
  sanitizeInput,
  preventSQLInjection,
  requestSizeLimiter,
  securityMonitoring
} from '@backendApiMiddleware/securityHeaders';
import {
  secureErrorHandler,
  addRequestId,
  responseTimeTracker
} from '@backendApiMiddleware/secureErrorHandler';
import routes from '@backendApiRoutes/index';

const app = express();

// 🔒 Must be first: logs raw body before parsing, auth, etc.
app.use('/api', rawRequestLogger);

// 🔐 Globally sign all outgoing JSON responses
app.use('/api', (req: Request, res: Response, next: NextFunction) => {
  if (
    req.path.startsWith('/api/mock/') ||
    req.path.startsWith('/api/interact/')
  ) return next();

  const originalJson = res.json.bind(res);

  res.json = (body: any) => {
    // Async signing logic inside a fire-and-forget IIFE
    (async () => {
      try {
        const operatorId = body?.operator_id || body?.payload?.operator_id || null;
        const { signature, kid, signedAt } = await signPayload(body, operatorId);

        res.setHeader('x-signature', signature);
        res.setHeader('x-signature-kid', kid);
        res.setHeader('x-signed-at', signedAt);

        originalJson({
          payload: body,
          signature,
          kid,
        });
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error('[Global Signing Error]', errorMessage);

        originalJson({
          payload: body,
          error: 'RESPONSE_SIGNING_FAILED',
          message: 'Failed to sign server response.',
        });
      }
    })();

    // Return synchronously per Express contract
    return res;
  };

  next();
});

// 🛡️ Security middleware - Apply before all other middleware
app.use(addRequestId);
app.use(responseTimeTracker);
app.use(securityHeaders);
app.use(securityMonitoring);
app.use(requestSizeLimiter);

// Standard Express Middleware with enhanced security
app.use(cors(corsSecurityOptions));
app.use(bodyParser.json({ limit: '2mb' }));
app.use(bodyParser.urlencoded({ extended: true }));

// 🛡️ Input validation and sanitization
app.use(sanitizeInput);
app.use(preventSQLInjection);

// 🛡️ Rate limiting - Apply general rate limiting to all API endpoints
app.use('/api', generalRateLimit);

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Pretty Health Check
app.get('/healthPretty', (req: Request, res: Response) => {
  const realIp = req.headers['x-forwarded-for'] || req.socket?.remoteAddress || 'Unknown';

  fs.readFile(path.join(__dirname, 'public', 'health.html'), 'utf8', (err, data) => {
    if (err) return res.status(500).send('Error reading health page.');

    const responseHtml = data
        .replace('{{status}}', 'OK')
        .replace('{{message}}', 'Service is running')
        .replace('{{apiVersion}}', '1.0')
        .replace('{{environment}}', process.env.NODE_ENV || 'development')
        .replace('{{uptime}}', `${process.uptime().toFixed(2)} seconds`)
        .replace('{{timestamp}}', new Date().toISOString())
        .replace('{{ip}}', String(realIp));

    res.send(responseHtml);
  });
});

// 🛡️ Authentication rate limiting - Apply to auth-heavy endpoints
app.use(['/api/session', '/api/verify', '/api/operator'], authRateLimit);

// 🔒 Auth middleware (except for /health and /test/test)
app.use('/api', async (req: Request, res: Response, next: NextFunction) => {
  if (
    req.path === '/health' ||
    req.path === '/test/test' ||
      req.path === '/interact/play' ||
      req.path.startsWith('/interact/')
  ) return next();

  try {
    await authenticateOperator(req, res, next);
  } catch (err) {
    next(err);
  }
});

// 🔍 Request logging
app.use('/api', requestLogger);

// 📦 Mount routes
app.use('/api', routes);

// 🧯 Enhanced error handling with security protection
app.use(secureErrorHandler);

// Start server
const PORT = process.env.PORT || 3100;
const server = app.listen(PORT, () => {
  if (process.env.NODE_ENV !== 'production') {
    console.log(`🚀 RGS backend running on port ${PORT} (env: ${process.env.NODE_ENV || 'development'})`);
  }
});

// 🔁 Graceful Shutdown
async function shutdown(exitCode = 0) {
  console.log('🛑 Shutting down server gracefully...');

  const forceTimeout = setTimeout(() => {
    console.error('💀 Force exiting process after timeout.');
    process.exit(1);
  }, 10000);

  try {
    if (server) {
      await new Promise<void>((resolve, reject) => {
        server.close((err?: Error) => {
          if (err) return reject(err);
          resolve();
        });
      });
      console.log('✅ Server closed.');
    }

    // TODO: Gracefully close DB pool here (if available)
    // await db.end();
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
  } finally {
    clearTimeout(forceTimeout);
    console.log('👋 Exiting process.');
    process.exit(exitCode);
  }
}

// Signals & Error Handling
process.on('SIGTERM', () => { void shutdown(0); });
process.on('SIGINT', () => { void shutdown(0); });
process.on('unhandledRejection', (reason) => {
  console.error('🛑 Unhandled Promise Rejection:', reason);
  void shutdown(1);
});
process.on('uncaughtException', (error) => {
  console.error('🛑 Uncaught Exception:', error);
  void shutdown(1);
});

export default app;
