// apps/backendApi/src/controllers/operatorCallbackController.ts
// Controller for operator callback endpoints with transaction idempotency

import type { Request, Response, NextFunction } from 'express';
import { 
  BetCallbackPayload, 
  WinCallbackPayload, 
  PromoWinCallbackPayload,
  OperatorCallbackResponse 
} from '@backendApiTypes/zod/operatorCallbacks';
import { createError } from '@backendApiUtils/errors/errorFactory';
import { ERROR_TYPES } from '@backendApiUtils/errors/errorTypes';
import { TransactionManager } from '@backendApiServices/transaction/transactionManager';
import { OperatorAPIClient } from '@backendApiServices/operator/operatorAPIClient';
import { AuditLogger } from '@backendApiServices/audit/auditLogger';

const transactionManager = new TransactionManager();
const operatorAPIClient = new OperatorAPIClient();
const auditLogger = new AuditLogger();

/**
 * Process bet placement callback
 * POST /api/operator/callback/bet
 */
export const processBetCallback = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const betData: BetCallbackPayload = req.body;
    const startTime = performance.now();

    // 1. Validate session and operator
    const session = await validateActiveSession(betData.session_id, betData.operator_id);
    if (!session) {
      return next(createError(ERROR_TYPES.SESSION_NOT_FOUND, betData.operator_id));
    }

    // 2. Check for duplicate transaction (idempotency)
    const existingTransaction = await transactionManager.checkDuplicateTransaction(
      betData.transaction_id,
      betData.operator_id,
      'bet'
    );
    
    if (existingTransaction) {
      console.log(`[BetCallback] Duplicate transaction detected: ${betData.transaction_id}`);
      res.status(200).json({
        success: true,
        transaction_id: betData.transaction_id,
        new_balance: existingTransaction.new_balance,
        operator_transaction_id: existingTransaction.operator_transaction_id,
        timestamp: new Date().toISOString(),
        message: 'Transaction already processed'
      });
      return;
    }

    // 3. Validate bet amount and currency
    await validateBetAmount(betData.bet_amount, betData.currency, betData.operator_id);

    // 4. Call operator API for balance deduction
    const operatorResponse = await operatorAPIClient.processBetDeduction(betData);

    // 5. Record transaction in database
    const transactionRecord = await transactionManager.recordBetTransaction(
      betData, 
      operatorResponse
    );

    // 6. Log audit event
    await auditLogger.logBetPlacement({
      round_id: betData.round_id,
      session_id: betData.session_id,
      user_id: betData.user_id,
      operator_id: betData.operator_id,
      bet_amount: betData.bet_amount,
      currency: betData.currency,
      game_code: betData.game_code,
      transaction_id: betData.transaction_id,
    });

    // 7. Record performance metrics
    const duration = performance.now() - startTime;
    console.log(`[BetCallback] Processed in ${duration.toFixed(2)}ms for operator ${betData.operator_id}`);

    const response: OperatorCallbackResponse = {
      success: true,
      transaction_id: betData.transaction_id,
      new_balance: operatorResponse.new_balance,
      operator_transaction_id: operatorResponse.operator_transaction_id,
      timestamp: new Date().toISOString()
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('[BetCallback] Error processing bet:', error);
    next(createError(ERROR_TYPES.BET_PROCESSING_FAILED, req.body?.operator_id, error));
  }
};

/**
 * Process win notification callback
 * POST /api/operator/callback/win
 */
export const processWinCallback = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const winData: WinCallbackPayload = req.body;
    const startTime = performance.now();

    // 1. Validate session and operator
    const session = await validateActiveSession(winData.session_id, winData.operator_id);
    if (!session) {
      return next(createError(ERROR_TYPES.SESSION_NOT_FOUND, winData.operator_id));
    }

    // 2. Check for duplicate transaction (idempotency)
    const existingTransaction = await transactionManager.checkDuplicateTransaction(
      winData.transaction_id,
      winData.operator_id,
      'win'
    );
    
    if (existingTransaction) {
      console.log(`[WinCallback] Duplicate transaction detected: ${winData.transaction_id}`);
      res.status(200).json({
        success: true,
        transaction_id: winData.transaction_id,
        new_balance: existingTransaction.new_balance,
        operator_transaction_id: existingTransaction.operator_transaction_id,
        timestamp: new Date().toISOString(),
        message: 'Transaction already processed'
      });
      return;
    }

    // 3. Validate related bet transaction exists
    await validateRelatedBetTransaction(winData.related_bet_transaction_id, winData.operator_id);

    // 4. Call operator API for balance credit
    const operatorResponse = await operatorAPIClient.processWinCredit(winData);

    // 5. Record transaction in database
    const transactionRecord = await transactionManager.recordWinTransaction(
      winData, 
      operatorResponse
    );

    // 6. Log audit event
    await auditLogger.logWinPayout({
      round_id: winData.round_id,
      session_id: winData.session_id,
      user_id: winData.user_id,
      operator_id: winData.operator_id,
      win_amount: winData.win_amount,
      currency: winData.currency,
      game_code: winData.game_code,
      transaction_id: winData.transaction_id,
      related_bet_transaction_id: winData.related_bet_transaction_id,
    });

    // 7. Record performance metrics
    const duration = performance.now() - startTime;
    console.log(`[WinCallback] Processed in ${duration.toFixed(2)}ms for operator ${winData.operator_id}`);

    const response: OperatorCallbackResponse = {
      success: true,
      transaction_id: winData.transaction_id,
      new_balance: operatorResponse.new_balance,
      operator_transaction_id: operatorResponse.operator_transaction_id,
      timestamp: new Date().toISOString()
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('[WinCallback] Error processing win:', error);
    next(createError(ERROR_TYPES.WIN_PROCESSING_FAILED, req.body?.operator_id, error));
  }
};

/**
 * Process promotional win notification callback
 * POST /api/operator/callback/promo-win
 */
export const processPromoWinCallback = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const promoData: PromoWinCallbackPayload = req.body;
    const startTime = performance.now();

    // 1. Validate session and operator
    const session = await validateActiveSession(promoData.session_id, promoData.operator_id);
    if (!session) {
      return next(createError(ERROR_TYPES.SESSION_NOT_FOUND, promoData.operator_id));
    }

    // 2. Check for duplicate transaction (idempotency)
    const existingTransaction = await transactionManager.checkDuplicateTransaction(
      promoData.transaction_id,
      promoData.operator_id,
      'promo_win'
    );
    
    if (existingTransaction) {
      console.log(`[PromoWinCallback] Duplicate transaction detected: ${promoData.transaction_id}`);
      res.status(200).json({
        success: true,
        transaction_id: promoData.transaction_id,
        new_balance: existingTransaction.new_balance,
        operator_transaction_id: existingTransaction.operator_transaction_id,
        timestamp: new Date().toISOString(),
        message: 'Transaction already processed'
      });
      return;
    }

    // 3. Validate promotion eligibility
    await validatePromotionEligibility(promoData);

    // 4. Call operator API for promotional balance credit
    const operatorResponse = await operatorAPIClient.processPromoWinCredit(promoData);

    // 5. Record transaction in database
    const transactionRecord = await transactionManager.recordPromoWinTransaction(
      promoData, 
      operatorResponse
    );

    // 6. Log audit event
    await auditLogger.logPromoWin({
      round_id: promoData.round_id,
      session_id: promoData.session_id,
      user_id: promoData.user_id,
      operator_id: promoData.operator_id,
      promo_amount: promoData.promo_amount,
      currency: promoData.currency,
      game_code: promoData.game_code,
      transaction_id: promoData.transaction_id,
      promotion_type: promoData.promotion_type,
      tournament_id: promoData.tournament_id,
    });

    // 7. Record performance metrics
    const duration = performance.now() - startTime;
    console.log(`[PromoWinCallback] Processed in ${duration.toFixed(2)}ms for operator ${promoData.operator_id}`);

    const response: OperatorCallbackResponse = {
      success: true,
      transaction_id: promoData.transaction_id,
      new_balance: operatorResponse.new_balance,
      operator_transaction_id: operatorResponse.operator_transaction_id,
      timestamp: new Date().toISOString()
    };

    res.status(200).json(response);

  } catch (error) {
    console.error('[PromoWinCallback] Error processing promo win:', error);
    next(createError(ERROR_TYPES.PROMO_WIN_PROCESSING_FAILED, req.body?.operator_id, error));
  }
};

/**
 * Helper function to validate active session
 */
async function validateActiveSession(sessionId: string, operatorId: string): Promise<any> {
  // Implementation will be added based on existing session validation logic
  // This is a placeholder for the actual session validation
  return { session_id: sessionId, operator_id: operatorId, status: 'active' };
}

/**
 * Helper function to validate bet amount
 */
async function validateBetAmount(amount: number, currency: string, operatorId: string): Promise<void> {
  // Implementation will validate against operator bet limits
  if (amount <= 0) {
    throw new Error('Invalid bet amount');
  }
}

/**
 * Helper function to validate related bet transaction
 */
async function validateRelatedBetTransaction(betTransactionId: string, operatorId: string): Promise<void> {
  // Implementation will check if the related bet transaction exists
  console.log(`Validating related bet transaction: ${betTransactionId}`);
}

/**
 * Helper function to validate promotion eligibility
 */
async function validatePromotionEligibility(promoData: PromoWinCallbackPayload): Promise<void> {
  // Implementation will validate promotion rules and eligibility
  console.log(`Validating promotion eligibility for: ${promoData.promotion_type}`);
}
