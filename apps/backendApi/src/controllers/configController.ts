// /backendApi/controllers/configController.ts
import { Request, Response, NextFunction } from 'express';
import { cacheManager } from '@backendApiUtils/cacheManager';
import { db, getTable } from '@insolence-rgs/db';

const isDebug = process.env.LOG_LEVEL === 'debug';

/**
 * Get operator-specific games configuration
 * POST /api/config/operator-games
 */
export const getOperatorGames = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { operator_id } = req.body;

    if (!operator_id) {
      res.status(400).json({
        error: 'MISSING_OPERATOR_ID',
        message: 'operator_id is required'
      });
      return;
    }

    if (isDebug) {
      console.log('[ConfigController] 🎮 Getting games for operator:', operator_id);
    }

    // Handle demo_server with default games
    if (operator_id === 'demo_server') {
      const demoGames = ['ins_mines', 'ins_lootbox_base', 'ins_lootbox_new', 'ins_lootbox_third'];
      
      res.status(200).json({
        success: true,
        operator_id,
        games: demoGames,
        total_games: demoGames.length,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Check if operator exists in cache
    const operatorDetails = cacheManager.getOperatorDetails(operator_id, 'production');
    if (!operatorDetails) {
      res.status(404).json({
        error: 'OPERATOR_NOT_FOUND',
        message: `Operator ${operator_id} not found or inactive`
      });
      return;
    }

    // Get operator-specific game permissions from database
    const gamePermissionsQuery = `
      SELECT DISTINCT g.game_code, g.name, g.status
      FROM config.games g
      LEFT JOIN config.operator_game_settings ogs ON g.game_code = ogs.game_code AND ogs.operator_id = $1
      WHERE g.status = 'active' 
        AND (ogs.is_enabled = true OR ogs.operator_id IS NULL)
      ORDER BY g.game_code
    `;

    const gamePermissionsResult = await db.query(gamePermissionsQuery, [operator_id]);
    const allowedGames = gamePermissionsResult.rows.map(row => row.game_code);

    if (isDebug) {
      console.log(`[ConfigController] ✅ Found ${allowedGames.length} games for operator ${operator_id}`);
    }

    res.status(200).json({
      success: true,
      operator_id,
      games: allowedGames,
      total_games: allowedGames.length,
      game_details: gamePermissionsResult.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ConfigController] ❌ Error getting operator games:', error);
    
    res.status(500).json({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to retrieve operator games configuration',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get game configuration for specific game
 * POST /api/config/game-details
 */
export const getGameDetails = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { game_code, operator_id } = req.body;

    if (!game_code) {
      res.status(400).json({
        error: 'MISSING_GAME_CODE',
        message: 'game_code is required'
      });
      return;
    }

    if (isDebug) {
      console.log('[ConfigController] 🎯 Getting game details:', { game_code, operator_id });
    }

    // Get base game configuration
    const gameQuery = `
      SELECT 
        g.*,
        ogs.bet_limits,
        ogs.rtp_settings,
        ogs.is_enabled as operator_enabled
      FROM config.games g
      LEFT JOIN config.operator_game_settings ogs ON g.game_code = ogs.game_code AND ogs.operator_id = $2
      WHERE g.game_code = $1
    `;

    const gameResult = await db.query(gameQuery, [game_code, operator_id || 'demo_server']);

    if (!gameResult.rowCount) {
      res.status(404).json({
        error: 'GAME_NOT_FOUND',
        message: `Game ${game_code} not found`
      });
      return;
    }

    const gameDetails = gameResult.rows[0];

    res.status(200).json({
      success: true,
      game_code,
      operator_id: operator_id || 'demo_server',
      game_details: gameDetails,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ConfigController] ❌ Error getting game details:', error);
    
    res.status(500).json({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to retrieve game details',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get all available games (for lobby discovery)
 * GET /api/config/all-games
 */
export const getAllGames = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (isDebug) {
      console.log('[ConfigController] 📋 Getting all available games');
    }

    // Get all active games from database
    const gamesQuery = `
      SELECT 
        game_code,
        name,
        description,
        category,
        technology,
        status,
        created_at
      FROM config.games
      WHERE status = 'active'
      ORDER BY name
    `;

    const gamesResult = await db.query(gamesQuery);

    res.status(200).json({
      success: true,
      games: gamesResult.rows,
      total_games: gamesResult.rows.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ConfigController] ❌ Error getting all games:', error);
    
    res.status(500).json({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to retrieve games list',
      timestamp: new Date().toISOString()
    });
  }
};
