// /backendApi/services/session/sessionAuthenticateService.ts

import { db, getTable } from '@insolence-rgs/db';
import { cacheManager } from '@backendApiUtils/cacheManager';
import { createError } from '@backendApiUtils/errors/errorFactory';
import { ERROR_TYPES } from '@backendApiUtils/errors/errorTypes';
import axios from 'axios';
import { signPayloadWithKey } from '@backendApiUtils/cryptoUtils';
import { validateOperatorSignature } from '@backendApiUtils/verifyOperatorSignature';
import type { SessionAuthenticateInput } from '@backendApiTypes/zod/session';

// Type for operator API response
interface OperatorValidationResponse {
  valid: boolean;
  balance: number;
  currency: string;
  player_details: Record<string, any>;
}

const isDebug = process.env.LOG_LEVEL === 'debug';

export interface SessionAuthenticationResult {
  session_id: string;
  user_id: string;
  operator_id: string;
  game_code: string;
  currency: string;
  play_mode: 'real' | 'demo';
  balance: number;
  authenticated: boolean;
  player_details?: any;
  operator_response?: any;
}

/**
 * Authenticate session with operator validation
 */
export const sessionAuthenticateService = async ({
  session_id,
  operator_id,
  external_user_id,
  game_code,
  currency,
  play_mode,
  meta,
}: SessionAuthenticateInput): Promise<SessionAuthenticationResult> => {
  const startTime = Date.now();

  if (isDebug) {
    console.log('[SessionAuthenticateService] 🔐 Authenticating session:', {
      session_id,
      operator_id,
      external_user_id,
      game_code,
      play_mode
    });
  }

  try {
    // 1. Phase 2: Enhanced cache authentication (simplified and optimized)
    const cacheResult = await cacheManager.authenticateSession(session_id);

    if (cacheResult.authenticated && cacheResult.session) {
      const cachedSession = cacheResult.session;

      // Validate operator and user match
      if (cachedSession.operator_id !== operator_id) {
        throw createError(ERROR_TYPES.OPERATOR_MISMATCH, operator_id, session_id);
      }

      if (isDebug) {
        console.log(`[SessionAuthenticateService] ⚡ Cache hit - session authenticated from ${cacheResult.source}`);
      }

      // Return cached session (optimized for performance)
      const durationMs = Date.now() - startTime;

      // Log session event asynchronously (non-blocking for performance)
      logSessionEvent(session_id, operator_id, 'authenticate', {
        external_user_id,
        game_code,
        authenticated: true,
        balance: cachedSession.balance || (operator_id === 'demo_server' ? 1000.00 : 0),
        currency: cachedSession.currency || currency || 'USD',
        cache_source: cacheResult.source
      }, {
        userId: cachedSession.user_id,
        status: 'success',
        requestData: { session_id, external_user_id, game_code },
        durationMs
      }).catch(err => console.warn('Failed to log session event:', err.message));

      return {
        session_id,
        user_id: cachedSession.user_id,
        operator_id,
        game_code: cachedSession.game_code || game_code,
        currency: cachedSession.currency || currency || 'USD',
        play_mode: cachedSession.play_mode,
        balance: cachedSession.balance || (operator_id === 'demo_server' ? 1000.00 : 0),
        authenticated: true,
        player_details: {
          external_user_id,
          demo_player: operator_id === 'demo_server',
          balance: cachedSession.balance || (operator_id === 'demo_server' ? 1000.00 : 0)
        }
      };
    }

    // 2. Fallback to database validation (cache miss)
    if (isDebug) {
      console.log('[SessionAuthenticateService] 💾 Cache miss - validating session from database');
    }

    const sessionQuery = `
      SELECT s.*, u.external_user_id, u.user_id
      FROM ${getTable('DataGameSessions')} s
      LEFT JOIN ${getTable('DataUsers')} u ON s.user_id = u.user_id
      WHERE s.session_id = $1 AND s.operator_id = $2 AND s.status = 'open'
    `;

    const sessionResult = await db.query(sessionQuery, [session_id, operator_id]);

    if (!sessionResult.rowCount) {
      throw createError(ERROR_TYPES.SESSION_NOT_FOUND, operator_id, session_id);
    }

    const session = sessionResult.rows[0];

    // 2. Validate user matches
    if (session.external_user_id !== external_user_id) {
      throw createError(ERROR_TYPES.USER_MISMATCH, operator_id, session_id);
    }

    // 3. Handle demo server authentication
    if (operator_id === 'demo_server') {
      if (isDebug) {
        console.log('[SessionAuthenticateService] 🎮 Demo server authentication - auto-approved');
      }

      return {
        session_id,
        user_id: session.user_id,
        operator_id,
        game_code: session.game_code || game_code,
        currency: session.currency || currency || 'USD',
        play_mode: session.play_mode || play_mode || 'demo',
        balance: 1000.00, // Demo balance
        authenticated: true,
        player_details: {
          external_user_id,
          demo_player: true,
          balance: 1000.00
        }
      };
    }

    // 4. Handle real operator authentication
    const operatorDetails = cacheManager.getOperatorDetails(operator_id, 'production');

    if (!operatorDetails) {
      throw createError(ERROR_TYPES.INVALID_OPERATOR, operator_id);
    }

    if (!operatorDetails.base_api_url) {
      throw createError(ERROR_TYPES.OPERATOR_CONFIG_MISSING, operator_id);
    }

    // 5. Call operator's session validation endpoint
    const operatorResponse = await validateSessionWithOperator(
      operatorDetails.base_api_url,
      session_id,
      external_user_id,
      operatorDetails.api_key
    );

    if (!operatorResponse.valid) {
      throw createError(ERROR_TYPES.OPERATOR_SESSION_INVALID, operator_id, session_id);
    }

    // 6. Log authentication event with comprehensive audit data
    const durationMs = Date.now() - startTime;
    await logSessionEvent(session_id, operator_id, 'authenticate', {
      external_user_id,
      game_code,
      authenticated: true,
      balance: operatorResponse.balance,
      currency: operatorResponse.currency,
      cache_source: 'database' // Indicates this was a cache miss
    }, {
      userId: session.user_id,
      status: 'success',
      requestData: { session_id, external_user_id, game_code },
      responseData: operatorResponse,
      durationMs
    });

    if (isDebug) {
      console.log('[SessionAuthenticateService] ✅ Session authenticated successfully');
    }

    return {
      session_id,
      user_id: session.user_id,
      operator_id,
      game_code: session.game_code || game_code,
      currency: operatorResponse.currency || session.currency || currency || 'USD',
      play_mode: session.play_mode || play_mode || 'real',
      balance: operatorResponse.balance || 0,
      authenticated: true,
      player_details: operatorResponse.player_details,
      operator_response: operatorResponse
    };

  } catch (error) {
    console.error('[SessionAuthenticateService] ❌ Authentication failed:', error);

    // Log failed authentication attempt asynchronously (non-blocking)
    logSessionEvent(session_id, operator_id, 'authenticate_failed', {
      external_user_id,
      game_code,
      authenticated: false,
      error_details: error instanceof Error ? error.stack : 'Unknown error'
    }, {
      status: 'failed',
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      requestData: { session_id, external_user_id, game_code }
    }).catch(logErr => console.warn('Failed to log authentication failure:', logErr.message));

    throw error;
  }
};

/**
 * Validate session with operator's API
 */
async function validateSessionWithOperator(
  baseApiUrl: string,
  sessionId: string,
  externalUserId: string,
  apiKey: string
): Promise<{
  valid: boolean;
  balance: number;
  currency: string;
  player_details: any;
}> {
  try {
    const response = await fetch(`${baseApiUrl}/session/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      },
      body: JSON.stringify({
        session_id: sessionId,
        external_user_id: externalUserId
      })
    });

    if (!response.ok) {
      throw new Error(`Operator API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as OperatorValidationResponse;

    if (isDebug) {
      console.log('[SessionAuthenticateService] 📡 Operator response:', data);
    }

    return {
      valid: data.valid || false,
      balance: data.balance || 0,
      currency: data.currency || 'USD',
      player_details: data.player_details || {}
    };

  } catch (error) {
    console.error('[SessionAuthenticateService] ❌ Operator API call failed:', error);

    // Return invalid for failed API calls
    return {
      valid: false,
      balance: 0,
      currency: 'USD',
      player_details: {}
    };
  }
}

/**
 * Enhanced session event logging with comprehensive audit data
 */
async function logSessionEvent(
  sessionId: string,
  operatorId: string,
  eventType: string,
  eventData: any,
  options: {
    userId?: string;
    status?: string;
    errorMessage?: string;
    requestData?: any;
    responseData?: any;
    durationMs?: number;
    ipAddress?: string;
    userAgent?: string;
  } = {}
): Promise<void> {
  try {
    const {
      userId,
      status = 'success',
      errorMessage,
      requestData,
      responseData,
      durationMs,
      ipAddress,
      userAgent
    } = options;

    // Determine event category based on event type
    let eventCategory = 'session';
    if (eventType.includes('authenticate')) {
      eventCategory = 'authentication';
    } else if (eventType.includes('balance') || eventType.includes('transaction')) {
      eventCategory = 'transaction';
    } else if (eventType.includes('operator')) {
      eventCategory = 'operator';
    } else if (eventType.includes('fraud') || eventType.includes('compliance')) {
      eventCategory = 'compliance';
    }

    await db.query(
      `INSERT INTO ${getTable('DataSessionEvents')}
       (session_id, operator_id, user_id, event_type, event_category, event_data,
        request_data, response_data, status, error_message, ip_address, user_agent,
        duration_ms, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW())`,
      [
        sessionId,
        operatorId,
        userId || null,
        eventType,
        eventCategory,
        JSON.stringify(eventData),
        requestData ? JSON.stringify(requestData) : null,
        responseData ? JSON.stringify(responseData) : null,
        status,
        errorMessage || null,
        ipAddress || null,
        userAgent || null,
        durationMs || null
      ]
    );

    if (isDebug) {
      console.log('[SessionAuthenticateService] 📝 Event logged:', {
        sessionId,
        eventType,
        eventCategory,
        status
      });
    }
  } catch (error) {
    console.error('[SessionAuthenticateService] ❌ Failed to log session event:', error);
    // Don't throw - logging failures shouldn't break authentication
  }
}
