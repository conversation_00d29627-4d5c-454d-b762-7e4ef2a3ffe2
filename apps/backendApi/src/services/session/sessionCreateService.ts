// /backendApi/services/session/sessionCreateService.ts

import { db, getTable, generateInsertQuery, generateInsertQueryWithConflict } from '@insolence-rgs/db';
import { v4 as uuidv4 } from 'uuid';
import { createError } from '@backendApiUtils/errors/errorFactory';
import { ERROR_TYPES } from '@backendApiUtils/errors/errorTypes';
import { cacheManager } from '@backendApiUtils/cacheManager';
import type { SessionCreateInput, SessionCreateResult } from '@backendApiTypes/zod/session';

/**
 * Generate game launch URL with session parameters
 */
function generateGameLaunchUrl(session_id: string, game_code: string, operator_id: string, play_mode: string): string {
  const baseUrl = process.env.GAMES_FRONTEND_URL || 'http://localhost:3001';
  const gameUrl = `${baseUrl}/${game_code}`;

  const params = new URLSearchParams({
    session_id,
    game_code,
    operator_id,
    play_mode,
    currency: 'USD', // Will be enhanced with actual currency from session
    game_mode: 'desktop',
    language: 'en'
  });

  return `${gameUrl}?${params.toString()}`;
}

export const sessionCreateService = async (validatedBody: SessionCreateInput, operator: any): Promise<SessionCreateResult & { launch_url: string }> => {
  const {
    operator_id,
    external_user_id,
    game_code,
    currency,
    play_mode,
    game_mode,
    token,
    platform,
    jurisdiction,
    meta,
    language,
    ip,
    country,
    lobby_url,
    cashier_url,
    promo
  } = validatedBody;

  const isDebug = process.env.LOG_LEVEL === 'debug';

  if (isDebug) {
    console.log('[SessionCreateService] 🎮 Creating session for game launch:', {
      operator_id,
      external_user_id,
      game_code,
      play_mode
    });
  }

  try {
    // Currency validation: Check operator-specific override first, then global default
    const operatorCurrency = cacheManager.getCurrency(operator_id, currency);
    const globalCurrency = cacheManager.getCurrency(undefined, currency); // Global lookup

    // Validate currency permissions using two-tier system
    if (operatorCurrency) {
      // Operator has specific currency configuration
      if (!operatorCurrency.isEnabled) {
        throw createError(ERROR_TYPES.INVALID_CURRENCY, operator_id, null, [currency]);
      }
    } else if (!globalCurrency?.isEnabledByDefault) {
      // No operator override, check global default
      throw createError(ERROR_TYPES.INVALID_CURRENCY, operator_id, null, [currency]);
    }

    const resolvedGame = cacheManager.getResolvedGame(operator_id, game_code);
    if (!resolvedGame) {
      throw createError(ERROR_TYPES.GAME_NOT_FOUND, operator_id, null, [game_code]);
    }

    const isOperatorScoped = resolvedGame.operator_id === operator_id;
    const isActive = resolvedGame.status === 'active';
    const isGloballyEnabled = resolvedGame.globally_enabled;

    if (isOperatorScoped && !isActive) {
      throw createError(ERROR_TYPES.GAME_DISABLED_FOR_OPERATOR, operator_id, null, [game_code]);
    }

    if (!isOperatorScoped && (!isActive || !isGloballyEnabled)) {
      throw createError(ERROR_TYPES.GAME_NOT_GLOBALLY_ENABLED, operator_id, null, [game_code]);
    }

    const operatorGameSettings = cacheManager.getResolvedGame(operator_id, game_code);
    const engine_path = operatorGameSettings?.engine_url || resolvedGame.default_engine_url;
    const animation_path = operatorGameSettings?.preloader_url || resolvedGame.animation_preloader_url;

    const userInsertQuery = generateInsertQueryWithConflict(
        'DataUsers',
        ['operator_id', 'external_user_id', 'currency'],
        ['operator_id', 'external_user_id', 'currency'],
        [],
        ['user_id'],
        { doNothingOnConflict: true }
    );

    const userInsert = await db.query(userInsertQuery, [operator_id, external_user_id, currency]);

    let user_id: string;
    if (userInsert.rows.length) {
      user_id = userInsert.rows[0].user_id;
    } else {
      const userRes = await db.query(
          `SELECT user_id FROM ${getTable('DataUsers')} WHERE operator_id = $1 AND external_user_id = $2 AND currency = $3`,
          [operator_id, external_user_id, currency]
      );
      if (!userRes.rowCount) {
        throw createError(ERROR_TYPES.USER_LOOKUP_FAILED, operator_id);
      }
      user_id = userRes.rows[0].user_id;
    }

    const existingSession = await db.query(
        `SELECT * FROM ${getTable('DataGameSessions')}
         WHERE operator_id = $1 AND user_id = $2 AND game_code = $3 AND currency = $4 AND status = 'open'`,
        [operator_id, user_id, game_code, currency]
    );

    if (existingSession.rowCount) {
      if (process.env.LOG_LEVEL === 'debug') {
        console.log('[SessionService] ♻️ Reusing existing session:', existingSession.rows[0]);
      }
      return existingSession.rows[0];
    }

    const session_id = uuidv4();
    const insertData = {
      session_id,
      operator_id,
      user_id,
      game_code,
      play_mode,
      game_mode,
      currency,
      language,
      ip,
      country,
      token,
      platform,
      jurisdiction,
      metadata: meta || null,
      lobby_url,
      cashier_url,
      promo_eligible: promo,
      engine_path,
      animation_path,
      status: 'open'
    };

    await db.query(
        generateInsertQuery('DataGameSessions', Object.keys(insertData)),
        Object.values(insertData)
    );

    // Generate launch URL for frontend redirection
    const launch_url = generateGameLaunchUrl(session_id, game_code, operator_id, play_mode);

    if (isDebug) {
      console.log('[SessionCreateService] ✅ Session created successfully:', {
        session_id,
        game_code,
        operator_id,
        play_mode,
        launch_url
      });
    }

    return {
      session_id,
      game_code,
      operator_id,
      currency,
      play_mode,
      launch_url
    };

  } catch (err) {
    console.error('[sessionCreateService] ❌ Error:', err);
    throw err;
  }
};
