// apps/backendApi/src/services/transaction/transactionManager.ts
// Transaction management service with idempotency handling

import { redis } from '@insolence-rgs/cache-main';
import { db } from '@insolence-rgs/db';
import type { 
  BetCallbackPayload, 
  WinCallbackPayload, 
  PromoWinCallbackPayload 
} from '@backendApiTypes/zod/operatorCallbacks';

export interface TransactionRecord {
  transaction_id: string;
  operator_transaction_id?: string;
  new_balance?: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at: Date;
  completed_at?: Date;
}

export interface OperatorResponse {
  success: boolean;
  new_balance: number;
  operator_transaction_id: string;
  message?: string;
}

export class TransactionManager {
  private readonly CACHE_TTL = 86400; // 24 hours for transaction idempotency

  /**
   * Check if transaction already exists (idempotency check)
   */
  async checkDuplicateTransaction(
    transactionId: string,
    operatorId: string,
    transactionType: 'bet' | 'win' | 'promo_win'
  ): Promise<TransactionRecord | null> {
    try {
      // Check Redis cache first for fast lookup
      const cacheKey = `transaction:${operatorId}:${transactionId}`;
      const cachedResult = await redis.get(cacheKey);
      
      if (cachedResult) {
        console.log(`[TransactionManager] Cache hit for transaction: ${transactionId}`);
        return JSON.parse(cachedResult);
      }

      // Check database if not in cache
      const query = `
        SELECT transaction_id, operator_transaction_id, new_balance, status, 
               created_at, completed_at
        FROM data.operator_transactions 
        WHERE transaction_id = $1 AND operator_id = $2 AND transaction_type = $3
        LIMIT 1
      `;
      
      const result = await db.query(query, [transactionId, operatorId, transactionType]);
      
      if (result.rows.length > 0) {
        const transaction = result.rows[0];
        
        // Cache the result for future lookups
        await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(transaction));
        
        return transaction;
      }

      return null;

    } catch (error) {
      console.error('[TransactionManager] Error checking duplicate transaction:', error);
      // Return null to allow transaction to proceed rather than fail
      return null;
    }
  }

  /**
   * Record bet transaction in database
   */
  async recordBetTransaction(
    betData: BetCallbackPayload,
    operatorResponse: OperatorResponse
  ): Promise<TransactionRecord> {
    try {
      const query = `
        INSERT INTO data.operator_transactions (
          transaction_id, round_id, session_id, user_id, operator_id,
          transaction_type, amount, currency, game_code, status,
          operator_response, operator_transaction_id, new_balance,
          created_at, completed_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW()
        )
        RETURNING transaction_id, operator_transaction_id, new_balance, 
                 status, created_at, completed_at
      `;

      const values = [
        betData.transaction_id,
        betData.round_id,
        betData.session_id,
        betData.user_id,
        betData.operator_id,
        'bet',
        betData.bet_amount,
        betData.currency,
        betData.game_code,
        operatorResponse.success ? 'completed' : 'failed',
        JSON.stringify(operatorResponse),
        operatorResponse.operator_transaction_id,
        operatorResponse.new_balance
      ];

      const result = await db.query(query, values);
      const transaction = result.rows[0];

      // Cache the transaction for idempotency
      const cacheKey = `transaction:${betData.operator_id}:${betData.transaction_id}`;
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(transaction));

      console.log(`[TransactionManager] Bet transaction recorded: ${betData.transaction_id}`);
      return transaction;

    } catch (error) {
      console.error('[TransactionManager] Error recording bet transaction:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to record bet transaction: ${errorMessage}`);
    }
  }

  /**
   * Record win transaction in database
   */
  async recordWinTransaction(
    winData: WinCallbackPayload,
    operatorResponse: OperatorResponse
  ): Promise<TransactionRecord> {
    try {
      const query = `
        INSERT INTO data.operator_transactions (
          transaction_id, round_id, session_id, user_id, operator_id,
          transaction_type, amount, currency, game_code, status,
          operator_response, operator_transaction_id, new_balance,
          related_transaction_id, created_at, completed_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()
        )
        RETURNING transaction_id, operator_transaction_id, new_balance, 
                 status, created_at, completed_at
      `;

      const values = [
        winData.transaction_id,
        winData.round_id,
        winData.session_id,
        winData.user_id,
        winData.operator_id,
        'win',
        winData.win_amount,
        winData.currency,
        winData.game_code,
        operatorResponse.success ? 'completed' : 'failed',
        JSON.stringify(operatorResponse),
        operatorResponse.operator_transaction_id,
        operatorResponse.new_balance,
        winData.related_bet_transaction_id
      ];

      const result = await db.query(query, values);
      const transaction = result.rows[0];

      // Cache the transaction for idempotency
      const cacheKey = `transaction:${winData.operator_id}:${winData.transaction_id}`;
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(transaction));

      console.log(`[TransactionManager] Win transaction recorded: ${winData.transaction_id}`);
      return transaction;

    } catch (error) {
      console.error('[TransactionManager] Error recording win transaction:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to record win transaction: ${errorMessage}`);
    }
  }

  /**
   * Record promotional win transaction in database
   */
  async recordPromoWinTransaction(
    promoData: PromoWinCallbackPayload,
    operatorResponse: OperatorResponse
  ): Promise<TransactionRecord> {
    try {
      const query = `
        INSERT INTO data.operator_transactions (
          transaction_id, round_id, session_id, user_id, operator_id,
          transaction_type, amount, currency, game_code, status,
          operator_response, operator_transaction_id, new_balance,
          promotion_type, tournament_id, created_at, completed_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW()
        )
        RETURNING transaction_id, operator_transaction_id, new_balance, 
                 status, created_at, completed_at
      `;

      const values = [
        promoData.transaction_id,
        promoData.round_id,
        promoData.session_id,
        promoData.user_id,
        promoData.operator_id,
        'promo_win',
        promoData.promo_amount,
        promoData.currency,
        promoData.game_code,
        operatorResponse.success ? 'completed' : 'failed',
        JSON.stringify(operatorResponse),
        operatorResponse.operator_transaction_id,
        operatorResponse.new_balance,
        promoData.promotion_type,
        promoData.tournament_id
      ];

      const result = await db.query(query, values);
      const transaction = result.rows[0];

      // Cache the transaction for idempotency
      const cacheKey = `transaction:${promoData.operator_id}:${promoData.transaction_id}`;
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(transaction));

      console.log(`[TransactionManager] Promo win transaction recorded: ${promoData.transaction_id}`);
      return transaction;

    } catch (error) {
      console.error('[TransactionManager] Error recording promo win transaction:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to record promo win transaction: ${errorMessage}`);
    }
  }

  /**
   * Get transaction status for monitoring
   */
  async getTransactionStatus(transactionId: string, operatorId: string): Promise<TransactionRecord | null> {
    try {
      const query = `
        SELECT transaction_id, operator_transaction_id, new_balance, status, 
               created_at, completed_at, retry_count
        FROM data.operator_transactions 
        WHERE transaction_id = $1 AND operator_id = $2
        LIMIT 1
      `;
      
      const result = await db.query(query, [transactionId, operatorId]);
      
      return result.rows.length > 0 ? result.rows[0] : null;

    } catch (error) {
      console.error('[TransactionManager] Error getting transaction status:', error);
      return null;
    }
  }

  /**
   * Update transaction status (for retry scenarios)
   */
  async updateTransactionStatus(
    transactionId: string,
    operatorId: string,
    status: 'pending' | 'completed' | 'failed' | 'cancelled',
    retryCount?: number
  ): Promise<void> {
    try {
      const query = `
        UPDATE data.operator_transactions 
        SET status = $1, retry_count = COALESCE($2, retry_count), 
            completed_at = CASE WHEN $1 IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE completed_at END
        WHERE transaction_id = $3 AND operator_id = $4
      `;
      
      await db.query(query, [status, retryCount, transactionId, operatorId]);

      // Invalidate cache
      const cacheKey = `transaction:${operatorId}:${transactionId}`;
      await redis.del(cacheKey);

      console.log(`[TransactionManager] Transaction status updated: ${transactionId} -> ${status}`);

    } catch (error) {
      console.error('[TransactionManager] Error updating transaction status:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to update transaction status: ${errorMessage}`);
    }
  }

  /**
   * Get transaction metrics for monitoring
   */
  async getTransactionMetrics(operatorId: string, timeframe: 'hour' | 'day' | 'week' = 'hour'): Promise<{
    total_transactions: number;
    successful_transactions: number;
    failed_transactions: number;
    total_amount: number;
    success_rate: number;
  }> {
    try {
      const intervals = {
        hour: '1 hour',
        day: '1 day',
        week: '1 week'
      };

      const query = `
        SELECT 
          COUNT(*) as total_transactions,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_transactions,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
          COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_amount
        FROM data.operator_transactions 
        WHERE operator_id = $1 
          AND created_at > NOW() - INTERVAL '${intervals[timeframe]}'
      `;
      
      const result = await db.query(query, [operatorId]);
      const metrics = result.rows[0];

      const successRate = metrics.total_transactions > 0 
        ? (metrics.successful_transactions / metrics.total_transactions) * 100 
        : 0;

      return {
        total_transactions: parseInt(metrics.total_transactions),
        successful_transactions: parseInt(metrics.successful_transactions),
        failed_transactions: parseInt(metrics.failed_transactions),
        total_amount: parseFloat(metrics.total_amount),
        success_rate: Math.round(successRate * 100) / 100
      };

    } catch (error) {
      console.error('[TransactionManager] Error getting transaction metrics:', error);
      return {
        total_transactions: 0,
        successful_transactions: 0,
        failed_transactions: 0,
        total_amount: 0,
        success_rate: 0
      };
    }
  }
}
