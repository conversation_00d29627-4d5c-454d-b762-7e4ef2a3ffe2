// apps/backendApi/src/services/audit/auditLogger.ts
// Audit logging service for operator callback transactions

import { db } from '@insolence-rgs/db';

export interface BetPlacementEvent {
  round_id: string;
  session_id: string;
  user_id: string;
  operator_id: string;
  bet_amount: number;
  currency: string;
  game_code: string;
  transaction_id: string;
}

export interface WinPayoutEvent {
  round_id: string;
  session_id: string;
  user_id: string;
  operator_id: string;
  win_amount: number;
  currency: string;
  game_code: string;
  transaction_id: string;
  related_bet_transaction_id: string;
}

export interface PromoWinEvent {
  round_id: string;
  session_id: string;
  user_id: string;
  operator_id: string;
  promo_amount: number;
  currency: string;
  game_code: string;
  transaction_id: string;
  promotion_type: string;
  tournament_id?: string;
}

export class AuditLogger {
  /**
   * Log bet placement event
   */
  async logBetPlacement(event: BetPlacementEvent): Promise<void> {
    try {
      // Log to financial events table
      await db.query(`
        INSERT INTO audit.financial_events (
          event_type, round_id, session_id, user_id, operator_id, 
          amount, currency, game_code, transaction_id, created_at, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
      `, [
        'bet_placed',
        event.round_id,
        event.session_id,
        event.user_id,
        event.operator_id,
        event.bet_amount,
        event.currency,
        event.game_code,
        event.transaction_id,
        JSON.stringify({
          timestamp: new Date().toISOString(),
          source: 'operator_callback',
          event_version: '1.0'
        })
      ]);

      // Log to session events for session tracking
      await db.query(`
        INSERT INTO data.session_events (
          session_id, event_type, event_data, created_at
        ) VALUES ($1, $2, $3, NOW())
      `, [
        event.session_id,
        'bet_placed',
        JSON.stringify({
          round_id: event.round_id,
          transaction_id: event.transaction_id,
          bet_amount: event.bet_amount,
          currency: event.currency,
          game_code: event.game_code
        })
      ]);

      console.log(`[AuditLogger] Bet placement logged: ${event.transaction_id}`);

    } catch (error) {
      console.error('[AuditLogger] Error logging bet placement:', error);
      // Don't throw error to avoid breaking the main transaction flow
    }
  }

  /**
   * Log win payout event
   */
  async logWinPayout(event: WinPayoutEvent): Promise<void> {
    try {
      // Log to financial events table
      await db.query(`
        INSERT INTO audit.financial_events (
          event_type, round_id, session_id, user_id, operator_id, 
          amount, currency, game_code, transaction_id, related_transaction_id, 
          created_at, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), $11)
      `, [
        'win_paid',
        event.round_id,
        event.session_id,
        event.user_id,
        event.operator_id,
        event.win_amount,
        event.currency,
        event.game_code,
        event.transaction_id,
        event.related_bet_transaction_id,
        JSON.stringify({
          timestamp: new Date().toISOString(),
          source: 'operator_callback',
          event_version: '1.0'
        })
      ]);

      // Log to session events for session tracking
      await db.query(`
        INSERT INTO data.session_events (
          session_id, event_type, event_data, created_at
        ) VALUES ($1, $2, $3, NOW())
      `, [
        event.session_id,
        'win_paid',
        JSON.stringify({
          round_id: event.round_id,
          transaction_id: event.transaction_id,
          related_bet_transaction_id: event.related_bet_transaction_id,
          win_amount: event.win_amount,
          currency: event.currency,
          game_code: event.game_code
        })
      ]);

      console.log(`[AuditLogger] Win payout logged: ${event.transaction_id}`);

    } catch (error) {
      console.error('[AuditLogger] Error logging win payout:', error);
      // Don't throw error to avoid breaking the main transaction flow
    }
  }

  /**
   * Log promotional win event
   */
  async logPromoWin(event: PromoWinEvent): Promise<void> {
    try {
      // Log to financial events table
      await db.query(`
        INSERT INTO audit.financial_events (
          event_type, round_id, session_id, user_id, operator_id, 
          amount, currency, game_code, transaction_id, created_at, metadata
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
      `, [
        'promo_win_paid',
        event.round_id,
        event.session_id,
        event.user_id,
        event.operator_id,
        event.promo_amount,
        event.currency,
        event.game_code,
        event.transaction_id,
        JSON.stringify({
          timestamp: new Date().toISOString(),
          source: 'operator_callback',
          promotion_type: event.promotion_type,
          tournament_id: event.tournament_id,
          event_version: '1.0'
        })
      ]);

      // Log to session events for session tracking
      await db.query(`
        INSERT INTO data.session_events (
          session_id, event_type, event_data, created_at
        ) VALUES ($1, $2, $3, NOW())
      `, [
        event.session_id,
        'promo_win_paid',
        JSON.stringify({
          round_id: event.round_id,
          transaction_id: event.transaction_id,
          promo_amount: event.promo_amount,
          currency: event.currency,
          game_code: event.game_code,
          promotion_type: event.promotion_type,
          tournament_id: event.tournament_id
        })
      ]);

      console.log(`[AuditLogger] Promotional win logged: ${event.transaction_id}`);

    } catch (error) {
      console.error('[AuditLogger] Error logging promotional win:', error);
      // Don't throw error to avoid breaking the main transaction flow
    }
  }

  /**
   * Log security event for operator callbacks
   */
  async logSecurityEvent(
    operatorId: string,
    eventType: string,
    details: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      await db.query(`
        INSERT INTO audit.security_events (
          event_type, operator_id, ip_address, user_agent, success, 
          metadata, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
      `, [
        eventType,
        operatorId,
        ipAddress || null,
        userAgent || null,
        details.success || false,
        JSON.stringify({
          ...details,
          timestamp: new Date().toISOString(),
          source: 'operator_callback'
        })
      ]);

      console.log(`[AuditLogger] Security event logged: ${eventType} for operator ${operatorId}`);

    } catch (error) {
      console.error('[AuditLogger] Error logging security event:', error);
    }
  }

  /**
   * Log operator callback failure for monitoring
   */
  async logCallbackFailure(
    operatorId: string,
    transactionId: string,
    errorType: string,
    errorMessage: string,
    requestData: Record<string, any>
  ): Promise<void> {
    try {
      await db.query(`
        INSERT INTO audit.callback_failures (
          operator_id, transaction_id, error_type, error_message, 
          request_data, created_at
        ) VALUES ($1, $2, $3, $4, $5, NOW())
      `, [
        operatorId,
        transactionId,
        errorType,
        errorMessage,
        JSON.stringify(requestData)
      ]);

      console.log(`[AuditLogger] Callback failure logged: ${transactionId} - ${errorType}`);

    } catch (error) {
      console.error('[AuditLogger] Error logging callback failure:', error);
    }
  }

  /**
   * Get audit trail for a specific transaction
   */
  async getTransactionAuditTrail(transactionId: string): Promise<any[]> {
    try {
      const query = `
        SELECT 
          'financial_event' as source,
          event_type,
          created_at,
          metadata,
          operator_id,
          amount,
          currency
        FROM audit.financial_events 
        WHERE transaction_id = $1
        
        UNION ALL
        
        SELECT 
          'session_event' as source,
          event_type,
          created_at,
          event_data as metadata,
          NULL as operator_id,
          NULL as amount,
          NULL as currency
        FROM data.session_events 
        WHERE event_data::jsonb ->> 'transaction_id' = $1
        
        ORDER BY created_at ASC
      `;

      const result = await db.query(query, [transactionId]);
      return result.rows;

    } catch (error) {
      console.error('[AuditLogger] Error getting audit trail:', error);
      return [];
    }
  }

  /**
   * Get operator callback metrics for monitoring
   */
  async getCallbackMetrics(
    operatorId: string, 
    timeframe: 'hour' | 'day' | 'week' = 'hour'
  ): Promise<{
    total_callbacks: number;
    successful_callbacks: number;
    failed_callbacks: number;
    bet_callbacks: number;
    win_callbacks: number;
    promo_callbacks: number;
  }> {
    try {
      const intervals = {
        hour: '1 hour',
        day: '1 day',
        week: '1 week'
      };

      const query = `
        SELECT 
          COUNT(*) as total_callbacks,
          COUNT(CASE WHEN event_type LIKE '%_placed' OR event_type LIKE '%_paid' THEN 1 END) as successful_callbacks,
          COUNT(CASE WHEN event_type = 'bet_placed' THEN 1 END) as bet_callbacks,
          COUNT(CASE WHEN event_type = 'win_paid' THEN 1 END) as win_callbacks,
          COUNT(CASE WHEN event_type = 'promo_win_paid' THEN 1 END) as promo_callbacks
        FROM audit.financial_events 
        WHERE operator_id = $1 
          AND created_at > NOW() - INTERVAL '${intervals[timeframe]}'
          AND metadata::jsonb ->> 'source' = 'operator_callback'
      `;

      const result = await db.query(query, [operatorId]);
      const metrics = result.rows[0];

      // Get failure count from callback_failures table
      const failureQuery = `
        SELECT COUNT(*) as failed_callbacks
        FROM audit.callback_failures 
        WHERE operator_id = $1 
          AND created_at > NOW() - INTERVAL '${intervals[timeframe]}'
      `;

      const failureResult = await db.query(failureQuery, [operatorId]);
      const failedCallbacks = parseInt(failureResult.rows[0].failed_callbacks);

      return {
        total_callbacks: parseInt(metrics.total_callbacks) + failedCallbacks,
        successful_callbacks: parseInt(metrics.successful_callbacks),
        failed_callbacks: failedCallbacks,
        bet_callbacks: parseInt(metrics.bet_callbacks),
        win_callbacks: parseInt(metrics.win_callbacks),
        promo_callbacks: parseInt(metrics.promo_callbacks)
      };

    } catch (error) {
      console.error('[AuditLogger] Error getting callback metrics:', error);
      return {
        total_callbacks: 0,
        successful_callbacks: 0,
        failed_callbacks: 0,
        bet_callbacks: 0,
        win_callbacks: 0,
        promo_callbacks: 0
      };
    }
  }
}
