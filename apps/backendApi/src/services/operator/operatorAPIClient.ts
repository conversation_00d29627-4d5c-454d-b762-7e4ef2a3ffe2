// apps/backendApi/src/services/operator/operatorAPIClient.ts
// Operator API client with retry logic and circuit breaker pattern

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type { 
  BetCallbackPayload, 
  WinCallbackPayload, 
  PromoWinCallbackPayload 
} from '@backendApiTypes/zod/operatorCallbacks';
import type { OperatorResponse } from '@backendApiServices/transaction/transactionManager';

interface OperatorConfig {
  operator_id: string;
  base_api_url: string;
  api_key: string;
  private_key: string;
  timeout: number;
  max_retries: number;
}

interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

export class OperatorAPIClient {
  private operatorClients = new Map<string, AxiosInstance>();
  private circuitBreakers = new Map<string, CircuitBreakerState>();
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second base delay
  private readonly circuitBreakerThreshold = 5; // failures before opening circuit
  private readonly circuitBreakerTimeout = 60000; // 1 minute before trying again

  /**
   * Get or create operator-specific HTTP client
   */
  private async getOperatorClient(operatorId: string): Promise<AxiosInstance> {
    if (this.operatorClients.has(operatorId)) {
      return this.operatorClients.get(operatorId)!;
    }

    const operatorConfig = await this.getOperatorConfig(operatorId);
    
    const client = axios.create({
      baseURL: operatorConfig.base_api_url,
      timeout: operatorConfig.timeout || 5000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${operatorConfig.api_key}`,
        'X-RGS-Version': '1.0',
        'User-Agent': 'InsolenceRGS/1.0'
      }
    });

    // Add request interceptor for signing
    client.interceptors.request.use(async (config) => {
      if (config.data) {
        const signature = await this.signRequest(config.data, operatorConfig.private_key);
        config.headers['X-RGS-Signature'] = signature;
        config.headers['X-RGS-Timestamp'] = Date.now().toString();
      }
      return config;
    });

    // Add response interceptor for error handling
    client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error(`[OperatorAPI] Request failed for operator ${operatorId}:`, error.message);
        return Promise.reject(error);
      }
    );

    this.operatorClients.set(operatorId, client);
    return client;
  }

  /**
   * Check circuit breaker state
   */
  private checkCircuitBreaker(operatorId: string): boolean {
    const breaker = this.circuitBreakers.get(operatorId);
    
    if (!breaker) {
      this.circuitBreakers.set(operatorId, {
        failures: 0,
        lastFailureTime: 0,
        state: 'CLOSED'
      });
      return true; // Allow request
    }

    const now = Date.now();

    switch (breaker.state) {
      case 'CLOSED':
        return true; // Allow request

      case 'OPEN':
        if (now - breaker.lastFailureTime > this.circuitBreakerTimeout) {
          breaker.state = 'HALF_OPEN';
          return true; // Allow one test request
        }
        return false; // Block request

      case 'HALF_OPEN':
        return true; // Allow test request

      default:
        return true;
    }
  }

  /**
   * Update circuit breaker state based on request result
   */
  private updateCircuitBreaker(operatorId: string, success: boolean): void {
    let breaker = this.circuitBreakers.get(operatorId);
    
    if (!breaker) {
      breaker = { failures: 0, lastFailureTime: 0, state: 'CLOSED' };
      this.circuitBreakers.set(operatorId, breaker);
    }

    if (success) {
      breaker.failures = 0;
      breaker.state = 'CLOSED';
    } else {
      breaker.failures++;
      breaker.lastFailureTime = Date.now();
      
      if (breaker.failures >= this.circuitBreakerThreshold) {
        breaker.state = 'OPEN';
        console.warn(`[OperatorAPI] Circuit breaker opened for operator ${operatorId}`);
      }
    }
  }

  /**
   * Make API call with retry logic and circuit breaker
   */
  private async callOperatorAPI(
    operatorId: string,
    endpoint: string,
    data: any
  ): Promise<AxiosResponse> {
    // Check circuit breaker
    if (!this.checkCircuitBreaker(operatorId)) {
      throw new Error(`Circuit breaker is OPEN for operator ${operatorId}`);
    }

    const client = await this.getOperatorClient(operatorId);
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`[OperatorAPI] Attempt ${attempt}/${this.maxRetries} for operator ${operatorId}`);
        
        const response = await client.post(endpoint, data);
        
        // Success - update circuit breaker
        this.updateCircuitBreaker(operatorId, true);
        
        return response;

      } catch (error) {
        lastError = error as Error;
        
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`[OperatorAPI] Attempt ${attempt} failed for operator ${operatorId}:`, errorMessage);
        
        // Don't retry on client errors (4xx)
        if (axios.isAxiosError(error) && error.response?.status && error.response.status < 500) {
          break;
        }
        
        // Wait before retry (exponential backoff)
        if (attempt < this.maxRetries) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1);
          await this.delay(delay);
        }
      }
    }

    // All attempts failed - update circuit breaker
    this.updateCircuitBreaker(operatorId, false);
    
    const errorMessage = lastError?.message || 'Unknown error';
    throw new Error(`Operator API call failed after ${this.maxRetries} attempts: ${errorMessage}`);
  }

  /**
   * Process bet deduction with operator
   */
  async processBetDeduction(betData: BetCallbackPayload): Promise<OperatorResponse> {
    try {
      const endpoint = '/api/rgs/bet';
      const payload = {
        transaction_id: betData.transaction_id,
        user_id: betData.user_id,
        session_id: betData.session_id,
        game_code: betData.game_code,
        round_id: betData.round_id,
        bet_amount: betData.bet_amount,
        currency: betData.currency,
        timestamp: betData.timestamp,
        metadata: betData.bet_metadata
      };

      const response = await this.callOperatorAPI(betData.operator_id, endpoint, payload);
      
      return {
        success: response.data.success || false,
        new_balance: response.data.balance || 0,
        operator_transaction_id: response.data.transaction_id || betData.transaction_id,
        message: response.data.message
      };

    } catch (error) {
      console.error('[OperatorAPI] Bet deduction failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Bet deduction failed: ${errorMessage}`);
    }
  }

  /**
   * Process win credit with operator
   */
  async processWinCredit(winData: WinCallbackPayload): Promise<OperatorResponse> {
    try {
      const endpoint = '/api/rgs/win';
      const payload = {
        transaction_id: winData.transaction_id,
        user_id: winData.user_id,
        session_id: winData.session_id,
        game_code: winData.game_code,
        round_id: winData.round_id,
        win_amount: winData.win_amount,
        currency: winData.currency,
        related_bet_transaction_id: winData.related_bet_transaction_id,
        timestamp: winData.timestamp,
        metadata: winData.win_metadata
      };

      const response = await this.callOperatorAPI(winData.operator_id, endpoint, payload);
      
      return {
        success: response.data.success || false,
        new_balance: response.data.balance || 0,
        operator_transaction_id: response.data.transaction_id || winData.transaction_id,
        message: response.data.message
      };

    } catch (error) {
      console.error('[OperatorAPI] Win credit failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Win credit failed: ${errorMessage}`);
    }
  }

  /**
   * Process promotional win credit with operator
   */
  async processPromoWinCredit(promoData: PromoWinCallbackPayload): Promise<OperatorResponse> {
    try {
      const endpoint = '/api/rgs/promo-win';
      const payload = {
        transaction_id: promoData.transaction_id,
        user_id: promoData.user_id,
        session_id: promoData.session_id,
        game_code: promoData.game_code,
        round_id: promoData.round_id,
        promo_amount: promoData.promo_amount,
        currency: promoData.currency,
        promotion_type: promoData.promotion_type,
        tournament_id: promoData.tournament_id,
        timestamp: promoData.timestamp,
        metadata: promoData.promo_metadata
      };

      const response = await this.callOperatorAPI(promoData.operator_id, endpoint, payload);
      
      return {
        success: response.data.success || false,
        new_balance: response.data.balance || 0,
        operator_transaction_id: response.data.transaction_id || promoData.transaction_id,
        message: response.data.message
      };

    } catch (error) {
      console.error('[OperatorAPI] Promo win credit failed:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Promo win credit failed: ${errorMessage}`);
    }
  }

  /**
   * Get operator configuration
   */
  private async getOperatorConfig(operatorId: string): Promise<OperatorConfig> {
    // This would typically fetch from database or cache
    // For now, return a mock configuration
    return {
      operator_id: operatorId,
      base_api_url: `https://api.operator-${operatorId}.com`,
      api_key: 'mock-api-key',
      private_key: 'mock-private-key',
      timeout: 5000,
      max_retries: 3
    };
  }

  /**
   * Sign request payload
   */
  private async signRequest(payload: any, privateKey: string): Promise<string> {
    // This would implement actual cryptographic signing
    // For now, return a mock signature
    const payloadString = JSON.stringify(payload);
    return `mock-signature-${Buffer.from(payloadString).toString('base64').slice(0, 16)}`;
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get circuit breaker status for monitoring
   */
  getCircuitBreakerStatus(operatorId: string): CircuitBreakerState | null {
    return this.circuitBreakers.get(operatorId) || null;
  }

  /**
   * Reset circuit breaker (for manual intervention)
   */
  resetCircuitBreaker(operatorId: string): void {
    this.circuitBreakers.set(operatorId, {
      failures: 0,
      lastFailureTime: 0,
      state: 'CLOSED'
    });
    console.log(`[OperatorAPI] Circuit breaker reset for operator ${operatorId}`);
  }
}
