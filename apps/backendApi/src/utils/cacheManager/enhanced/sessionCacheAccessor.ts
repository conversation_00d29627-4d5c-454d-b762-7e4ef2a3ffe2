/**
 * Enhanced Session Cache Accessor for Backend API
 * Provides high-performance session operations with Redis fallback to database
 */

import { redis, redisKeys } from '@insolence-rgs/cache-main';
import { db } from '@insolence-rgs/db';

export interface SessionCacheData {
  session_id: string;
  operator_id: string;
  user_id: string;
  game_code: string;
  currency: string;
  play_mode: 'real' | 'demo';
  status: 'open' | 'closed' | 'suspended';
  balance?: number;
  metadata?: any;
  created_at: string;
  last_activity: string;
}

export class SessionCacheAccessor {
  private static readonly CACHE_TIMEOUT = 2000; // 2 seconds timeout for cache operations
  private static readonly DB_TIMEOUT = 5000; // 5 seconds timeout for database operations

  /**
   * Fast session authentication using Redis cache (simplified)
   */
  static async authenticateSession(sessionId: string): Promise<{
    authenticated: boolean;
    session?: SessionCacheData;
    source: 'cache' | 'database' | 'not_found';
  }> {
    try {
      // Try Redis cache first
      const cachedSession = await this.getSessionFromCache(sessionId);

      if (cachedSession) {
        // Update activity timestamp (non-blocking)
        this.updateSessionActivity(sessionId).catch(err =>
          console.warn('Failed to update session activity:', err.message)
        );

        return {
          authenticated: true,
          session: cachedSession,
          source: 'cache'
        };
      }

      // Fallback to database
      const dbSession = await this.getSessionFromDatabase(sessionId);

      if (dbSession) {
        // Cache the session for future requests (non-blocking)
        this.cacheSession(dbSession).catch(err =>
          console.warn('Failed to cache session:', err.message)
        );

        return {
          authenticated: true,
          session: dbSession,
          source: 'database'
        };
      }

      return {
        authenticated: false,
        source: 'not_found'
      };

    } catch (error) {
      console.error('❌ Session authentication error:', error);
      return {
        authenticated: false,
        source: 'not_found'
      };
    }
  }



  /**
   * Get session from Redis cache
   */
  private static async getSessionFromCache(sessionId: string): Promise<SessionCacheData | null> {
    try {
      const sessionKey = redisKeys.sessionById(sessionId);
      const sessionData = await redis.hgetall(sessionKey);
      
      if (!sessionData || !sessionData.session_id) {
        return null;
      }
      
      return {
        session_id: sessionData.session_id,
        operator_id: sessionData.operator_id,
        user_id: sessionData.user_id,
        game_code: sessionData.game_code,
        currency: sessionData.currency,
        play_mode: sessionData.play_mode as 'real' | 'demo',
        status: sessionData.status as 'open' | 'closed' | 'suspended',
        balance: sessionData.balance ? parseFloat(sessionData.balance) : undefined,
        metadata: sessionData.metadata ? JSON.parse(sessionData.metadata) : undefined,
        created_at: sessionData.created_at,
        last_activity: sessionData.last_activity
      };
    } catch (error) {
      console.error('❌ Error getting session from cache:', error);
      return null;
    }
  }

  /**
   * Get session from database
   */
  private static async getSessionFromDatabase(sessionId: string): Promise<SessionCacheData | null> {
    try {
      const query = `
        SELECT 
          session_id,
          operator_id,
          user_id,
          game_code,
          currency,
          play_mode,
          status,
          balance,
          metadata,
          started_at as created_at
        FROM data.game_sessions 
        WHERE session_id = $1 AND status = 'open'
      `;
      
      const result = await db.query(query, [sessionId]);
      
      if (!result.rows.length) {
        return null;
      }
      
      const row = result.rows[0];
      return {
        session_id: row.session_id,
        operator_id: row.operator_id,
        user_id: row.user_id,
        game_code: row.game_code,
        currency: row.currency,
        play_mode: row.play_mode,
        status: row.status,
        balance: row.balance,
        metadata: row.metadata,
        created_at: row.created_at,
        last_activity: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting session from database:', error);
      return null;
    }
  }

  /**
   * Cache session data in Redis
   */
  private static async cacheSession(session: SessionCacheData): Promise<void> {
    try {
      const sessionKey = redisKeys.sessionById(session.session_id);
      const userSessionsKey = redisKeys.userActiveSessions(session.user_id);
      const operatorSessionsKey = redisKeys.operatorActiveSessions(session.operator_id);
      
      // Use pipeline for atomic operations
      const pipeline = redis.pipeline();
      
      // Cache session details
      pipeline.hset(sessionKey, {
        session_id: session.session_id,
        operator_id: session.operator_id,
        user_id: session.user_id,
        game_code: session.game_code,
        currency: session.currency,
        play_mode: session.play_mode,
        status: session.status,
        balance: session.balance || 0,
        metadata: JSON.stringify(session.metadata || {}),
        created_at: session.created_at,
        last_activity: session.last_activity
      });
      
      // Add to user's active sessions
      pipeline.sadd(userSessionsKey, session.session_id);
      
      // Add to operator's active sessions
      pipeline.sadd(operatorSessionsKey, session.session_id);
      
      // Set TTLs
      pipeline.expire(sessionKey, 86400); // 24 hours
      pipeline.expire(userSessionsKey, 86400);
      pipeline.expire(operatorSessionsKey, 7200); // 2 hours
      
      await pipeline.exec();
    } catch (error) {
      console.error('❌ Error caching session:', error);
    }
  }

  /**
   * Update session activity timestamp
   */
  private static async updateSessionActivity(sessionId: string): Promise<void> {
    try {
      const sessionKey = redisKeys.sessionById(sessionId);
      await redis.hset(sessionKey, 'last_activity', new Date().toISOString());
    } catch (error) {
      console.error('❌ Error updating session activity:', error);
    }
  }

  /**
   * Check if operator supports game (cached lookup)
   */
  static async isGameSupported(operatorId: string, gameCode: string): Promise<boolean> {
    try {
      // Try cache first
      const gamePermissionsKey = redisKeys.operatorGamePermissions(operatorId);
      const isSupported = await redis.sismember(gamePermissionsKey, gameCode);
      
      if (isSupported === 1) {
        return true;
      }
      
      // Fallback to database
      const query = `
        SELECT 1 FROM config.games g
        LEFT JOIN config.operator_game_settings ogs 
          ON g.game_code = ogs.game_code AND ogs.operator_id = $1
        WHERE g.game_code = $2 
          AND g.status = 'active'
          AND COALESCE(ogs.is_enabled, true) = true
      `;
      
      const result = await db.query(query, [operatorId, gameCode]);
      return result.rows.length > 0;
      
    } catch (error) {
      console.error('❌ Error checking game support:', error);
      return false;
    }
  }

  /**
   * Get operator's supported games (cached)
   */
  static async getOperatorSupportedGames(operatorId: string): Promise<string[]> {
    try {
      const gamePermissionsKey = redisKeys.operatorGamePermissions(operatorId);
      const games = await redis.smembers(gamePermissionsKey);
      
      if (games.length > 0) {
        return games;
      }
      
      // Fallback to database
      const query = `
        SELECT DISTINCT g.game_code
        FROM config.games g
        LEFT JOIN config.operator_game_settings ogs 
          ON g.game_code = ogs.game_code AND ogs.operator_id = $1
        WHERE g.status = 'active'
          AND COALESCE(ogs.is_enabled, true) = true
        ORDER BY g.game_code
      `;
      
      const result = await db.query(query, [operatorId]);
      return result.rows.map(row => row.game_code);
      
    } catch (error) {
      console.error('❌ Error getting operator supported games:', error);
      return [];
    }
  }

  /**
   * Get session metrics for operator
   */
  static async getSessionMetrics(operatorId: string): Promise<any> {
    try {
      const metricsKey = redisKeys.sessionMetrics(operatorId);
      const metricsData = await redis.get(metricsKey);
      
      if (metricsData) {
        return JSON.parse(metricsData);
      }
      
      // Calculate metrics from database if not cached
      const query = `
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN status = 'open' THEN 1 END) as active_sessions,
          COUNT(CASE WHEN play_mode = 'real' THEN 1 END) as real_sessions,
          COUNT(CASE WHEN play_mode = 'demo' THEN 1 END) as demo_sessions
        FROM data.game_sessions 
        WHERE operator_id = $1 
          AND started_at > NOW() - INTERVAL '24 hours'
      `;
      
      const result = await db.query(query, [operatorId]);
      const metrics = result.rows[0] || {};
      
      // Cache the metrics
      await redis.setex(metricsKey, 3600, JSON.stringify(metrics)); // 1 hour TTL
      
      return metrics;
      
    } catch (error) {
      console.error('❌ Error getting session metrics:', error);
      return {};
    }
  }

  /**
   * Close session and update cache
   */
  static async closeSession(sessionId: string): Promise<void> {
    try {
      // Update database
      await db.query(
        'UPDATE data.game_sessions SET status = $1, ended_at = NOW() WHERE session_id = $2',
        ['closed', sessionId]
      );
      
      // Update cache
      const session = await this.getSessionFromCache(sessionId);
      if (session) {
        const sessionKey = redisKeys.sessionById(sessionId);
        const userSessionsKey = redisKeys.userActiveSessions(session.user_id);
        const operatorSessionsKey = redisKeys.operatorActiveSessions(session.operator_id);
        
        const pipeline = redis.pipeline();
        
        // Update session status
        pipeline.hset(sessionKey, {
          status: 'closed',
          last_activity: new Date().toISOString()
        });
        
        // Remove from active sessions
        pipeline.srem(userSessionsKey, sessionId);
        pipeline.srem(operatorSessionsKey, sessionId);
        
        // Reduce TTL for closed sessions
        pipeline.expire(sessionKey, 3600); // 1 hour
        
        await pipeline.exec();
      }
    } catch (error) {
      console.error('❌ Error closing session:', error);
    }
  }
}
