// /backendApi/setters/errors/errorTypes.ts

export type ErrorDetailsFunction = (...args: any[]) => any;

export interface ErrorDefinition {
    code: string;
    status: number;
    message: (...args: any[]) => string;
    details?: ErrorDetailsFunction;
    hint?: string;
}

export const ERROR_TYPES: Record<string, ErrorDefinition> = {
    INVALID_INPUT: {
        code: 'INVALID_INPUT',
        status: 400,
        message: () => 'One or more input fields are invalid.',
        details: (issues) => issues,
        hint: 'Check required fields, data types, and allowed values.'
    },

    MISSING_REQUIRED_FIELDS: {
        code: 'MISSING_REQUIRED_FIELDS',
        status: 400,
        message: () => 'Missing required fields.',
        details: (fields) => fields,
        hint: 'Ensure all required fields are populated in the request.'
    },

    INVALID_CURRENCY: {
        code: 'INVALID_CURRENCY',
        status: 400,
        message: (currency, operator) => `Currency ${currency} not enabled for operator ${operator}`,
        details: (currency, operator) => ({ currency, operator }),
        hint: 'Enable the currency in ConfigOperatorCurrencyConfig or default ConfigCurrencies.'
    },

    MISSING_AUTH_HEADERS: {
        code: 'MISSING_AUTH_HEADERS',
        status: 400,
        message: () => 'Operator ID, API Key, and Signature headers are required.',
    },

    MISSING_API_KEY: {
        code: 'MISSING_API_KEY',
        status: 401,
        message: () => 'Missing API Key in request headers.'
    },

    INVALID_ALGORITHM: {
        code: 'INVALID_ALGORITHM',
        status: 400,
        message: () => 'The algorithm must be RS256, ES256, or plain.'
    },

    INVALID_OPERATOR: {
        code: 'INVALID_OPERATOR',
        status: 403,
        message: (operatorId) => `Operator ${operatorId} not found or unauthorized.`,
        details: (operatorId) => ({ operatorId })
    },

    UNAUTHORIZED_OPERATOR: {
        code: 'UNAUTHORIZED_OPERATOR',
        status: 403,
        message: () => 'Operator authentication failed.'
    },

    INVALID_API_KEY: {
        code: 'INVALID_API_KEY',
        status: 401,
        message: () => 'API Key does not match the signature.'
    },

    INVALID_SIGNATURE: {
        code: 'INVALID_SIGNATURE',
        status: 401,
        message: () => 'Signature validation failed or operator ID mismatch.'
    },

    OPERATOR_ID_MISMATCH: {
        code: 'OPERATOR_ID_MISMATCH',
        status: 400,
        message: () => 'API Key and operator_id mismatch.'
    },

    OPERATOR_ACTION_NOT_AUTHORIZED: {
        code: 'OPERATOR_ACTION_NOT_AUTHORIZED',
        status: 403,
        message: () => 'Operator is not authorized to perform this action.'
    },

    SESSION_INSERT_FAILED: {
        code: 'SESSION_INSERT_FAILED',
        status: 500,
        message: () => 'Failed to create session in database.'
    },

    SESSION_CONFLICT: {
        code: 'SESSION_CONFLICT',
        status: 409,
        message: () => 'An active session already exists and engine URLs have changed.'
    },

    SESSION_ALREADY_CLOSED: {
        code: 'SESSION_ALREADY_CLOSED',
        status: 400,
        message: () => 'Session is already closed.'
    },

    SESSION_NOT_FOUND: {
        code: 'SESSION_NOT_FOUND',
        status: 404,
        message: () => 'Session not found.'
    },

    SESSION_CANNOT_BE_CLOSED: {
        code: 'SESSION_CANNOT_BE_CLOSED',
        status: 400,
        message: () => 'This session cannot be closed due to its current state.'
    },

    INVALID_SESSION_ID: {
        code: 'INVALID_SESSION_ID',
        status: 400,
        message: () => 'The session ID provided is not valid or does not exist.'
    },

    GAME_NOT_FOUND: {
        code: 'GAME_NOT_FOUND',
        status: 404,
        message: (gameCode) => `Game ${gameCode} not found or inactive.`,
        details: (gameCode) => ({ gameCode })
    },

    GAME_NOT_ENABLED_FOR_OPERATOR: {
        code: 'GAME_NOT_ENABLED_FOR_OPERATOR',
        status: 403,
        message: (gameCode, operatorId) => `Game ${gameCode} is not enabled for operator ${operatorId}.`,
        details: (gameCode, operatorId) => ({ gameCode, operatorId })
    },

    USER_LOOKUP_FAILED: {
        code: 'USER_LOOKUP_FAILED',
        status: 500,
        message: () => 'User insert failed and no matching user found.'
    },

    DB_CONNECTION_FAILED: {
        code: 'DB_CONNECTION_FAILED',
        status: 500,
        message: () => 'Failed to connect to the database.'
    },

    ACTION_UNAUTHORIZED: {
        code: 'ACTION_UNAUTHORIZED',
        status: 403,
        message: () => 'Action cannot be carried out by this operator.'
    },

    INVALID_API_KEY_OR_IP: {
        code: 'INVALID_API_KEY_OR_IP',
        status: 401,
        message: () => 'Invalid API Key or IP address.'
    },

    AUTHENTICATION_ERROR: {
        code: 'AUTHENTICATION_ERROR',
        status: 500,
        message: () => 'Failed to authenticate operator.'
    },

    UNKNOWN_ERROR: {
        code: 'UNKNOWN_ERROR',
        status: 500,
        message: () => 'An unknown error occurred.'
    },

    // Operator Callback Error Types
    BET_PROCESSING_FAILED: {
        code: 'BET_PROCESSING_FAILED',
        status: 500,
        message: () => 'Failed to process bet placement with operator.',
        details: (operatorId, error) => ({ operatorId, error: error?.message }),
        hint: 'Check operator API connectivity and transaction details.'
    },

    WIN_PROCESSING_FAILED: {
        code: 'WIN_PROCESSING_FAILED',
        status: 500,
        message: () => 'Failed to process win payout with operator.',
        details: (operatorId, error) => ({ operatorId, error: error?.message }),
        hint: 'Check operator API connectivity and win transaction details.'
    },

    PROMO_WIN_PROCESSING_FAILED: {
        code: 'PROMO_WIN_PROCESSING_FAILED',
        status: 500,
        message: () => 'Failed to process promotional win with operator.',
        details: (operatorId, error) => ({ operatorId, error: error?.message }),
        hint: 'Check operator API connectivity and promotional win details.'
    },

    DUPLICATE_TRANSACTION: {
        code: 'DUPLICATE_TRANSACTION',
        status: 409,
        message: () => 'Transaction has already been processed.',
        details: (transactionId) => ({ transactionId }),
        hint: 'Check transaction ID for uniqueness.'
    },

    INVALID_BET_AMOUNT: {
        code: 'INVALID_BET_AMOUNT',
        status: 400,
        message: (amount, currency) => `Invalid bet amount ${amount} ${currency}.`,
        details: (amount, currency, limits) => ({ amount, currency, limits }),
        hint: 'Check bet amount against operator limits and currency rules.'
    },

    RELATED_BET_NOT_FOUND: {
        code: 'RELATED_BET_NOT_FOUND',
        status: 400,
        message: () => 'Related bet transaction not found.',
        details: (betTransactionId) => ({ betTransactionId }),
        hint: 'Ensure the related bet transaction exists before processing win.'
    },

    PROMOTION_NOT_ELIGIBLE: {
        code: 'PROMOTION_NOT_ELIGIBLE',
        status: 400,
        message: () => 'User is not eligible for this promotion.',
        details: (promotionType, userId) => ({ promotionType, userId }),
        hint: 'Check promotion rules and user eligibility criteria.'
    },

    OPERATOR_API_TIMEOUT: {
        code: 'OPERATOR_API_TIMEOUT',
        status: 504,
        message: () => 'Operator API request timed out.',
        details: (operatorId, endpoint) => ({ operatorId, endpoint }),
        hint: 'Check operator API response times and network connectivity.'
    },

    CIRCUIT_BREAKER_OPEN: {
        code: 'CIRCUIT_BREAKER_OPEN',
        status: 503,
        message: () => 'Operator API circuit breaker is open due to repeated failures.',
        details: (operatorId) => ({ operatorId }),
        hint: 'Wait for circuit breaker to reset or contact operator support.'
    },
};