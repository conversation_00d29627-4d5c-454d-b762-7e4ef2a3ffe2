// types/zod/config.ts
import { z } from 'zod';

// Operator games configuration schema
export const operatorGamesSchema = z.object({
    operator_id: z.string().min(1, 'operator_id is required'),
});

// Game details configuration schema
export const gameDetailsSchema = z.object({
    game_code: z.string().min(1, 'game_code is required'),
    operator_id: z.string().optional(),
});

// Inferred types
export type OperatorGamesPayload = z.infer<typeof operatorGamesSchema>;
export type GameDetailsPayload = z.infer<typeof gameDetailsSchema>;
