// apps/backendApi/src/types/zod/operatorCallbacks.ts
// Zod validation schemas for operator callback endpoints

import { z } from 'zod';

/**
 * Bet Placement Callback Schema
 * Endpoint: POST /api/operator/callback/bet
 * Purpose: Notify operator of bet placement for balance deduction
 */
export const betCallbackSchema = z.object({
  round_id: z.string().uuid('Invalid round_id format'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().length(3, 'currency must be 3-letter code'),
  bet_amount: z.number().positive('bet_amount must be positive'),
  transaction_id: z.string().min(1, 'transaction_id is required'),
  game_code: z.string().min(1, 'game_code is required'),
  bet_metadata: z.object({
    game_round_id: z.string().optional(),
    bet_type: z.string().optional(),
    multiplier: z.number().optional(),
    auto_play: z.boolean().optional(),
    bet_lines: z.number().optional(),
    coin_value: z.number().optional(),
  }).optional(),
  timestamp: z.string().datetime('Invalid timestamp format'),
});

export type BetCallbackPayload = z.infer<typeof betCallbackSchema>;

/**
 * Win Notification Callback Schema
 * Endpoint: POST /api/operator/callback/win
 * Purpose: Notify operator of win amount for balance credit
 */
export const winCallbackSchema = z.object({
  round_id: z.string().uuid('Invalid round_id format'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().length(3, 'currency must be 3-letter code'),
  win_amount: z.number().min(0, 'win_amount must be non-negative'),
  transaction_id: z.string().min(1, 'transaction_id is required'),
  game_code: z.string().min(1, 'game_code is required'),
  related_bet_transaction_id: z.string().min(1, 'related_bet_transaction_id is required'),
  win_metadata: z.object({
    win_type: z.enum(['regular', 'bonus', 'jackpot', 'free_spin']),
    multiplier: z.number().optional(),
    bonus_round: z.boolean().optional(),
    jackpot_type: z.string().optional(),
    free_spins_remaining: z.number().optional(),
    bonus_game_id: z.string().optional(),
  }).optional(),
  timestamp: z.string().datetime('Invalid timestamp format'),
});

export type WinCallbackPayload = z.infer<typeof winCallbackSchema>;

/**
 * Promotional Win Notification Callback Schema
 * Endpoint: POST /api/operator/callback/promo-win
 * Purpose: Notify operator of promotional wins (tournaments, bonuses)
 */
export const promoWinCallbackSchema = z.object({
  round_id: z.string().uuid('Invalid round_id format'),
  session_id: z.string().uuid('Invalid session_id format'),
  user_id: z.string().uuid('Invalid user_id format'),
  operator_id: z.string().min(1, 'operator_id is required'),
  currency: z.string().length(3, 'currency must be 3-letter code'),
  promo_amount: z.number().positive('promo_amount must be positive'),
  transaction_id: z.string().min(1, 'transaction_id is required'),
  game_code: z.string().min(1, 'game_code is required'),
  promotion_type: z.enum(['tournament', 'bonus', 'cashback', 'loyalty']),
  tournament_id: z.string().optional(),
  promo_metadata: z.object({
    promotion_name: z.string().optional(),
    tournament_rank: z.number().optional(),
    bonus_type: z.string().optional(),
    expiry_date: z.string().datetime().optional(),
    promotion_id: z.string().optional(),
    campaign_id: z.string().optional(),
    loyalty_level: z.string().optional(),
  }).optional(),
  timestamp: z.string().datetime('Invalid timestamp format'),
});

export type PromoWinCallbackPayload = z.infer<typeof promoWinCallbackSchema>;

/**
 * Common response schema for all operator callbacks
 */
export const operatorCallbackResponseSchema = z.object({
  success: z.boolean(),
  transaction_id: z.string(),
  new_balance: z.number().optional(),
  operator_transaction_id: z.string().optional(),
  timestamp: z.string().datetime(),
  error: z.string().optional(),
  message: z.string().optional(),
});

export type OperatorCallbackResponse = z.infer<typeof operatorCallbackResponseSchema>;

/**
 * Transaction idempotency check schema
 */
export const transactionIdempotencySchema = z.object({
  transaction_id: z.string().min(1, 'transaction_id is required'),
  operator_id: z.string().min(1, 'operator_id is required'),
  transaction_type: z.enum(['bet', 'win', 'promo_win']),
});

export type TransactionIdempotencyCheck = z.infer<typeof transactionIdempotencySchema>;

/**
 * Operator API request schema for external calls
 */
export const operatorApiRequestSchema = z.object({
  operator_id: z.string(),
  endpoint: z.string(),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE']),
  payload: z.record(z.any()),
  headers: z.record(z.string()).optional(),
  timeout: z.number().default(5000),
  retry_count: z.number().default(3),
});

export type OperatorApiRequest = z.infer<typeof operatorApiRequestSchema>;

/**
 * Validation helper functions
 */
export const validateBetCallback = (data: unknown): BetCallbackPayload => {
  return betCallbackSchema.parse(data);
};

export const validateWinCallback = (data: unknown): WinCallbackPayload => {
  return winCallbackSchema.parse(data);
};

export const validatePromoWinCallback = (data: unknown): PromoWinCallbackPayload => {
  return promoWinCallbackSchema.parse(data);
};

/**
 * Schema registry for operator callbacks
 */
export const operatorCallbackSchemas = {
  '/api/operator/callback/bet': betCallbackSchema,
  '/api/operator/callback/win': winCallbackSchema,
  '/api/operator/callback/promo-win': promoWinCallbackSchema,
} as const;

/**
 * Type guard functions
 */
export const isBetCallback = (data: any): data is BetCallbackPayload => {
  return betCallbackSchema.safeParse(data).success;
};

export const isWinCallback = (data: any): data is WinCallbackPayload => {
  return winCallbackSchema.safeParse(data).success;
};

export const isPromoWinCallback = (data: any): data is PromoWinCallbackPayload => {
  return promoWinCallbackSchema.safeParse(data).success;
};
