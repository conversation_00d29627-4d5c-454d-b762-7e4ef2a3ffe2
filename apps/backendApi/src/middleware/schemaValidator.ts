// /backendApi/middleware/schemaValidator.ts

import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';
import { operatorVerifySchema } from '@backendApiTypes/zod/operator';
import { sessionCreateSchema, sessionCloseSchema, sessionAuthenticateSchema } from '@backendApiTypes/zod/session';
import { gameInteractionSchema } from '@backendApiTypes/zod/gameInteraction';
import { operatorGamesSchema, gameDetailsSchema } from '@backendApiTypes/zod/config';

const isDebug = process.env.LOG_LEVEL === 'debug';

export const schemaValidator = (route: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    let schema: ZodSchema | undefined;

    switch (route) {
      case '/api/verify':
        schema = operatorVerifySchema;
        break;
      case '/api/session/create':
        schema = sessionCreateSchema;
        break;
      case '/api/session/authenticate':
        schema = sessionAuthenticateSchema;
        break;
      case '/api/session/:session_id/close':
        req.body.session_id = req.params.session_id; // Inject param into body for validation
        schema = sessionCloseSchema;
        break;
      case '/api/interact/play':
        schema = gameInteractionSchema;
        break;
      case '/api/config/operator-games':
        schema = operatorGamesSchema;
        break;
      case '/api/config/game-details':
        schema = gameDetailsSchema;
        break;
      default:
        return next({
          status: 404,
          code: 'SCHEMA_NOT_FOUND',
          message: `No schema found for route ${route}`,
        });
    }

    const result = schema.safeParse(req.body);

    if (result.success) {
      req.body = result.data;
      return next();
    }

    const zodErr = result.error;
    const errorDetails = zodErr.errors.map((e) => ({
      path: e.path.join('.'),
      message: e.message,
      code: e.code,
    }));

    if (isDebug) {
      console.warn('[schemaValidator] Validation errors:', errorDetails);
    }

    return next({
      status: 400,
      code: 'VALIDATION_ERROR',
      message: 'One or more validation errors occurred.',
      details: errorDetails,
    });
  };
};
