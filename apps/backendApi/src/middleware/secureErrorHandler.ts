// apps/backendApi/src/middleware/secureErrorHandler.ts
// Enhanced error handler that prevents information disclosure

import type { Request, Response, NextFunction } from 'express';
import { signPayload } from '@backendApiUtils/cryptoUtils';

interface StandardErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any[];
    timestamp: string;
    request_id: string;
    trace_id?: string;
  };
}

interface SecurityContext {
  timestamp: number;
  ip: string;
  userAgent: string;
  suspicious: boolean;
}

/**
 * Secure error handler that prevents information disclosure
 * Logs detailed errors internally while returning sanitized responses
 */
export const secureErrorHandler = async (
  err: unknown,
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';
  const errorObj = typeof err === 'object' && err !== null ? (err as any) : {};
  const securityContext = (req as any).security as SecurityContext;

  // Generate unique request ID for tracking
  const requestId = req.headers['x-request-id'] as string || generateRequestId();
  const traceId = req.headers['x-trace-id'] as string || undefined;

  // Determine error details based on environment
  const errorCode = errorObj.code || 'INTERNAL_ERROR';
  const errorStatus = errorObj.status || 500;
  
  // Sanitize error message for production
  let errorMessage = errorObj.message || 'An internal error occurred';
  if (isProduction) {
    errorMessage = sanitizeErrorMessage(errorMessage, errorCode);
  }

  // Build standardized error response
  const standardError: StandardErrorResponse = {
    success: false,
    error: {
      code: errorCode,
      message: errorMessage,
      details: isDevelopment ? errorObj.details : undefined,
      timestamp: new Date().toISOString(),
      request_id: requestId,
      trace_id: traceId,
    }
  };

  // Log comprehensive error details internally
  const logContext = {
    error: {
      name: errorObj.name,
      message: errorObj.message,
      stack: errorObj.stack,
      code: errorCode,
      status: errorStatus,
      details: errorObj.details
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      path: req.path,
      operator: req.operator?.operatorId || 'unknown',
      user_agent: req.headers['user-agent'],
      ip: req.ip,
      headers: sanitizeHeaders(req.headers),
      body: sanitizeRequestBody(req.body),
      query: req.query,
      params: req.params
    },
    security: securityContext,
    response: standardError,
    timestamp: new Date().toISOString(),
    request_id: requestId,
    trace_id: traceId
  };

  // Log based on error severity
  if (errorStatus >= 500) {
    console.error('[SecureErrorHandler] Server error:', logContext);
  } else if (errorStatus >= 400) {
    console.warn('[SecureErrorHandler] Client error:', logContext);
  } else {
    console.log('[SecureErrorHandler] Request error:', logContext);
  }

  // Log security violations separately
  if (securityContext?.suspicious || isSecurityError(errorCode)) {
    console.error('[Security] Security violation detected:', {
      error_code: errorCode,
      ip: req.ip,
      user_agent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
      timestamp: new Date().toISOString(),
      request_id: requestId
    });
  }

  // Sign error response if operator context exists
  try {
    const operatorId = req.body?.operator_id || req.operator?.operatorId;
    if (operatorId && !isProduction) { // Only sign in non-production for now
      const { signature, kid, signedAt } = await signPayload(standardError as unknown as Record<string, unknown>, operatorId);
      res.setHeader('x-signature', signature);
      res.setHeader('x-signature-kid', kid);
      res.setHeader('x-signed-at', signedAt);
    }
  } catch (signingError) {
    console.warn('[SecureErrorHandler] Error response signing failed:', signingError);
  }

  // Set security headers for error responses
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
  res.setHeader('Pragma', 'no-cache');

  // Send sanitized error response
  res.status(errorStatus).json(standardError);
};

/**
 * Sanitize error messages to prevent information disclosure
 */
function sanitizeErrorMessage(message: string, errorCode: string): string {
  // Map of error codes to safe messages
  const safeMessages: Record<string, string> = {
    'VALIDATION_ERROR': 'Invalid request data provided',
    'AUTHENTICATION_FAILED': 'Authentication failed',
    'AUTHORIZATION_FAILED': 'Access denied',
    'RATE_LIMIT_EXCEEDED': 'Too many requests, please try again later',
    'SESSION_NOT_FOUND': 'Session not found or expired',
    'OPERATOR_NOT_FOUND': 'Operator not found',
    'GAME_NOT_FOUND': 'Game not found',
    'INSUFFICIENT_BALANCE': 'Insufficient balance',
    'TRANSACTION_FAILED': 'Transaction could not be processed',
    'INTERNAL_ERROR': 'An internal error occurred',
    'SERVICE_UNAVAILABLE': 'Service temporarily unavailable',
    'DATABASE_ERROR': 'Database operation failed',
    'NETWORK_ERROR': 'Network operation failed',
    'TIMEOUT_ERROR': 'Request timed out',
    'SECURITY_VIOLATION': 'Request blocked due to security policy'
  };

  // Return safe message if available, otherwise generic message
  return safeMessages[errorCode] || 'An error occurred while processing your request';
}

/**
 * Check if error code indicates a security violation
 */
function isSecurityError(errorCode: string): boolean {
  const securityErrorCodes = [
    'SECURITY_VIOLATION',
    'SQL_INJECTION_DETECTED',
    'XSS_ATTEMPT_DETECTED',
    'INVALID_SIGNATURE',
    'RATE_LIMIT_EXCEEDED',
    'UNAUTHORIZED_ACCESS',
    'SUSPICIOUS_ACTIVITY'
  ];

  return securityErrorCodes.includes(errorCode);
}

/**
 * Sanitize request headers for logging
 */
function sanitizeHeaders(headers: any): any {
  const sensitiveHeaders = [
    'authorization',
    'x-api-key',
    'cookie',
    'x-rgs-signature',
    'x-operator-key'
  ];

  const sanitized: any = {};
  for (const [key, value] of Object.entries(headers)) {
    if (sensitiveHeaders.includes(key.toLowerCase())) {
      sanitized[key] = '[REDACTED]';
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Sanitize request body for logging
 */
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = [
    'password',
    'api_key',
    'private_key',
    'secret',
    'token',
    'signature',
    'credit_card',
    'ssn',
    'social_security'
  ];

  const sanitized: any = Array.isArray(body) ? [] : {};

  for (const [key, value] of Object.entries(body)) {
    if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeRequestBody(value);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Generate unique request ID for tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Enhanced error logger for security events
 */
export const logSecurityEvent = (
  eventType: string,
  details: Record<string, any>,
  req: Request
): void => {
  const securityEvent = {
    event_type: eventType,
    timestamp: new Date().toISOString(),
    ip: req.ip,
    user_agent: req.headers['user-agent'],
    path: req.path,
    method: req.method,
    operator_id: req.body?.operator_id || req.operator?.operatorId || 'unknown',
    session_id: req.body?.session_id || 'unknown',
    details,
    request_id: req.headers['x-request-id'] || generateRequestId()
  };

  console.error('[SecurityEvent]', securityEvent);

  // In production, this would also send to security monitoring system
  if (process.env.NODE_ENV === 'production') {
    // Send to security monitoring service
    // await securityMonitoringService.logEvent(securityEvent);
  }
};

/**
 * Middleware to add request ID to all requests
 */
export const addRequestId = (req: Request, res: Response, next: NextFunction): void => {
  const requestId = req.headers['x-request-id'] as string || generateRequestId();
  req.headers['x-request-id'] = requestId;
  res.setHeader('X-Request-ID', requestId);
  next();
};

/**
 * Response time tracking middleware
 */
export const responseTimeTracker = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    res.setHeader('X-Response-Time', `${duration}ms`);

    // Log slow requests
    if (duration > 1000) { // Log requests taking more than 1 second
      console.warn('[Performance] Slow request detected:', {
        method: req.method,
        path: req.path,
        duration: `${duration}ms`,
        operator: req.operator?.operatorId || 'unknown',
        request_id: req.headers['x-request-id']
      });
    }
  });

  next();
};
