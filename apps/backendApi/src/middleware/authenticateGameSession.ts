// /backendApi/middleware/authenticateGameSession.ts

import type { Request, Response, NextFunction } from 'express';
import { db, getTable } from '@insolence-rgs/db';
import { cacheManager } from '@backendApiUtils/cacheManager';
import { createError } from '@backendApiUtils/errors/errorFactory';
import { ERROR_TYPES } from '@backendApiUtils/errors/errorTypes';

const isDebug = process.env.LOG_LEVEL === 'debug';

interface AuthenticatedRequest extends Request {
  session?: {
    session_id: string;
    user_id: string;
    operator_id: string;
    game_code: string;
    currency: string;
    play_mode: 'real' | 'demo';
    status: string;
  };
  operator?: {
    operatorId: string;
    environment: string;
    details: any;
    metadata: Record<string, any>;
  };
}

/**
 * Middleware to authenticate game session and validate operator
 */
export const authenticateGameSession = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { session_id, operator_id, user_id, game_code, play_mode } = req.body;

    // Validate required fields
    if (!session_id || !operator_id || !user_id || !game_code) {
      res.status(400).json({
        error: 'MISSING_REQUIRED_FIELDS',
        message: 'session_id, operator_id, user_id, and game_code are required',
      });
      return;
    }

    // Validate play_mode
    if (play_mode && !['real', 'demo'].includes(play_mode)) {
      res.status(400).json({
        error: 'INVALID_PLAY_MODE',
        message: 'play_mode must be "real" or "demo"',
      });
      return;
    }

    if (isDebug) {
      console.log('[GameSessionAuth] 🔐 Authenticating session:', {
        session_id,
        operator_id,
        user_id,
        game_code,
        play_mode
      });
    }

    // 1. Validate operator exists in cache
    const operatorDetails = cacheManager.getOperatorDetails(operator_id, 'production');
    if (!operatorDetails) {
      res.status(401).json({
        error: 'INVALID_OPERATOR',
        message: `Operator ${operator_id} not found or inactive`,
      });
      return;
    }

    // 2. Validate session exists and is active
    const sessionQuery = `
      SELECT s.*, u.external_user_id 
      FROM ${getTable('DataGameSessions')} s
      LEFT JOIN ${getTable('DataUsers')} u ON s.user_id = u.user_id
      WHERE s.session_id = $1 
        AND s.operator_id = $2 
        AND s.user_id = $3
        AND s.status = 'open'
    `;

    const sessionResult = await db.query(sessionQuery, [session_id, operator_id, user_id]);

    if (!sessionResult.rowCount) {
      res.status(401).json({
        error: 'INVALID_SESSION',
        message: 'Session not found, expired, or does not belong to this operator/user',
      });
      return;
    }

    const session = sessionResult.rows[0];

    // 3. Validate game_code matches session (if session has specific game)
    if (session.game_code && session.game_code !== game_code) {
      res.status(403).json({
        error: 'GAME_CODE_MISMATCH',
        message: `Session is for game ${session.game_code}, not ${game_code}`,
      });
      return;
    }

    // 4. Validate operator supports this game
    const gameSupported = await validateOperatorGameSupport(operator_id, game_code);
    if (!gameSupported) {
      res.status(403).json({
        error: 'GAME_NOT_SUPPORTED',
        message: `Operator ${operator_id} does not support game ${game_code}`,
      });
      return;
    }

    // 5. Rate limiting check (basic implementation)
    const rateLimitKey = `rate_limit:${session_id}:${Date.now() / 60000 | 0}`; // per minute
    // TODO: Implement Redis-based rate limiting

    if (isDebug) {
      console.log('[GameSessionAuth] ✅ Session authenticated successfully');
    }

    // Attach session and operator info to request
    req.session = {
      session_id: session.session_id,
      user_id: session.user_id,
      operator_id: session.operator_id,
      game_code: session.game_code || game_code,
      currency: session.currency,
      play_mode: session.play_mode || play_mode || 'demo',
      status: session.status,
    };

    req.operator = {
      operatorId: operator_id,
      environment: 'production',
      details: operatorDetails,
      metadata: {},
    };

    next();
  } catch (error) {
    console.error('[GameSessionAuth] ❌ Authentication error:', error);
    res.status(500).json({
      error: 'AUTHENTICATION_ERROR',
      message: 'Internal server error during session authentication',
    });
  }
};

/**
 * Validate that operator supports the specified game
 */
async function validateOperatorGameSupport(operatorId: string, gameCode: string): Promise<boolean> {
  try {
    // Check if operator has access to this game
    const gameAccessQuery = `
      SELECT 1 FROM ${getTable('ConfigOperatorGames')} 
      WHERE operator_id = $1 AND game_code = $2 AND status = 'active'
    `;
    
    const result = await db.query(gameAccessQuery, [operatorId, gameCode]);
    return result.rowCount !== null && result.rowCount > 0;
  } catch (error) {
    console.error('[GameSessionAuth] Error validating game support:', error);
    return false; // Fail closed
  }
}

/**
 * Middleware specifically for demo mode - less strict validation
 */
export const authenticateDemoSession = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const { play_mode } = req.body;

  // If it's demo mode, allow with minimal validation
  if (play_mode === 'demo') {
    req.session = {
      session_id: req.body.session_id || 'demo-session',
      user_id: req.body.user_id || 'demo-user',
      operator_id: req.body.operator_id || 'demo_operator',
      game_code: req.body.game_code,
      currency: req.body.currency || 'USD',
      play_mode: 'demo',
      status: 'open',
    };

    req.operator = {
      operatorId: 'demo_operator',
      environment: 'demo',
      details: { name: 'Demo Operator' },
      metadata: {},
    };

    return next();
  }

  // For real mode, use full authentication
  return authenticateGameSession(req, res, next);
};
