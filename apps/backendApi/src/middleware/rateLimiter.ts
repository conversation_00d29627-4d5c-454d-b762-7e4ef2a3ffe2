// apps/backendApi/src/middleware/rateLimiter.ts
// Redis-based rate limiting middleware for casino-grade DDoS protection

import rateLimit from 'express-rate-limit';
import { redis } from '@insolence-rgs/cache-main';
import type { Request, Response } from 'express';

const isDebug = process.env.LOG_LEVEL === 'debug';

/**
 * Custom Redis store for rate limiting
 * Implements the express-rate-limit Store interface
 */
class CustomRedisStore {
  private windowMs: number;

  constructor(windowMs: number = 60000) {
    this.windowMs = windowMs;
  }

  async increment(key: string): Promise<{ totalHits: number; resetTime: Date }> {
    try {
      // Use Redis INCR and EXPIRE for atomic operations
      const pipeline = redis.pipeline();
      pipeline.incr(key);
      pipeline.expire(key, Math.ceil(this.windowMs / 1000));

      const results = await pipeline.exec();
      const totalHits = results?.[0]?.[1] as number || 0;
      const resetTime = new Date(Date.now() + this.windowMs);

      return { totalHits, resetTime };
    } catch (error) {
      console.error('[RateLimit] Redis store error:', error);
      // Fallback to allow request if Red<PERSON> fails
      return { totalHits: 0, resetTime: new Date(Date.now() + this.windowMs) };
    }
  }

  async decrement(key: string): Promise<void> {
    try {
      await redis.decr(key);
    } catch (error) {
      console.error('[RateLimit] Redis decrement error:', error);
    }
  }

  async resetKey(key: string): Promise<void> {
    try {
      await redis.del(key);
    } catch (error) {
      console.error('[RateLimit] Redis reset error:', error);
    }
  }
}

/**
 * Rate limiter for authentication endpoints
 * Limit: 100 requests per 15 minutes per IP
 * Target: Prevent brute force authentication attacks
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req: Request): string => {
    // Use IP address as primary key, with operator fallback for identification
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    const operator = req.body?.operator_id || 'unknown';
    return `auth:${ip}:${operator}`;
  },
  skip: (req: Request): boolean => {
    // Skip rate limiting for health checks and test endpoints
    return req.path === '/health' || req.path.startsWith('/test/');
  },
  handler: (req: Request, res: Response): void => {
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    const operator = req.body?.operator_id || 'unknown';

    console.warn(`[RateLimit] Authentication rate limit exceeded for IP: ${ip}, Operator: ${operator}`);

    // Log security event for monitoring
    if (isDebug) {
      console.log('[RateLimit] Request details:', {
        ip,
        operator,
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent']
      });
    }

    res.status(429).json({
      success: false,
      error: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many authentication attempts, please try again later.',
      retry_after: 15 * 60, // seconds
    });
  }
});

/**
 * Rate limiter for game interaction endpoints
 * Limit: 60 requests per minute per session
 * Target: Prevent game action spam and ensure fair play
 */
export const gameInteractionRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // 60 game actions per minute per session
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    // Use session_id as primary key for game interactions
    const sessionId = req.body?.session_id || 'unknown';
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    return `game:${sessionId}:${ip}`;
  },
  skip: (req: Request): boolean => {
    // Skip for demo mode to allow testing
    return req.body?.play_mode === 'demo' && process.env.NODE_ENV !== 'production';
  },
  handler: (req: Request, res: Response): void => {
    const sessionId = req.body?.session_id || 'unknown';
    const gameCode = req.body?.game_code || 'unknown';
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';

    console.warn(`[RateLimit] Game interaction rate limit exceeded for Session: ${sessionId}, Game: ${gameCode}, IP: ${ip}`);

    if (isDebug) {
      console.log('[RateLimit] Game interaction details:', {
        sessionId,
        gameCode,
        action: req.body?.action,
        ip,
        path: req.path
      });
    }

    res.status(429).json({
      success: false,
      error: 'GAME_RATE_LIMIT_EXCEEDED',
      message: 'Too many game actions, please slow down.',
      retry_after: 60, // seconds
    });
  }
});

/**
 * Rate limiter for operator callback endpoints
 * Limit: 1000 requests per hour per operator
 * Target: Prevent operator API abuse while allowing high-volume operations
 */
export const operatorCallbackRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // 1000 requests per hour per operator
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    // Use operator_id as primary key for callback endpoints
    const operatorId = req.body?.operator_id || req.headers['x-operator-id'] || 'unknown';
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    return `callback:${operatorId}:${ip}`;
  },
  handler: (req: Request, res: Response): void => {
    const operatorId = req.body?.operator_id || req.headers['x-operator-id'] || 'unknown';
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';

    console.error(`[RateLimit] Operator callback rate limit exceeded for Operator: ${operatorId}, IP: ${ip}`);

    if (isDebug) {
      console.log('[RateLimit] Operator callback details:', {
        operatorId,
        ip,
        path: req.path,
        method: req.method,
        transactionId: req.body?.transaction_id
      });
    }

    res.status(429).json({
      success: false,
      error: 'OPERATOR_RATE_LIMIT_EXCEEDED',
      message: 'Operator API rate limit exceeded, please contact support.',
      retry_after: 60 * 60, // seconds
    });
  }
});

/**
 * General API rate limiter for all other endpoints
 * Limit: 200 requests per 5 minutes per IP
 * Target: General DDoS protection
 */
export const generalRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 200, // 200 requests per 5 minutes per IP
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';
    return `general:${ip}`;
  },
  skip: (req: Request): boolean => {
    // Skip for health checks and internal endpoints
    return req.path === '/health' || 
           req.path === '/healthPretty' || 
           req.path.startsWith('/test/');
  },
  handler: (req: Request, res: Response): void => {
    const ip = req.ip || req.socket?.remoteAddress || 'unknown';

    console.warn(`[RateLimit] General rate limit exceeded for IP: ${ip}`);

    if (isDebug) {
      console.log('[RateLimit] General request details:', {
        ip,
        path: req.path,
        method: req.method,
        userAgent: req.headers['user-agent']
      });
    }

    res.status(429).json({
      success: false,
      error: 'GENERAL_RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later.',
      retry_after: 5 * 60, // seconds
    });
  }
});

/**
 * Rate limiter configuration for monitoring and alerting
 */
export const rateLimitConfig = {
  auth: {
    windowMs: 15 * 60 * 1000,
    max: 100,
    name: 'Authentication Rate Limit'
  },
  game: {
    windowMs: 1 * 60 * 1000,
    max: 60,
    name: 'Game Interaction Rate Limit'
  },
  operator: {
    windowMs: 60 * 60 * 1000,
    max: 1000,
    name: 'Operator Callback Rate Limit'
  },
  general: {
    windowMs: 5 * 60 * 1000,
    max: 200,
    name: 'General API Rate Limit'
  }
};

/**
 * Utility function to get current rate limit status for monitoring
 */
export const getRateLimitStatus = async (key: string): Promise<{
  remaining: number;
  resetTime: Date;
  totalHits: number;
} | null> => {
  try {
    const hits = await redis.get(key);
    const ttl = await redis.ttl(key);
    
    if (!hits || ttl <= 0) {
      return null;
    }
    
    return {
      remaining: Math.max(0, 100 - parseInt(hits)), // Assuming max of 100 for example
      resetTime: new Date(Date.now() + (ttl * 1000)),
      totalHits: parseInt(hits)
    };
  } catch (error) {
    console.error('[RateLimit] Error getting rate limit status:', error);
    return null;
  }
};
