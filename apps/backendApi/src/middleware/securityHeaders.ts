// apps/backendApi/src/middleware/securityHeaders.ts
// Comprehensive security headers middleware for casino-grade protection

import type { Request, Response, NextFunction } from 'express';

/**
 * Security headers middleware
 * Implements comprehensive security headers for casino-grade protection
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Content Security Policy - Prevent XSS attacks
  res.setHeader('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Allow inline scripts for API responses
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self'",
    "media-src 'none'",
    "object-src 'none'",
    "child-src 'none'",
    "frame-src 'none'",
    "worker-src 'none'",
    "frame-ancestors 'none'",
    "form-action 'self'",
    "base-uri 'self'",
    "manifest-src 'self'"
  ].join('; '));

  // Prevent clickjacking attacks
  res.setHeader('X-Frame-Options', 'DENY');

  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // Enable XSS protection (legacy browsers)
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Strict Transport Security - Force HTTPS
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  // Referrer Policy - Control referrer information
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Permissions Policy - Control browser features
  res.setHeader('Permissions-Policy', [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'accelerometer=()',
    'gyroscope=()',
    'fullscreen=(self)',
    'picture-in-picture=()'
  ].join(', '));

  // Cross-Origin Embedder Policy
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');

  // Cross-Origin Opener Policy
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');

  // Cross-Origin Resource Policy
  res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');

  // Remove server information
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');

  // Custom security headers for RGS
  res.setHeader('X-RGS-Version', '1.0');
  res.setHeader('X-Content-Security', 'protected');

  next();
};

/**
 * CORS security configuration
 * Enhanced CORS settings for casino operations
 */
export const corsSecurityOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) {
      return callback(null, true);
    }

    // Get allowed origins from environment
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://localhost:3000',
      'https://localhost:3001'
    ];

    // Check if origin is allowed
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`[CORS] Blocked request from unauthorized origin: ${origin}`);
      callback(new Error('Not allowed by CORS'), false);
    }
  },
  credentials: true,
  optionsSuccessStatus: 200, // Support legacy browsers
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-API-Key',
    'X-Operator-ID',
    'X-Session-ID',
    'X-Request-ID',
    'X-RGS-Signature',
    'X-RGS-Timestamp'
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'X-Request-ID',
    'X-Response-Time'
  ],
  maxAge: 86400 // 24 hours
};

/**
 * Request sanitization middleware
 * Sanitizes all incoming request data to prevent XSS and injection attacks
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }

    // Sanitize URL parameters
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }

    next();
  } catch (error) {
    console.error('[Security] Input sanitization error:', error);
    res.status(400).json({
      success: false,
      error: 'INVALID_INPUT',
      message: 'Request contains invalid or malicious content',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }

  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const sanitizedKey = sanitizeString(key);
        sanitized[sanitizedKey] = sanitizeObject(obj[key]);
      }
    }
    return sanitized;
  }

  return obj;
}

/**
 * Sanitize string values to prevent XSS and injection attacks
 */
function sanitizeString(str: string): string {
  if (typeof str !== 'string') {
    return str;
  }

  return str
    // Remove script tags
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Remove javascript: URLs
    .replace(/javascript:/gi, '')
    // Remove on* event handlers
    .replace(/on\w+\s*=/gi, '')
    // Remove data: URLs (except images)
    .replace(/data:(?!image\/)/gi, 'data-blocked:')
    // Remove vbscript: URLs
    .replace(/vbscript:/gi, '')
    // Remove expression() CSS
    .replace(/expression\s*\(/gi, 'blocked(')
    // Remove import statements
    .replace(/@import/gi, '@blocked')
    // Limit string length to prevent DoS
    .slice(0, 10000);
}

/**
 * SQL injection prevention middleware
 * Additional layer of protection against SQL injection attempts
 */
export const preventSQLInjection = (req: Request, res: Response, next: NextFunction): void => {
  const sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /(--|\/\*|\*\/|;)/g,
    /(\b(CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()/gi,
    /(\b(WAITFOR|DELAY)\b)/gi,
    /(xp_|sp_)/gi
  ];

  const checkForSQLInjection = (value: any): boolean => {
    if (typeof value === 'string') {
      return sqlInjectionPatterns.some(pattern => pattern.test(value));
    }
    return false;
  };

  const scanObject = (obj: any): boolean => {
    if (obj === null || obj === undefined) {
      return false;
    }

    if (typeof obj === 'string') {
      return checkForSQLInjection(obj);
    }

    if (Array.isArray(obj)) {
      return obj.some(item => scanObject(item));
    }

    if (typeof obj === 'object') {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (checkForSQLInjection(key) || scanObject(obj[key])) {
            return true;
          }
        }
      }
    }

    return false;
  };

  // Check request body, query, and params for SQL injection attempts
  if (scanObject(req.body) || scanObject(req.query) || scanObject(req.params)) {
    console.warn('[Security] SQL injection attempt detected:', {
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
      body: req.body,
      query: req.query,
      params: req.params
    });

    res.status(400).json({
      success: false,
      error: 'SECURITY_VIOLATION',
      message: 'Request blocked due to security policy violation',
      timestamp: new Date().toISOString()
    });
    return;
  }

  next();
};

/**
 * Request size limiting middleware
 * Prevents DoS attacks through large payloads
 */
export const requestSizeLimiter = (req: Request, res: Response, next: NextFunction): void => {
  const maxSizes = {
    '/api/operator/callback': 50 * 1024, // 50KB for operator callbacks
    '/api/interact': 10 * 1024, // 10KB for game interactions
    '/api/session': 5 * 1024, // 5KB for session operations
    default: 2 * 1024 * 1024 // 2MB default
  };

  let maxSize = maxSizes.default;

  // Determine appropriate size limit based on endpoint
  for (const [path, size] of Object.entries(maxSizes)) {
    if (path !== 'default' && req.path.startsWith(path)) {
      maxSize = size;
      break;
    }
  }

  // Check content-length header
  const contentLength = parseInt(req.headers['content-length'] || '0');
  if (contentLength > maxSize) {
    console.warn(`[Security] Request size limit exceeded: ${contentLength} > ${maxSize} for ${req.path}`);
    
    res.status(413).json({
      success: false,
      error: 'PAYLOAD_TOO_LARGE',
      message: `Request payload too large. Maximum allowed: ${maxSize} bytes`,
      timestamp: new Date().toISOString()
    });
    return;
  }

  next();
};

/**
 * Security monitoring middleware
 * Logs security-related events for monitoring and alerting
 */
export const securityMonitoring = (req: Request, res: Response, next: NextFunction): void => {
  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\.\//g, // Directory traversal
    /%2e%2e%2f/gi, // URL encoded directory traversal
    /\0/g, // Null bytes
    /%00/gi, // URL encoded null bytes
    /\x00/g, // Hex null bytes
  ];

  const fullUrl = req.originalUrl || req.url;
  const userAgent = req.headers['user-agent'] || '';

  // Check for suspicious patterns
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(fullUrl) || pattern.test(userAgent)
  );

  if (isSuspicious) {
    console.warn('[Security] Suspicious request detected:', {
      ip: req.ip,
      userAgent,
      path: req.path,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }

  // Add security context to request
  (req as any).security = {
    timestamp: Date.now(),
    ip: req.ip,
    userAgent,
    suspicious: isSuspicious
  };

  next();
};
