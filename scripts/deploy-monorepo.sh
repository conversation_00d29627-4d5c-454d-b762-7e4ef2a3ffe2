#!/bin/bash

# 🚀 Comprehensive Monorepo Deployment Script
# Deploys the entire insolence_rgs ecosystem with validation and health checks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
NODE_ENV="${NODE_ENV:-production}"
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --env=*)
            ENV_FILE="${1#*=}"
            shift
            ;;
        *.env*)
            ENV_FILE="$1"
            shift
            ;;
        *)
            # Unknown option, treat as environment file for backward compatibility
            if [ -z "$ENV_FILE" ] && [[ "$1" == *.env* ]]; then
                ENV_FILE="$1"
            fi
            shift
            ;;
    esac
done

# Determine environment file based on NODE_ENV if not provided as argument
if [ -z "$ENV_FILE" ]; then
    if [ "$NODE_ENV" = "development" ]; then
        # Try .env.local first, then .env.development
        if [ -f ".env.local" ]; then
            ENV_FILE=".env.local"
        else
            ENV_FILE=".env.development"
        fi
    else
        ENV_FILE=".env.production"
    fi
fi

# Centralized logging directory structure
LOGS_BASE_DIR="logs/deployments"
LOGS_SERVICES_DIR="$LOGS_BASE_DIR/services"
LOGS_REPORTS_DIR="$LOGS_BASE_DIR/reports"
DEPLOYMENT_LOG="$LOGS_BASE_DIR/deployment-$(date +%Y%m%d-%H%M%S).log"

# Create centralized logging directories
create_logging_directories() {
    mkdir -p "$LOGS_BASE_DIR"
    mkdir -p "$LOGS_SERVICES_DIR"
    mkdir -p "$LOGS_REPORTS_DIR"

    # Ensure proper permissions
    chmod 755 "$LOGS_BASE_DIR"
    chmod 755 "$LOGS_SERVICES_DIR"
    chmod 755 "$LOGS_REPORTS_DIR"
}

# Initialize logging
create_logging_directories

# Create the deployment log file
touch "$DEPLOYMENT_LOG"

# Logging functions with centralized log directory
log_info() { echo -e "${BLUE}ℹ️  $1${NC}" | tee -a "$DEPLOYMENT_LOG"; }
log_success() { echo -e "${GREEN}✅ $1${NC}" | tee -a "$DEPLOYMENT_LOG"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$DEPLOYMENT_LOG"; }
log_error() { echo -e "${RED}❌ $1${NC}" | tee -a "$DEPLOYMENT_LOG"; }
log_header() { echo -e "\n${CYAN}${BOLD}🔍 $1${NC}" | tee -a "$DEPLOYMENT_LOG"; }

# Environment-aware PM2 configuration selection
if [ "$NODE_ENV" = "production" ]; then
    PM2_CONFIG="pm2-production.config.cjs"
    log_info "Using production PM2 configuration"
else
    PM2_CONFIG="pm2-local.config.cjs"
    log_info "Using local/development PM2 configuration"
fi

# Service startup order (respects dependencies)
# Note: gamesUi is deprecated and excluded from deployment
SERVICE_ORDER=("rgs-cacheManager" "rgs-backendApi-dev" "rgs-gamesLogic-service" "rgs-gamesFrontend-dev")

# Health check endpoints (Bash 3.x compatible)
# Note: gamesUi health check removed as service is deprecated
get_health_endpoint() {
    case "$1" in
        "rgs-backendApi-dev") echo "http://localhost:3100/api/health" ;;
        "rgs-gamesLogic-service") echo "null" ;;
        "rgs-gamesFrontend-dev") echo "http://localhost:5173/" ;;
        *) echo "" ;;
    esac
}

# Cleanup function for rollback
cleanup_deployment() {
    log_warning "Deployment failed. Initiating rollback..."
    pm2 stop all || true
    pm2 delete all || true
    log_error "Deployment rolled back. Check logs for details."
    exit 1
}

# Trap errors for automatic rollback
trap cleanup_deployment ERR

# Essential files configuration (Bash 3.x compatible)
# Environment file is determined by NODE_ENV and script arguments
get_essential_files_list() {
    local env_file="$1"
    echo "$env_file"
    echo "keys/insolence_rgs_private_v1.pem"
    echo "keys/insolence_rgs_public_v1.pem"
    echo "certs/do-ca.crt"
    echo "certs/private.pem"
    echo "certs/public.pem"
}

get_file_description() {
    case "$1" in
        ".env.production") echo "Production environment configuration file" ;;
        ".env.local") echo "Local development environment configuration file" ;;
        ".env.development") echo "Development environment configuration file" ;;
        ".env"*) echo "Environment configuration file" ;;
        "keys/insolence_rgs_private_v1.pem") echo "RSA private key for JWT signing (permissions: 600)" ;;
        "keys/insolence_rgs_public_v1.pem") echo "RSA public key for JWT verification (permissions: 644)" ;;
        "certs/do-ca.crt") echo "Database SSL certificate (optional)" ;;
        "certs/private.pem") echo "SSL private certificate (optional)" ;;
        "certs/public.pem") echo "SSL public certificate (optional)" ;;
        *) echo "Unknown file" ;;
    esac
}

# File templates for missing files
get_file_template() {
    local file_path="$1"
    case "$file_path" in
        ".env.production")
            cat << 'EOF'
# Production Environment Configuration
NODE_ENV=production
RGS_ENV=production

# Service Configuration
RUN_BACKEND=true
RUN_GAMES=true
RUN_CACHE=true
RUN_GAMES_FRONTEND=true
RUN_FRONTEND=false

# API and Game URLs (UPDATE THESE)
BASE_API_ROUTE=https://api.yourdomain.com/api
BASE_GAME_URL=https://games.yourdomain.com/play

# Database Configuration (UPDATE THESE)
DB_HOST=your-database-host
DB_PORT=25060
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=insolence
DB_CONFIG_SSL=true
DB_SSL__REJECT_UNAUTHORIZED=false

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
CACHE_WIPE=false
CACHE_PERSIST=false

# Security Keys
RGS_PRIVATE_KEY_PATH=./keys/insolence_rgs_private_v1.pem
RGS_PUBLIC_KEY_PATH=./keys/insolence_rgs_public_v1.pem
RGS_KEY_ID=rgs-main-2025

# Logging
DEBUG=false
LOG__LEVEL=info
EOF
            ;;
        ".env.local"|".env.development")
            cat << 'EOF'
# Local Development Environment Configuration
NODE_ENV=development
RGS_ENV=development

# Service Configuration
RUN_BACKEND=true
RUN_GAMES=true
RUN_CACHE=true
RUN_GAMES_FRONTEND=true
RUN_FRONTEND=false

# API and Game URLs (Local Development)
BASE_API_ROUTE=http://localhost:3100/api
BASE_GAME_URL=http://localhost:5173/play

# Database Configuration (Local Development)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=insolence_dev
DB_CONFIG_SSL=false
DB_SSL__REJECT_UNAUTHORIZED=false

# Redis Configuration (Local)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
CACHE_WIPE=true
CACHE_PERSIST=false

# Security Keys (Development)
RGS_PRIVATE_KEY_PATH=./keys/insolence_rgs_private_v1.pem
RGS_PUBLIC_KEY_PATH=./keys/insolence_rgs_public_v1.pem
RGS_KEY_ID=rgs-dev-2025

# Logging (Development)
DEBUG=true
LOG__LEVEL=debug
EOF
            ;;
        "keys/insolence_rgs_private_v1.pem")
            echo "-----BEGIN PRIVATE KEY-----"
            echo "[Your RSA private key content here]"
            echo "-----END PRIVATE KEY-----"
            ;;
        "keys/insolence_rgs_public_v1.pem")
            echo "-----BEGIN PUBLIC KEY-----"
            echo "[Your RSA public key content here]"
            echo "-----END PUBLIC KEY-----"
            ;;
        *)
            echo "[File content for $file_path]"
            ;;
    esac
}

# Interactive file validation and creation assistance
validate_essential_files() {
    log_header "ESSENTIAL FILES VALIDATION"

    local missing_files=()
    local permission_issues=()
    local all_files_present=true

    # Check if keys directory exists
    if [ ! -d "keys" ]; then
        log_error "Keys directory missing: keys/"
        log_info "Creating keys directory with secure permissions..."
        mkdir -p keys
        chmod 700 keys
        log_success "Created keys directory with permissions 700"
    fi

    # Check if certs directory exists (optional)
    if [ ! -d "certs" ]; then
        log_warning "Certificates directory missing: certs/ (optional)"
        log_info "Creating certs directory..."
        mkdir -p certs
        chmod 755 certs
        log_success "Created certs directory"
    fi

    # Ensure centralized logging directories exist (already created but double-check)
    if [ ! -d "$LOGS_SERVICES_DIR" ]; then
        log_info "Creating centralized logging directories..."
        create_logging_directories
        log_success "Created centralized logging directories"
    fi

    # Get essential files list for current environment
    local essential_files_list=($(get_essential_files_list "$ENV_FILE"))

    # Validate each essential file
    for file_path in "${essential_files_list[@]}"; do
        local description=$(get_file_description "$file_path")

        if [ ! -f "$file_path" ]; then
            log_error "MISSING: $file_path"
            log_info "   Description: $description"
            missing_files+=("$file_path")
            all_files_present=false
        else
            log_success "Found: $file_path"

            # Check permissions for key files
            if [[ "$file_path" == *"private"* ]]; then
                local perms=$(stat -c "%a" "$file_path" 2>/dev/null || stat -f "%A" "$file_path" 2>/dev/null || echo "unknown")
                if [ "$perms" != "600" ]; then
                    log_warning "Incorrect permissions on $file_path: $perms (should be 600)"
                    permission_issues+=("chmod 600 $file_path")
                fi
            elif [[ "$file_path" == *"public"* ]] || [[ "$file_path" == *".crt" ]]; then
                local perms=$(stat -c "%a" "$file_path" 2>/dev/null || stat -f "%A" "$file_path" 2>/dev/null || echo "unknown")
                if [ "$perms" != "644" ]; then
                    log_warning "Incorrect permissions on $file_path: $perms (should be 644)"
                    permission_issues+=("chmod 644 $file_path")
                fi
            fi
        fi
    done

    # Handle missing files
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "❌ ${#missing_files[@]} essential file(s) missing for deployment"
        log_header "MISSING FILES ASSISTANCE"

        for file_path in "${missing_files[@]}"; do
            log_info "📄 Missing file: $file_path"
            log_info "   Description: $(get_file_description "$file_path")"
            log_info "   Expected location: $(pwd)/$file_path"

            # Show template for the file
            log_info "   Template content:"
            echo "   ┌─────────────────────────────────────────────────────────────"
            get_file_template "$file_path" | sed 's/^/   │ /'
            echo "   └─────────────────────────────────────────────────────────────"
            echo
        done

        log_warning "🔧 NEXT STEPS:"
        log_warning "1. Create the missing files listed above"
        log_warning "2. Update file contents with your actual values"
        log_warning "3. Set correct permissions (600 for private keys, 644 for public keys)"
        log_warning "4. Run this deployment script again"
        echo

        # Interactive mode
        read -p "$(echo -e "${YELLOW}Press ENTER after you have created the missing files, or Ctrl+C to exit...${NC}")" -r

        # Re-validate after user input
        log_info "Re-validating files..."
        for file_path in "${missing_files[@]}"; do
            if [ -f "$file_path" ]; then
                log_success "✅ Now found: $file_path"
            else
                log_error "❌ Still missing: $file_path"
                all_files_present=false
            fi
        done
    fi

    # Handle permission issues
    if [ ${#permission_issues[@]} -gt 0 ]; then
        log_warning "⚠️  Permission issues detected:"
        for fix_cmd in "${permission_issues[@]}"; do
            log_warning "   Run: $fix_cmd"
        done

        read -p "$(echo -e "${YELLOW}Press ENTER after fixing permissions, or Ctrl+C to exit...${NC}")" -r
    fi

    # Return proper numeric exit code (0 = success, 1 = failure)
    if [ "$all_files_present" = true ]; then
        return 0
    else
        return 1
    fi
}

# Pre-deployment validation
validate_system() {
    log_header "PRE-DEPLOYMENT VALIDATION"

    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        log_error "This script must be run from the project root directory (package.json not found)"
        exit 1
    fi

    # Validate essential files first (interactive)
    log_info "Validating essential files for deployment..."
    if ! validate_essential_files; then
        log_error "Essential files validation failed. Cannot proceed with deployment."
        exit 1
    fi

    # Check if PM2 config exists after file validation
    if [ ! -f "$PM2_CONFIG" ]; then
        log_error "PM2 configuration file $PM2_CONFIG not found"
        log_error "Expected PM2 configs: pm2-local.config.cjs (dev) or pm2-production.config.cjs (prod)"
        exit 1
    fi

    # Check if environment file exists (should be created by validate_essential_files)
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found after validation"
        exit 1
    fi

    log_info "Using environment file: $ENV_FILE"
    log_info "Using PM2 configuration: $PM2_CONFIG"

    # Run comprehensive validation (with graceful handling of missing files)
    log_info "Running comprehensive system validation..."
    if ! node scripts/validate-monorepo.js "$ENV_FILE"; then
        log_warning "System validation had issues, but continuing with deployment..."
        log_warning "Some validation checks may fail due to missing optional files"
    fi

    log_success "Pre-deployment validation completed successfully"
}

# Check system requirements
check_requirements() {
    log_header "SYSTEM REQUIREMENTS CHECK"

    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    NODE_VERSION=$(node --version)
    log_success "Node.js $NODE_VERSION is available"

    # Check pnpm (primary package manager)
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm is not installed. Installing pnpm..."
        if command -v npm &> /dev/null; then
            npm install -g pnpm
        else
            log_error "Neither pnpm nor npm is available. Please install Node.js first."
            exit 1
        fi
    fi
    PNPM_VERSION=$(pnpm --version)
    log_success "pnpm $PNPM_VERSION is available"

    # Check/Install PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 is not installed. Installing PM2..."
        pnpm add -g pm2
    fi
    PM2_VERSION=$(pm2 --version)
    log_success "PM2 $PM2_VERSION is available"

    # Verify pnpm workspace configuration
    if [ -f "pnpm-workspace.yaml" ]; then
        log_success "pnpm workspace configuration found"
    else
        log_warning "pnpm-workspace.yaml not found. Creating default configuration..."
        cat > pnpm-workspace.yaml << EOF
packages:
  - 'apps/*'
  - 'packages/*'
  - 'scripts'
EOF
        log_success "Created pnpm-workspace.yaml"
    fi
}

# Install dependencies
install_dependencies() {
    log_header "DEPENDENCY INSTALLATION"

    log_info "Installing dependencies using pnpm workspace..."

    # Install all workspace dependencies (root, packages, and apps)
    log_info "Running pnpm install (workspace-aware)..."
    pnpm install

    # Verify package installations first (packages may be dependencies for apps)
    log_info "Verifying package dependencies..."
    if [ -d "packages" ]; then
        for package_dir in packages/*/; do
            if [ -f "${package_dir}package.json" ]; then
                package_name=$(basename "$package_dir")
                log_info "Verifying package: $package_name"
                cd "$package_dir" && pnpm list --depth=0 > /dev/null 2>&1 && cd ../..
                log_success "Package $package_name dependencies verified"
            fi
        done
    else
        log_info "No packages directory found, skipping package verification"
    fi

    # Verify installations for enabled services
    source "$ENV_FILE"

    log_info "Verifying application dependencies..."

    if [ "$RUN_BACKEND" = "true" ]; then
        log_info "Verifying backend dependencies..."
        if [ -f "apps/backendApi/package.json" ]; then
            cd apps/backendApi && pnpm list --depth=0 > /dev/null 2>&1 && cd ../..
            log_success "Backend dependencies verified"
        else
            log_warning "Backend package.json not found"
        fi
    fi

    if [ "$RUN_GAMES" = "true" ]; then
        log_info "Verifying games logic dependencies..."
        if [ -f "apps/gamesLogic/package.json" ]; then
            cd apps/gamesLogic && pnpm list --depth=0 > /dev/null 2>&1 && cd ../..
            log_success "Games logic dependencies verified"
        else
            log_warning "Games logic package.json not found"
        fi
    fi

    if [ "$RUN_CACHE" = "true" ]; then
        log_info "Verifying cache manager dependencies..."
        if [ -f "apps/cacheManager/package.json" ]; then
            cd apps/cacheManager && pnpm list --depth=0 > /dev/null 2>&1 && cd ../..
            log_success "Cache manager dependencies verified"
        else
            log_warning "Cache manager package.json not found"
        fi
    fi

    if [ "$RUN_GAMES_FRONTEND" = "true" ]; then
        log_info "Verifying games frontend dependencies..."
        if [ -f "apps/gamesFrontend/package.json" ]; then
            cd apps/gamesFrontend && pnpm list --depth=0 > /dev/null 2>&1 && cd ../..
            log_success "Games frontend dependencies verified"
        else
            log_warning "Games frontend package.json not found"
        fi
    fi

    # Note: gamesUi dependency verification removed as service is deprecated
    # RUN_FRONTEND flag is kept for backward compatibility but gamesUi is not processed

    log_success "All dependencies installed and verified successfully"
}

# Analyze package dependencies
analyze_package_dependencies() {
    local packages_with_deps=()
    local packages_without_deps=()

    if [ -d "packages" ]; then
        for package_dir in packages/*/; do
            if [ -f "${package_dir}package.json" ]; then
                package_name=$(basename "$package_dir")

                # Check if package has dependencies on other workspace packages
                if grep -q '"@.*/' "${package_dir}package.json" 2>/dev/null; then
                    packages_with_deps+=("$package_name")
                else
                    packages_without_deps+=("$package_name")
                fi
            fi
        done
    fi

    # Return packages in dependency order (independent packages first)
    echo "${packages_without_deps[@]} ${packages_with_deps[@]}"
}

# Build packages
build_packages() {
    log_header "PACKAGES BUILD PHASE"

    if [ ! -d "packages" ]; then
        log_info "No packages directory found, skipping package builds"
        return 0
    fi

    # Check if any packages exist
    local package_count=$(find packages -maxdepth 1 -type d -name "*" | wc -l)
    if [ "$package_count" -le 1 ]; then
        log_info "No packages found in packages directory"
        return 0
    fi

    log_info "Building shared packages before applications..."

    # Get packages in dependency order
    log_info "Analyzing package dependencies..."
    local packages_order=($(analyze_package_dependencies))

    if [ ${#packages_order[@]} -eq 0 ]; then
        log_info "No buildable packages found"
        return 0
    fi

    log_info "Package build order: ${packages_order[*]}"

    # Build packages in dependency order
    for package_name in "${packages_order[@]}"; do
        local package_dir="packages/$package_name"

        if [ -f "$package_dir/package.json" ]; then
            log_info "Building package: $package_name"

            cd "$package_dir"

            # Check if package has a build script
            if grep -q '"build"' package.json 2>/dev/null; then
                log_info "Running build for $package_name..."

                if pnpm run build; then
                    log_success "Package $package_name built successfully"
                else
                    log_error "Package $package_name build failed"
                    cd ../..
                    return 1
                fi
            else
                # Check for TypeScript compilation
                if [ -f "tsconfig.json" ] && command -v tsc &> /dev/null; then
                    # Check if there are TypeScript source files to compile
                    if find src -name "*.ts" -o -name "*.tsx" 2>/dev/null | head -1 | grep -q .; then
                        log_info "Running TypeScript compilation for $package_name..."

                        if pnpm exec tsc; then
                            log_success "Package $package_name compiled successfully"
                        else
                            log_error "Package $package_name TypeScript compilation failed"
                            cd ../..
                            return 1
                        fi
                    else
                        log_info "No TypeScript source files found for $package_name, skipping compilation"
                    fi
                else
                    log_info "No build script or TypeScript config found for $package_name, skipping"
                fi
            fi

            cd ../..
        else
            log_warning "Package.json not found for $package_name"
        fi
    done

    log_success "All packages built successfully"
    return 0
}

# Validate package builds
validate_package_builds() {
    log_header "PACKAGE BUILD VALIDATION"

    if [ ! -d "packages" ]; then
        log_info "No packages to validate"
        return 0
    fi

    local validation_failed=false

    for package_dir in packages/*/; do
        if [ -f "${package_dir}package.json" ]; then
            package_name=$(basename "$package_dir")
            log_info "Validating package build: $package_name"

            cd "$package_dir"

            # Check for common build outputs
            local build_outputs=("dist" "lib" "build" "out")
            local found_output=false

            for output_dir in "${build_outputs[@]}"; do
                if [ -d "$output_dir" ]; then
                    log_success "Package $package_name has build output: $output_dir"
                    found_output=true
                    break
                fi
            done

            # Check for TypeScript declaration files
            if find . -name "*.d.ts" -not -path "./node_modules/*" | head -1 | grep -q .; then
                log_success "Package $package_name has TypeScript declarations"
                found_output=true
            fi

            # Check package.json for main/exports fields
            if grep -q '"main"\|"exports"\|"module"' package.json; then
                log_success "Package $package_name has entry points defined"
            else
                log_warning "Package $package_name may not have proper entry points"
            fi

            if [ "$found_output" = false ]; then
                log_warning "Package $package_name may not have been built (no build output found)"
            fi

            cd ../..
        fi
    done

    if [ "$validation_failed" = true ]; then
        log_error "Package build validation failed"
        return 1
    fi

    log_success "Package build validation completed"
    return 0
}

# Build games frontend with environment-aware protection
build_games_frontend_with_protection() {
    log_info "Building games frontend with environment-aware protection..."
    cd apps/gamesFrontend

    # Check if build script exists
    if ! grep -q '"build"' package.json 2>/dev/null; then
        log_warning "No build script found for games frontend"
        cd ../..
        return 1
    fi

    # Determine build command based on environment
    local build_command="build"
    local protection_level="Basic"

    if [ "$NODE_ENV" = "production" ]; then
        # Check if protected build script exists
        if grep -q '"build:protected"' package.json 2>/dev/null; then
            build_command="build:protected"
            protection_level="Maximum (Advanced Obfuscation)"
            log_info "🔒 Production environment detected - using protected build with advanced obfuscation"
        else
            build_command="build:prod"
            protection_level="Enhanced (Terser Minification)"
            log_info "🔒 Production environment detected - using enhanced build with Terser minification"
        fi
    else
        log_info "🔧 Development/staging environment detected - using standard build"
    fi

    log_info "Protection Level: $protection_level"
    log_info "Build Command: pnpm run $build_command"

    # Ensure obfuscation dependencies are installed for protected builds
    if [ "$build_command" = "build:protected" ]; then
        log_info "Verifying obfuscation dependencies for protected build..."

        # Check if javascript-obfuscator is installed
        if ! pnpm list javascript-obfuscator > /dev/null 2>&1; then
            log_warning "javascript-obfuscator not found. Installing obfuscation dependencies..."
            if pnpm add -D javascript-obfuscator terser; then
                log_success "Obfuscation dependencies installed successfully"
            else
                log_error "Failed to install obfuscation dependencies"
                log_warning "Falling back to enhanced production build..."
                build_command="build:prod"
                protection_level="Enhanced (Terser Minification - Fallback)"
            fi
        else
            log_success "Obfuscation dependencies verified"
        fi
    fi

    # Execute the build
    log_info "Executing build: pnpm run $build_command"
    if pnpm run "$build_command"; then
        log_success "✅ Games frontend built successfully with $protection_level protection"

        # Verify build output
        if [ -d "dist" ]; then
            local js_files=$(find dist -name "*.js" | wc -l)
            local total_size=$(du -sh dist 2>/dev/null | cut -f1 || echo "unknown")
            log_info "Build output: $js_files JavaScript files, total size: $total_size"

            # Check if obfuscation was applied (for protected builds)
            if [ "$build_command" = "build:protected" ]; then
                local sample_js=$(find dist -name "*.js" | head -1)
                if [ -n "$sample_js" ] && [ -f "$sample_js" ]; then
                    # Check for obfuscation indicators
                    if grep -q "_0x" "$sample_js" 2>/dev/null; then
                        log_success "🔒 Advanced obfuscation verified - code is protected"
                    else
                        log_warning "⚠️ Obfuscation may not have been applied properly"
                    fi
                fi
            fi
        else
            log_warning "Build directory 'dist' not found after build"
        fi
    else
        log_error "❌ Games frontend build failed"

        # Provide helpful error information
        if [ "$build_command" = "build:protected" ]; then
            log_error "Protected build failed. This could be due to:"
            log_error "  - Missing obfuscation dependencies"
            log_error "  - Obfuscation configuration issues"
            log_error "  - Code compatibility issues with obfuscation"
            log_info "💡 Try running 'pnpm install' in apps/gamesFrontend to ensure all dependencies are installed"
            log_info "💡 Check the obfuscation configuration in vite-plugins/obfuscation-plugin.ts"
        fi

        cd ../..
        return 1
    fi

    cd ../..
    return 0
}

# Build applications with graceful error handling
build_applications() {
    log_header "APPLICATION BUILD PHASE"

    source "$ENV_FILE"
    local build_failures=()
    local critical_failures=()

    # Define critical vs non-critical services
    local critical_services=("gamesFrontend" "backendApi")

    if [ "$RUN_GAMES_FRONTEND" = "true" ]; then
        if ! build_games_frontend_with_protection; then
            log_error "Games frontend build failed"
            critical_failures+=("gamesFrontend")
            return 1
        fi
    fi

    if [ "$RUN_FRONTEND" = "true" ]; then
        log_info "Building games UI with pnpm..."
        cd apps/gamesUi

        # Check if build script exists
        if grep -q '"build"' package.json 2>/dev/null; then
            if pnpm run build; then
                log_success "Games UI built successfully"
            else
                log_warning "Games UI build failed (legacy app - continuing deployment)"
                build_failures+=("gamesUi")
            fi
        else
            log_warning "No build script found for games UI"
        fi
        cd ../..
    fi

    # Build backend applications if they have build scripts
    if [ "$RUN_BACKEND" = "true" ]; then
        log_info "Checking backend build requirements..."
        cd apps/backendApi

        if grep -q '"build"' package.json 2>/dev/null; then
            log_info "Building backend API..."
            if pnpm run build; then
                log_success "Backend API built successfully"
            else
                log_error "Backend API build failed"
                critical_failures+=("backendApi")
                cd ../..
                return 1
            fi
        else
            log_info "No build script found for backend API (may use runtime compilation)"
        fi
        cd ../..
    fi

    # Build cache manager with graceful error handling
    if [ "$RUN_CACHE" = "true" ]; then
        log_info "Checking cache manager build requirements..."
        cd apps/cacheManager

        if grep -q '"build"' package.json 2>/dev/null; then
            log_info "Building cache manager..."
            if pnpm run build; then
                log_success "Cache manager built successfully"
            else
                log_warning "Cache manager build failed (may have test/validation issues - continuing deployment)"
                log_info "Cache manager can run without build (TypeScript runtime compilation)"
                build_failures+=("cacheManager")
            fi
        else
            log_info "No build script found for cache manager (may use runtime compilation)"
        fi
        cd ../..
    fi

    # Build games logic if it has build scripts
    if [ "$RUN_GAMES" = "true" ]; then
        log_info "Checking games logic build requirements..."
        cd apps/gamesLogic

        if grep -q '"build"' package.json 2>/dev/null; then
            log_info "Building games logic..."
            if pnpm run build; then
                log_success "Games logic built successfully"
            else
                log_warning "Games logic build failed (may use runtime compilation - continuing deployment)"
                build_failures+=("gamesLogic")
            fi
        else
            log_info "No build script found for games logic (may use runtime compilation)"
        fi
        cd ../..
    fi

    # Skip workspace-wide build to preserve individual app builds (especially protected builds)
    log_info "Skipping workspace-wide build to preserve individual application builds"
    log_info "Individual applications have already been built with appropriate protection levels"

    # Report build status
    if [ ${#critical_failures[@]} -gt 0 ]; then
        log_error "Critical service builds failed: ${critical_failures[*]}"
        log_error "Cannot proceed with deployment due to critical build failures"
        return 1
    fi

    if [ ${#build_failures[@]} -gt 0 ]; then
        log_warning "Non-critical build failures detected: ${build_failures[*]}"
        log_warning "These services may use runtime compilation or have test utility issues"
        log_info "Continuing deployment as these failures are not critical for service operation"
    fi

    log_success "Application builds completed (critical services built successfully)"
    return 0
}

# Stop existing services
stop_existing_services() {
    log_header "STOPPING EXISTING SERVICES"
    
    log_info "Stopping existing PM2 processes..."
    pm2 stop all || true
    pm2 delete all || true
    
    # Wait for processes to fully stop
    sleep 3
    
    log_success "Existing services stopped"
}

# Start services in dependency order
start_services() {
    log_header "STARTING SERVICES"
    
    log_info "Starting services with PM2..."
    NODE_ENV="$NODE_ENV" pm2 start "$PM2_CONFIG"
    
    # Wait for services to initialize
    log_info "Waiting for services to initialize..."
    sleep 10
    
    log_success "Services started with PM2"
}

# Health check for individual service
check_service_health() {
    local service_name="$1"
    local endpoint=$(get_health_endpoint "$service_name")
    local max_attempts=30
    local attempt=1

    if [ -z "$endpoint" ]; then
        log_info "No health endpoint defined for $service_name, checking PM2 status only"
        return 0
    fi
    
    log_info "Checking health of $service_name at $endpoint..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$endpoint" > /dev/null 2>&1; then
            log_success "$service_name is healthy (attempt $attempt)"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts for $service_name..."
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name failed health check after $max_attempts attempts"
    return 1
}

# Comprehensive health checks
perform_health_checks() {
    log_header "HEALTH CHECKS"
    
    # Check PM2 status
    log_info "Checking PM2 status..."
    pm2 list
    
    # Check individual services
    local failed_services=()
    
    for service in "${SERVICE_ORDER[@]}"; do
        if pm2 list | grep -q "$service.*online"; then
            if check_service_health "$service"; then
                log_success "$service is running and healthy"
            else
                log_error "$service is running but failed health check"
                failed_services+=("$service")
            fi
        else
            # Check if service should be running
            source "$ENV_FILE"
            case "$service" in
                "rgs-backendApi-dev")
                    [ "$RUN_BACKEND" = "true" ] && failed_services+=("$service")
                    ;;
                "rgs-gamesLogic-service")
                    [ "$RUN_GAMES" = "true" ] && failed_services+=("$service")
                    ;;
                "rgs-cacheManager")
                    [ "$RUN_CACHE" = "true" ] && failed_services+=("$service")
                    ;;
                "rgs-gamesFrontend-dev")
                    [ "$RUN_GAMES_FRONTEND" = "true" ] && failed_services+=("$service")
                    ;;
                "rgs-gamesUi-dev")
                    [ "$RUN_FRONTEND" = "true" ] && failed_services+=("$service")
                    ;;
            esac
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "All enabled services are running and healthy"
        return 0
    else
        log_error "Failed services: ${failed_services[*]}"
        return 1
    fi
}

# Integration tests
run_integration_tests() {
    log_header "INTEGRATION TESTS"
    
    source "$ENV_FILE"
    
    # Test backend API if enabled
    if [ "$RUN_BACKEND" = "true" ]; then
        log_info "Testing backend API integration..."
        if curl -f -s "http://localhost:3100/api/health" > /dev/null; then
            log_success "Backend API is accessible"
        else
            log_error "Backend API integration test failed"
            return 1
        fi
    fi
    
    # Test games frontend if enabled
    if [ "$RUN_GAMES_FRONTEND" = "true" ]; then
        log_info "Testing games frontend integration..."
        if curl -f -s "http://localhost:5173/" > /dev/null; then
            log_success "Games frontend is accessible"
            
            # Test lootbox system specifically
            if curl -f -s "http://localhost:5173/ins_lootbox_base.html" > /dev/null; then
                log_success "Lootbox system is accessible"
            else
                log_warning "Lootbox system may not be accessible"
            fi
        else
            log_error "Games frontend integration test failed"
            return 1
        fi
    fi
    
    log_success "Integration tests completed successfully"
}

# Generate deployment report
generate_deployment_report() {
    log_header "DEPLOYMENT REPORT"

    local report_file="$LOGS_REPORTS_DIR/deployment-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# 🚀 Deployment Report

**Date**: $(date)
**Environment**: $NODE_ENV
**Environment File**: $ENV_FILE

## Services Status

$(pm2 list)

## Service URLs

EOF
    
    source "$ENV_FILE"
    
    if [ "$RUN_BACKEND" = "true" ]; then
        echo "- **Backend API**: http://localhost:3100/api" >> "$report_file"
    fi
    
    if [ "$RUN_GAMES" = "true" ]; then
        echo "- **Games Logic**: http://localhost:3000" >> "$report_file"
    fi
    
    if [ "$RUN_GAMES_FRONTEND" = "true" ]; then
        echo "- **Games Frontend**: http://localhost:5173" >> "$report_file"
        echo "- **Lootbox System**: http://localhost:5173/ins_lootbox_base.html" >> "$report_file"

        # Add protection level information
        if [ "$NODE_ENV" = "production" ]; then
            echo "- **Code Protection**: Advanced obfuscation enabled for production" >> "$report_file"
        else
            echo "- **Code Protection**: Basic protection (development/staging)" >> "$report_file"
        fi
    fi
    
    if [ "$RUN_FRONTEND" = "true" ]; then
        echo "- **Games UI**: http://localhost:3001" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Management Commands

\`\`\`bash
# View status
pm2 list

# View logs
pm2 logs

# Restart all services
pm2 restart all

# Stop all services
pm2 stop all
\`\`\`

## Deployment Log

See: $DEPLOYMENT_LOG
EOF
    
    log_success "Deployment report generated: $report_file"
}

# Main deployment function
main() {
    log_header "COMPREHENSIVE MONOREPO DEPLOYMENT"
    log_info "Starting deployment at $(date)"
    log_info "Deployment log: $DEPLOYMENT_LOG"

    if [ "$DRY_RUN" = true ]; then
        log_info "🔍 DRY RUN MODE - No actual deployment will occur"
    fi

    # Run dependency analysis first with centralized logging
    log_info "Running dependency analysis..."
    local dependency_report="$LOGS_REPORTS_DIR/dependency-analysis-$(date +%Y%m%d-%H%M%S).json"
    if ! node scripts/analyze-dependencies.js "$ENV_FILE" > "$dependency_report" 2>&1; then
        log_error "Dependency analysis failed. Please resolve issues before deploying."
        log_error "Analysis report saved to: $dependency_report"
        exit 1
    else
        log_success "Dependency analysis completed successfully (report: $dependency_report)"
    fi

    validate_system
    check_requirements
    install_dependencies

    # Build packages first (shared libraries/utilities)
    log_info "Building packages before applications..."
    if ! build_packages; then
        log_error "Package build failed. Cannot proceed with application builds."
        exit 1
    fi

    # Validate package builds
    if ! validate_package_builds; then
        log_error "Package build validation failed. Cannot proceed with application builds."
        exit 1
    fi

    # Build applications (which may depend on packages)
    log_info "Building applications..."
    if ! build_applications; then
        log_error "Application build failed. Cannot proceed with deployment."
        exit 1
    fi

    if [ "$DRY_RUN" = true ]; then
        log_success "🎉 DRY RUN COMPLETED SUCCESSFULLY!"
        log_info "All validation and build steps passed."
        log_info "Ready for actual deployment. Run without --dry-run flag to deploy."
        return 0
    fi

    stop_existing_services
    start_services

    if perform_health_checks && run_integration_tests; then
        generate_deployment_report

        log_success "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
        log_info "All services are running and healthy."
        log_info "Check the deployment report for service URLs and management commands."

        # Run final integration tests with centralized logging
        log_info "Running final integration tests..."
        local integration_report="$LOGS_REPORTS_DIR/integration-test-report-$(date +%Y%m%d-%H%M%S).json"
        if node scripts/test-integration.js "$ENV_FILE" > "$integration_report" 2>&1; then
            log_success "All integration tests passed! (report: $integration_report)"
        else
            log_warning "Some integration tests failed, but core services are running."
            log_warning "Integration test report saved to: $integration_report"
        fi
    else
        log_error "Deployment completed but some health checks failed."
        log_info "Check PM2 logs for details: pm2 logs"
        exit 1
    fi
}

# Run deployment
main "$@"
