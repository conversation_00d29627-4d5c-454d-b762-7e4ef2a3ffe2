 schema_name 
-------------
 audit
 config
 data
 public
(4 rows)

 schemaname |          tablename           | tableowner | hasindexes | hasrules | hastriggers 
------------+------------------------------+------------+------------+----------+-------------
 audit      | audit_logs                   | doadmin    | t          | f        | f
 audit      | callback_failures            | doadmin    | t          | f        | t
 audit      | financial_events             | doadmin    | t          | f        | f
 audit      | fraud_flags                  | doadmin    | t          | f        | t
 audit      | player_activity_log          | doadmin    | t          | f        | f
 audit      | provably_fair_evidence       | doadmin    | t          | f        | f
 audit      | round_events                 | doadmin    | t          | f        | t
 audit      | security_events              | doadmin    | t          | f        | f
 audit      | sessions_archive             | doadmin    | t          | f        | f
 config     | bet_limits                   | doadmin    | t          | f        | t
 config     | currencies                   | doadmin    | t          | f        | t
 config     | games                        | doadmin    | t          | f        | t
 config     | languages                    | doadmin    | t          | f        | t
 config     | operator_credentials         | doadmin    | t          | f        | t
 config     | operator_currency_config     | doadmin    | t          | f        | t
 config     | operator_custom_endpoints    | doadmin    | t          | f        | t
 config     | operator_default_game_config | doadmin    | t          | f        | t
 config     | operator_game_settings       | doadmin    | t          | f        | t
 config     | redis_cache_config           | doadmin    | t          | f        | f
 config     | rgs_keys                     | doadmin    | t          | f        | t
 config     | rtp_settings                 | doadmin    | t          | f        | t
 config     | system_settings              | doadmin    | t          | f        | t
 config     | ui_themes                    | doadmin    | t          | f        | f
 data       | game_rounds                  | doadmin    | t          | f        | t
 data       | game_sessions                | doadmin    | t          | f        | t
 data       | operator_callbacks           | doadmin    | t          | f        | t
 data       | operator_transactions        | doadmin    | t          | f        | t
 data       | operators                    | doadmin    | t          | f        | t
 data       | round_events                 | doadmin    | t          | f        | f
 data       | session_events               | doadmin    | t          | f        | t
 data       | user_transactions            | doadmin    | t          | f        | t
 data       | users                        | doadmin    | t          | f        | t
(32 rows)

 table_schema |           table_name            |       column_name        | ordinal_position |                   column_default                   | is_nullable |          data_type          | character_maximum_length | numeric_precision | numeric_scale | datetime_precision |       udt_name       
--------------+---------------------------------+--------------------------+------------------+----------------------------------------------------+-------------+-----------------------------+--------------------------+-------------------+---------------+--------------------+----------------------
 audit        | audit_logs                      | log_id                   |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | audit_logs                      | scope                    |                2 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 audit        | audit_logs                      | event                    |                3 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 audit        | audit_logs                      | details                  |                4 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | audit_logs                      | severity                 |                5 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 audit        | audit_logs                      | created_at               |                6 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 audit        | callback_failures               | failure_id               |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | callback_failures               | operator_id              |                2 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 audit        | callback_failures               | transaction_id           |                3 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 audit        | callback_failures               | error_type               |                4 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 audit        | callback_failures               | error_message            |                5 |                                                    | YES         | text                        |                          |                   |               |                    | text
 audit        | callback_failures               | request_data             |                6 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | callback_failures               | retry_count              |                7 | 0                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 audit        | callback_failures               | resolved                 |                8 | false                                              | YES         | boolean                     |                          |                   |               |                    | bool
 audit        | callback_failures               | created_at               |                9 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | callback_failures               | resolved_at              |               10 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | callback_monitoring             | operator_id              |                1 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 audit        | callback_monitoring             | pending_recent           |                2 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 audit        | callback_monitoring             | failed_last_hour         |                3 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 audit        | callback_monitoring             | unresolved_failures      |                4 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 audit        | callback_monitoring             | last_transaction_time    |                5 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | callback_monitoring             | avg_response_time_ms     |                6 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 audit        | financial_events                | event_id                 |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | financial_events                | event_type               |                2 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 audit        | financial_events                | round_id                 |                3 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | financial_events                | session_id               |                4 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | financial_events                | user_id                  |                5 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | financial_events                | operator_id              |                6 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 audit        | financial_events                | amount                   |                7 |                                                    | NO          | numeric                     |                          |                12 |             4 |                    | numeric
 audit        | financial_events                | currency                 |                8 |                                                    | NO          | character varying           |                        3 |                   |               |                    | varchar
 audit        | financial_events                | game_code                |                9 |                                                    | YES         | character varying           |                       12 |                   |               |                    | varchar
 audit        | financial_events                | transaction_id           |               10 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 audit        | financial_events                | related_transaction_id   |               11 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 audit        | financial_events                | metadata                 |               12 | '{}'::jsonb                                        | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | financial_events                | created_at               |               13 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | fraud_flags                     | flag_id                  |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | fraud_flags                     | user_id                  |                2 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | fraud_flags                     | session_id               |                3 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | fraud_flags                     | reason                   |                4 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 audit        | fraud_flags                     | metadata                 |                5 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | fraud_flags                     | flagged_at               |                6 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 audit        | operator_transaction_metrics    | operator_id              |                1 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 audit        | operator_transaction_metrics    | transaction_type         |                2 |                                                    | YES         | character varying           |                       20 |                   |               |                    | varchar
 audit        | operator_transaction_metrics    | hour_bucket              |                3 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | operator_transaction_metrics    | total_transactions       |                4 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 audit        | operator_transaction_metrics    | successful_transactions  |                5 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 audit        | operator_transaction_metrics    | failed_transactions      |                6 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 audit        | operator_transaction_metrics    | total_amount             |                7 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 audit        | operator_transaction_metrics    | avg_processing_time_ms   |                8 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 audit        | player_activity_log             | activity_id              |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | player_activity_log             | user_id                  |                2 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | player_activity_log             | session_id               |                3 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | player_activity_log             | operator_id              |                4 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 audit        | player_activity_log             | activity_type            |                5 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 audit        | player_activity_log             | game_code                |                6 |                                                    | YES         | character varying           |                       12 |                   |               |                    | varchar
 audit        | player_activity_log             | details                  |                7 |                                                    | NO          | jsonb                       |                          |                   |               |                    | jsonb
 audit        | player_activity_log             | created_at               |                8 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | player_activity_log             | retention_until          |                9 |                                                    | NO          | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | provably_fair_evidence          | evidence_id              |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | provably_fair_evidence          | round_id                 |                2 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | provably_fair_evidence          | server_seed_hash         |                3 |                                                    | NO          | character varying           |                       64 |                   |               |                    | varchar
 audit        | provably_fair_evidence          | client_seed              |                4 |                                                    | YES         | character varying           |                       64 |                   |               |                    | varchar
 audit        | provably_fair_evidence          | nonce                    |                5 |                                                    | NO          | integer                     |                          |                32 |             0 |                    | int4
 audit        | provably_fair_evidence          | result_hash              |                6 |                                                    | NO          | character varying           |                       64 |                   |               |                    | varchar
 audit        | provably_fair_evidence          | game_code                |                7 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 audit        | provably_fair_evidence          | created_at               |                8 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | provably_fair_evidence          | retention_until          |                9 |                                                    | NO          | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | round_events                    | event_id                 |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | round_events                    | round_id                 |                2 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | round_events                    | event_type               |                3 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 audit        | round_events                    | data                     |                4 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | round_events                    | created_at               |                5 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 audit        | security_events                 | event_id                 |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | security_events                 | event_type               |                2 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 audit        | security_events                 | operator_id              |                3 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 audit        | security_events                 | session_id               |                4 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | security_events                 | user_id                  |                5 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 audit        | security_events                 | ip_address               |                6 |                                                    | YES         | inet                        |                          |                   |               |                    | inet
 audit        | security_events                 | user_agent               |                7 |                                                    | YES         | text                        |                          |                   |               |                    | text
 audit        | security_events                 | success                  |                8 |                                                    | NO          | boolean                     |                          |                   |               |                    | bool
 audit        | security_events                 | failure_reason           |                9 |                                                    | YES         | text                        |                          |                   |               |                    | text
 audit        | security_events                 | risk_score               |               10 | 0                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 audit        | security_events                 | metadata                 |               11 | '{}'::jsonb                                        | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | security_events                 | created_at               |               12 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 audit        | sessions_archive                | archived_session_id      |                1 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 audit        | sessions_archive                | data                     |                2 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 audit        | sessions_archive                | archived_at              |                3 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | bet_limits                      | id                       |                1 | nextval('config.bet_limits_id_seq'::regclass)      | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | bet_limits                      | operator_id              |                2 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | bet_limits                      | game_code                |                3 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 config       | bet_limits                      | currency                 |                4 | 'USD'::text                                        | YES         | character varying           |                        5 |                   |               |                    | varchar
 config       | bet_limits                      | min_bet                  |                5 |                                                    | NO          | numeric                     |                          |                10 |             2 |                    | numeric
 config       | bet_limits                      | max_bet                  |                6 |                                                    | NO          | numeric                     |                          |                10 |             2 |                    | numeric
 config       | bet_limits                      | bet_levels               |                7 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _numeric
 config       | bet_limits                      | rtp_profile              |                8 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | bet_limits                      | status                   |                9 | 'active'::bet_limit_status                         | YES         | USER-DEFINED                |                          |                   |               |                    | bet_limit_status
 config       | bet_limits                      | created_by               |               10 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | bet_limits                      | updated_by               |               11 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | bet_limits                      | created_at               |               12 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | bet_limits                      | updated_at               |               13 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | currencies                      | currency_code            |                1 |                                                    | NO          | character varying           |                        5 |                   |               |                    | varchar
 config       | currencies                      | symbol                   |                2 |                                                    | NO          | character varying           |                        5 |                   |               |                    | varchar
 config       | currencies                      | pretty_multiplier        |                3 | 1.0                                                | NO          | numeric                     |                          |                10 |             4 |                    | numeric
 config       | currencies                      | is_enabled_by_default    |                4 | true                                               | NO          | boolean                     |                          |                   |               |                    | bool
 config       | currencies                      | created_at               |                5 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | currencies                      | updated_at               |                6 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | games                           | game_code                |                1 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 config       | games                           | display_name             |                2 |                                                    | NO          | character varying           |                      100 |                   |               |                    | varchar
 config       | games                           | engine                   |                3 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 config       | games                           | default_rtp              |                4 |                                                    | YES         | numeric                     |                          |                 5 |             2 |                    | numeric
 config       | games                           | default_theme            |                5 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | games                           | status                   |                6 | 'active'::game_status_type                         | YES         | USER-DEFINED                |                          |                   |               |                    | game_status_type
 config       | games                           | engine_url               |                7 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | games                           | preloader_url            |                8 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | games                           | animation_url            |                9 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | games                           | engine_version           |               10 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | games                           | preloader_version        |               11 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | games                           | animation_version        |               12 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | games                           | created_at               |               13 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | games                           | updated_at               |               14 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | games                           | globally_enabled         |               15 | true                                               | YES         | boolean                     |                          |                   |               |                    | bool
 config       | games                           | description              |               16 |                                                    | YES         | text                        |                          |                   |               |                    | text
 config       | games                           | game_type                |               17 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | games                           | has_bonus_buy            |               18 |                                                    | YES         | boolean                     |                          |                   |               |                    | bool
 config       | games                           | volatility               |               19 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | games                           | min_bet                  |               20 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 config       | games                           | max_bet                  |               21 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 config       | games                           | tags                     |               22 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | games                           | certification            |               23 |                                                    | YES         | text                        |                          |                   |               |                    | text
 config       | games                           | jurisdictions            |               24 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | games                           | supported_countries      |               25 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | games                           | allowed_bet_values       |               26 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _numeric
 config       | games                           | betscale_type            |               27 | 'fixed'::character varying                         | YES         | character varying           |                          |                   |               |                    | varchar
 config       | games                           | bet_step                 |               28 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 config       | games                           | cache_version            |               29 | 1                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 config       | games                           | cache_invalidated_at     |               30 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 config       | languages                       | language_code            |                1 |                                                    | NO          | character varying           |                        5 |                   |               |                    | varchar
 config       | languages                       | language_name            |                2 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 config       | languages                       | is_enabled_by_default    |                3 | true                                               | NO          | boolean                     |                          |                   |               |                    | bool
 config       | languages                       | created_at               |                4 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | languages                       | updated_at               |                5 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_credentials            | id                       |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 config       | operator_credentials            | operator_id              |                2 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_credentials            | environment              |                3 |                                                    | NO          | USER-DEFINED                |                          |                   |               |                    | environment_type
 config       | operator_credentials            | api_key                  |                4 |                                                    | NO          | character varying           |                       64 |                   |               |                    | varchar
 config       | operator_credentials            | public_key               |                5 |                                                    | NO          | text                        |                          |                   |               |                    | text
 config       | operator_credentials            | ip_whitelist             |                6 | ARRAY[]::text[]                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | operator_credentials            | algorithm                |                7 | 'RS256'::character varying                         | NO          | character varying           |                       10 |                   |               |                    | varchar
 config       | operator_credentials            | rgs_key_id               |                8 | 1                                                  | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | operator_credentials            | created_at               |                9 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_credentials            | updated_at               |               10 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_currency_config        | operator_id              |                1 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_currency_config        | currency_code            |                2 |                                                    | NO          | character varying           |                        5 |                   |               |                    | varchar
 config       | operator_currency_config        | pretty_multiplier        |                3 |                                                    | YES         | numeric                     |                          |                10 |             4 |                    | numeric
 config       | operator_currency_config        | is_enabled               |                4 | true                                               | YES         | boolean                     |                          |                   |               |                    | bool
 config       | operator_currency_config        | is_default               |                5 | false                                              | YES         | boolean                     |                          |                   |               |                    | bool
 config       | operator_custom_endpoints       | id                       |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 config       | operator_custom_endpoints       | operator_id              |                2 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_custom_endpoints       | endpoint_type            |                3 |                                                    | NO          | USER-DEFINED                |                          |                   |               |                    | endpoint_type
 config       | operator_custom_endpoints       | endpoint_url             |                4 |                                                    | NO          | character varying           |                      255 |                   |               |                    | varchar
 config       | operator_custom_endpoints       | is_enabled               |                5 | true                                               | YES         | boolean                     |                          |                   |               |                    | bool
 config       | operator_custom_endpoints       | created_at               |                6 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_custom_endpoints       | updated_at               |                7 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_custom_endpoints       | environment              |                8 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | environment_type
 config       | operator_default_game_config    | operator_id              |                1 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_default_game_config    | default_rtp              |                2 |                                                    | YES         | numeric                     |                          |                 5 |             2 |                    | numeric
 config       | operator_default_game_config    | default_min_bet          |                3 |                                                    | YES         | numeric                     |                          |                10 |             2 |                    | numeric
 config       | operator_default_game_config    | default_max_bet          |                4 |                                                    | YES         | numeric                     |                          |                10 |             2 |                    | numeric
 config       | operator_default_game_config    | default_currency         |                5 |                                                    | YES         | character varying           |                        5 |                   |               |                    | varchar
 config       | operator_default_game_config    | autoplay_enabled         |                6 | false                                              | YES         | boolean                     |                          |                   |               |                    | bool
 config       | operator_default_game_config    | buy_bonus_enabled        |                7 | false                                              | YES         | boolean                     |                          |                   |               |                    | bool
 config       | operator_default_game_config    | status                   |                8 | 'active'::game_status_type                         | YES         | USER-DEFINED                |                          |                   |               |                    | game_status_type
 config       | operator_default_game_config    | updated_at               |                9 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_game_settings          | id                       |                1 | nextval('config.game_settings_id_seq'::regclass)   | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | operator_game_settings          | operator_id              |                2 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_game_settings          | game_code                |                3 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 config       | operator_game_settings          | settings                 |                4 |                                                    | NO          | jsonb                       |                          |                   |               |                    | jsonb
 config       | operator_game_settings          | engine_url               |                5 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | operator_game_settings          | preloader_url            |                6 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | operator_game_settings          | rtp_profile              |                7 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | operator_game_settings          | version                  |                8 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | operator_game_settings          | status                   |                9 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | game_settings_status
 config       | operator_game_settings          | created_by               |               10 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_game_settings          | updated_by               |               11 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | operator_game_settings          | created_at               |               12 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_game_settings          | updated_at               |               13 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | operator_game_settings          | display_name             |               14 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | engine                   |               15 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | animation_url            |               16 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | preloader_version        |               17 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | animation_version        |               18 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | theme                    |               19 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | description              |               20 |                                                    | YES         | text                        |                          |                   |               |                    | text
 config       | operator_game_settings          | game_type                |               21 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | has_bonus_buy            |               22 |                                                    | YES         | boolean                     |                          |                   |               |                    | bool
 config       | operator_game_settings          | volatility               |               23 |                                                    | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | min_bet                  |               24 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 config       | operator_game_settings          | max_bet                  |               25 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 config       | operator_game_settings          | tags                     |               26 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | operator_game_settings          | certification            |               27 |                                                    | YES         | text                        |                          |                   |               |                    | text
 config       | operator_game_settings          | jurisdictions            |               28 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | operator_game_settings          | supported_countries      |               29 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _text
 config       | operator_game_settings          | allowed_bet_values       |               30 |                                                    | YES         | ARRAY                       |                          |                   |               |                    | _numeric
 config       | operator_game_settings          | betscale_type            |               31 | 'fixed'::character varying                         | YES         | character varying           |                          |                   |               |                    | varchar
 config       | operator_game_settings          | bet_step                 |               32 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 config       | operator_game_settings          | cache_version            |               33 | 1                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 config       | operator_game_settings          | cache_invalidated_at     |               34 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 config       | redis_cache_config              | cache_key                |                1 |                                                    | NO          | character varying           |                      255 |                   |               |                    | varchar
 config       | redis_cache_config              | ttl_seconds              |                2 | 3600                                               | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | redis_cache_config              | auto_refresh             |                3 | true                                               | YES         | boolean                     |                          |                   |               |                    | bool
 config       | redis_cache_config              | refresh_interval_seconds |                4 | 600                                                | YES         | integer                     |                          |                32 |             0 |                    | int4
 config       | redis_cache_config              | priority                 |                5 | 1                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 config       | redis_cache_config              | description              |                6 |                                                    | YES         | text                        |                          |                   |               |                    | text
 config       | redis_cache_config              | last_updated             |                7 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 config       | redis_cache_config              | created_at               |                8 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 config       | rgs_keys                        | id                       |                1 | nextval('config.rgs_keys_id_seq'::regclass)        | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | rgs_keys                        | kid                      |                2 |                                                    | NO          | text                        |                          |                   |               |                    | text
 config       | rgs_keys                        | private_key_path         |                3 |                                                    | NO          | text                        |                          |                   |               |                    | text
 config       | rgs_keys                        | public_key_pem           |                4 |                                                    | NO          | text                        |                          |                   |               |                    | text
 config       | rgs_keys                        | algorithm                |                5 | 'ES256'::text                                      | NO          | text                        |                          |                   |               |                    | text
 config       | rgs_keys                        | status                   |                6 | 'active'::text                                     | NO          | text                        |                          |                   |               |                    | text
 config       | rgs_keys                        | created_at               |                7 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 config       | rtp_settings                    | id                       |                1 | nextval('config.rtp_settings_id_seq'::regclass)    | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | rtp_settings                    | operator_id              |                2 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 config       | rtp_settings                    | game_code                |                3 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 config       | rtp_settings                    | rtp                      |                4 |                                                    | NO          | numeric                     |                          |                 5 |             2 |                    | numeric
 config       | rtp_settings                    | profile_name             |                5 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | rtp_settings                    | engine_url_per_rtp       |                6 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | rtp_settings                    | preloader_url_per_rtp    |                7 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 config       | rtp_settings                    | engine_version           |                8 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | rtp_settings                    | preloader_version        |                9 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 config       | rtp_settings                    | status                   |               10 | 'active'::rtp_status_type                          | YES         | USER-DEFINED                |                          |                   |               |                    | rtp_status_type
 config       | rtp_settings                    | created_at               |               11 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | rtp_settings                    | updated_at               |               12 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | system_settings                 | setting_key              |                1 |                                                    | NO          | text                        |                          |                   |               |                    | text
 config       | system_settings                 | setting_value            |                2 |                                                    | NO          | text                        |                          |                   |               |                    | text
 config       | system_settings                 | description              |                3 |                                                    | YES         | text                        |                          |                   |               |                    | text
 config       | system_settings                 | updated_at               |                4 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 config       | ui_themes                       | theme_id                 |                1 | nextval('config.ui_themes_theme_id_seq'::regclass) | NO          | integer                     |                          |                32 |             0 |                    | int4
 config       | ui_themes                       | name                     |                2 |                                                    | NO          | character varying           |                      100 |                   |               |                    | varchar
 config       | ui_themes                       | game_code                |                3 |                                                    | YES         | character varying           |                       10 |                   |               |                    | varchar
 config       | ui_themes                       | branding                 |                4 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 config       | ui_themes                       | config                   |                5 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 config       | ui_themes                       | status                   |                6 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | ui_themes_status
 config       | ui_themes                       | created_at               |                7 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 config       | ui_themes                       | updated_at               |                8 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | game_rounds                     | round_id                 |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | game_rounds                     | session_id               |                2 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | game_rounds                     | user_id                  |                3 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | game_rounds                     | game_code                |                4 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 data         | game_rounds                     | bet_amount               |                5 |                                                    | NO          | numeric                     |                          |                12 |             4 |                    | numeric
 data         | game_rounds                     | win_amount               |                6 | 0                                                  | NO          | numeric                     |                          |                10 |             2 |                    | numeric
 data         | game_rounds                     | outcome                  |                8 |                                                    | NO          | character varying           |                       20 |                   |               |                    | varchar
 data         | game_rounds                     | server_seed              |                9 |                                                    | NO          | character varying           |                      255 |                   |               |                    | varchar
 data         | game_rounds                     | result_data              |               10 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | game_rounds                     | metadata                 |               11 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | game_rounds                     | status                   |               12 |                                                    | NO          | USER-DEFINED                |                          |                   |               |                    | round_status_type
 data         | game_rounds                     | demo_mode                |               13 | false                                              | YES         | boolean                     |                          |                   |               |                    | bool
 data         | game_rounds                     | start_time               |               14 | now()                                              | NO          | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | game_rounds                     | end_time                 |               15 |                                                    | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | game_rounds                     | client_seed              |               16 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_rounds                     | nonce                    |               17 |                                                    | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | game_rounds                     | layout_hash              |               18 |                                                    | NO          | text                        |                          |                   |               |                    | text
 data         | game_rounds                     | multiplier               |               19 | 0                                                  | NO          | numeric                     |                          |                10 |             4 |                    | numeric
 data         | game_rounds                     | play_mode                |               20 | 'real'::play_mode                                  | YES         | USER-DEFINED                |                          |                   |               |                    | play_mode
 data         | game_rounds                     | updated_at               |               21 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | game_sessions                   | session_id               |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | game_sessions                   | operator_id              |                2 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 data         | game_sessions                   | user_id                  |                3 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | game_sessions                   | game_code                |                4 |                                                    | NO          | character varying           |                       12 |                   |               |                    | varchar
 data         | game_sessions                   | play_mode                |                5 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | play_mode_type
 data         | game_sessions                   | game_mode                |                6 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | game_mode_type
 data         | game_sessions                   | currency                 |                7 |                                                    | NO          | character varying           |                        5 |                   |               |                    | varchar
 data         | game_sessions                   | balance                  |                8 |                                                    | YES         | numeric                     |                          |                12 |             4 |                    | numeric
 data         | game_sessions                   | language                 |                9 | 'en_gb'::text                                      | YES         | character varying           |                        5 |                   |               |                    | varchar
 data         | game_sessions                   | ip                       |               10 |                                                    | YES         | character varying           |                       39 |                   |               |                    | varchar
 data         | game_sessions                   | country                  |               11 |                                                    | YES         | character varying           |                        2 |                   |               |                    | varchar
 data         | game_sessions                   | lobby_url                |               12 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_sessions                   | cashier_url              |               13 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_sessions                   | device_info              |               14 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_sessions                   | user_agent               |               15 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_sessions                   | status                   |               16 | 'open'::game_status_type                           | YES         | USER-DEFINED                |                          |                   |               |                    | game_status_type
 data         | game_sessions                   | engine_path              |               17 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_sessions                   | animation_path           |               18 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | game_sessions                   | started_at               |               19 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | game_sessions                   | ended_at                 |               20 |                                                    | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | game_sessions                   | platform                 |               21 |                                                    | YES         | character varying           |                       10 |                   |               |                    | varchar
 data         | game_sessions                   | jurisdiction             |               22 |                                                    | YES         | character varying           |                        3 |                   |               |                    | varchar
 data         | game_sessions                   | metadata                 |               23 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | game_sessions                   | promo_eligible           |               24 | true                                               | NO          | boolean                     |                          |                   |               |                    | bool
 data         | game_sessions                   | token                    |               25 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operator_callbacks              | callback_id              |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | operator_callbacks              | operator_id              |                2 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 data         | operator_callbacks              | endpoint                 |                3 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operator_callbacks              | request                  |                4 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | operator_callbacks              | response                 |                5 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | operator_callbacks              | status                   |                6 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | callback_status_type
 data         | operator_callbacks              | retries                  |                7 | 0                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | operator_callbacks              | timestamp                |                8 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | operator_transactions           | transaction_id           |                1 |                                                    | NO          | character varying           |                      255 |                   |               |                    | varchar
 data         | operator_transactions           | round_id                 |                2 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | operator_transactions           | session_id               |                3 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | operator_transactions           | user_id                  |                4 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | operator_transactions           | operator_id              |                5 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 data         | operator_transactions           | transaction_type         |                6 |                                                    | NO          | character varying           |                       20 |                   |               |                    | varchar
 data         | operator_transactions           | amount                   |                7 |                                                    | NO          | numeric                     |                          |                12 |             4 |                    | numeric
 data         | operator_transactions           | currency                 |                8 |                                                    | NO          | character varying           |                        3 |                   |               |                    | varchar
 data         | operator_transactions           | game_code                |                9 |                                                    | YES         | character varying           |                       12 |                   |               |                    | varchar
 data         | operator_transactions           | status                   |               10 | 'pending'::character varying                       | YES         | character varying           |                       20 |                   |               |                    | varchar
 data         | operator_transactions           | operator_response        |               11 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | operator_transactions           | operator_transaction_id  |               12 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operator_transactions           | new_balance              |               13 |                                                    | YES         | numeric                     |                          |                12 |             4 |                    | numeric
 data         | operator_transactions           | related_transaction_id   |               14 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operator_transactions           | promotion_type           |               15 |                                                    | YES         | character varying           |                       20 |                   |               |                    | varchar
 data         | operator_transactions           | tournament_id            |               16 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operator_transactions           | retry_count              |               17 | 0                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | operator_transactions           | created_at               |               18 | now()                                              | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 data         | operator_transactions           | completed_at             |               19 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 data         | operators                       | operator_id              |                1 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 data         | operators                       | name                     |                2 |                                                    | NO          | character varying           |                       25 |                   |               |                    | varchar
 data         | operators                       | domain                   |                3 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operators                       | status                   |               12 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | operator_status_type
 data         | operators                       | created_at               |               13 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | operators                       | updated_at               |               14 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | operators                       | default_currency         |               15 | 'USD'::text                                        | YES         | character varying           |                        5 |                   |               |                    | varchar
 data         | operators                       | default_jurisdiction     |               16 |                                                    | YES         | character varying           |                        5 |                   |               |                    | varchar
 data         | operators                       | lobby_url                |               17 |                                                    | YES         | character varying           |                      128 |                   |               |                    | varchar
 data         | operators                       | cashier_url              |               18 |                                                    | YES         | character varying           |                      128 |                   |               |                    | varchar
 data         | operators                       | base_api_url             |               20 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | operators                       | cache_invalidated_at     |               21 |                                                    | YES         | timestamp without time zone |                          |                   |               |                  6 | timestamp
 data         | operators                       | cache_version            |               22 | 1                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | round_events                    | event_id                 |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | round_events                    | round_id                 |                2 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | round_events                    | session_id               |                3 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | round_events                    | game_code                |                4 |                                                    | NO          | text                        |                          |                   |               |                    | text
 data         | round_events                    | play_mode                |                5 |                                                    | NO          | text                        |                          |                   |               |                    | text
 data         | round_events                    | action_type              |                6 |                                                    | NO          | text                        |                          |                   |               |                    | text
 data         | round_events                    | step                     |                7 | 0                                                  | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | round_events                    | tile_index               |                8 |                                                    | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | round_events                    | bet_amount               |                9 |                                                    | YES         | numeric                     |                          |                12 |             4 |                    | numeric
 data         | round_events                    | multiplier               |               10 |                                                    | YES         | numeric                     |                          |                12 |             4 |                    | numeric
 data         | round_events                    | win_amount               |               11 |                                                    | YES         | numeric                     |                          |                12 |             4 |                    | numeric
 data         | round_events                    | state                    |               12 |                                                    | NO          | jsonb                       |                          |                   |               |                    | jsonb
 data         | round_events                    | metadata                 |               13 | '{}'::jsonb                                        | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | round_events                    | created_at               |               14 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | session_events                  | event_id                 |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | session_events                  | session_id               |                2 |                                                    | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | session_events                  | operator_id              |                3 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 data         | session_events                  | user_id                  |                4 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | session_events                  | event_type               |                5 |                                                    | NO          | character varying           |                       50 |                   |               |                    | varchar
 data         | session_events                  | event_category           |                6 | 'session'::character varying                       | NO          | character varying           |                       20 |                   |               |                    | varchar
 data         | session_events                  | event_data               |                7 | '{}'::jsonb                                        | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | session_events                  | request_data             |                8 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | session_events                  | response_data            |                9 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | session_events                  | status                   |               10 | 'success'::character varying                       | YES         | character varying           |                       20 |                   |               |                    | varchar
 data         | session_events                  | error_message            |               11 |                                                    | YES         | text                        |                          |                   |               |                    | text
 data         | session_events                  | ip_address               |               12 |                                                    | YES         | inet                        |                          |                   |               |                    | inet
 data         | session_events                  | user_agent               |               13 |                                                    | YES         | text                        |                          |                   |               |                    | text
 data         | session_events                  | duration_ms              |               14 |                                                    | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | session_events                  | created_at               |               15 | now()                                              | NO          | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | user_transactions               | txn_id                   |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | user_transactions               | user_id                  |                2 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | user_transactions               | session_id               |                3 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | user_transactions               | round_id                 |                4 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | user_transactions               | type                     |                5 |                                                    | NO          | USER-DEFINED                |                          |                   |               |                    | txn_type
 data         | user_transactions               | amount                   |                6 |                                                    | NO          | numeric                     |                          |                10 |             2 |                    | numeric
 data         | user_transactions               | balance_after            |                7 |                                                    | YES         | numeric                     |                          |                10 |             2 |                    | numeric
 data         | user_transactions               | currency                 |                8 | 'USD'::text                                        | YES         | character varying           |                        5 |                   |               |                    | varchar
 data         | user_transactions               | currency_symbol          |                9 | '$'::text                                          | YES         | character varying           |                       10 |                   |               |                    | varchar
 data         | user_transactions               | token_address            |               10 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | user_transactions               | status                   |               11 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | txn_status_type
 data         | user_transactions               | demo_mode                |               12 | false                                              | YES         | boolean                     |                          |                   |               |                    | bool
 data         | user_transactions               | created_at               |               13 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | user_transactions               | updated_at               |               14 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | users                           | user_id                  |                1 | gen_random_uuid()                                  | NO          | uuid                        |                          |                   |               |                    | uuid
 data         | users                           | operator_id              |                2 |                                                    | NO          | character varying           |                       36 |                   |               |                    | varchar
 data         | users                           | external_user_id         |                3 |                                                    | NO          | text                        |                          |                   |               |                    | text
 data         | users                           | currency                 |                4 | 'USD'::text                                        | NO          | character varying           |                        5 |                   |               |                    | varchar
 data         | users                           | wallet_address           |                5 |                                                    | YES         | character varying           |                      255 |                   |               |                    | varchar
 data         | users                           | metadata                 |                6 |                                                    | YES         | jsonb                       |                          |                   |               |                    | jsonb
 data         | users                           | status                   |                7 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | user_status_type
 data         | users                           | created_at               |                8 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | users                           | updated_at               |                9 | now()                                              | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | v_cache_performance             | operator_id              |                1 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 data         | v_cache_performance             | event_type               |                2 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 data         | v_cache_performance             | total_events             |                3 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_cache_performance             | fast_responses           |                4 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_cache_performance             | slow_responses           |                5 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_cache_performance             | avg_duration_ms          |                6 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 data         | v_cache_performance             | max_duration_ms          |                7 |                                                    | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | v_session_authentication_events | event_id                 |                1 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | v_session_authentication_events | session_id               |                2 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | v_session_authentication_events | operator_id              |                3 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 data         | v_session_authentication_events | user_id                  |                4 |                                                    | YES         | uuid                        |                          |                   |               |                    | uuid
 data         | v_session_authentication_events | event_type               |                5 |                                                    | YES         | character varying           |                       50 |                   |               |                    | varchar
 data         | v_session_authentication_events | status                   |                6 |                                                    | YES         | character varying           |                       20 |                   |               |                    | varchar
 data         | v_session_authentication_events | error_message            |                7 |                                                    | YES         | text                        |                          |                   |               |                    | text
 data         | v_session_authentication_events | duration_ms              |                8 |                                                    | YES         | integer                     |                          |                32 |             0 |                    | int4
 data         | v_session_authentication_events | created_at               |                9 |                                                    | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
 data         | v_session_authentication_events | game_code                |               10 |                                                    | YES         | character varying           |                       12 |                   |               |                    | varchar
 data         | v_session_authentication_events | play_mode                |               11 |                                                    | YES         | USER-DEFINED                |                          |                   |               |                    | play_mode_type
 data         | v_session_authentication_events | currency                 |               12 |                                                    | YES         | character varying           |                        5 |                   |               |                    | varchar
 data         | v_session_authentication_events | operator_name            |               13 |                                                    | YES         | character varying           |                       25 |                   |               |                    | varchar
 data         | v_session_performance           | operator_id              |                1 |                                                    | YES         | character varying           |                       36 |                   |               |                    | varchar
 data         | v_session_performance           | game_code                |                2 |                                                    | YES         | character varying           |                       12 |                   |               |                    | varchar
 data         | v_session_performance           | total_sessions           |                3 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_session_performance           | active_sessions          |                4 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_session_performance           | avg_duration_seconds     |                5 |                                                    | YES         | numeric                     |                          |                   |               |                    | numeric
 data         | v_session_performance           | real_sessions            |                6 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_session_performance           | demo_sessions            |                7 |                                                    | YES         | bigint                      |                          |                64 |             0 |                    | int8
 data         | v_session_performance           | last_session_start       |                8 |                                                    | YES         | timestamp with time zone    |                          |                   |               |                  6 | timestamptz
(411 rows)

 table_schema |          table_name          |          constraint_name          | constraint_type |     column_name     
--------------+------------------------------+-----------------------------------+-----------------+---------------------
 audit        | audit_logs                   | audit_logs_pkey                   | PRIMARY KEY     | log_id
 audit        | callback_failures            | callback_failures_pkey            | PRIMARY KEY     | failure_id
 audit        | financial_events             | financial_events_pkey             | PRIMARY KEY     | event_id
 audit        | fraud_flags                  | fraud_flags_pkey                  | PRIMARY KEY     | flag_id
 audit        | player_activity_log          | player_activity_log_pkey          | PRIMARY KEY     | activity_id
 audit        | provably_fair_evidence       | provably_fair_evidence_pkey       | PRIMARY KEY     | evidence_id
 audit        | round_events                 | round_events_pkey                 | PRIMARY KEY     | event_id
 audit        | security_events              | security_events_pkey              | PRIMARY KEY     | event_id
 audit        | sessions_archive             | sessions_archive_pkey             | PRIMARY KEY     | archived_session_id
 config       | bet_limits                   | bet_limits_pkey                   | PRIMARY KEY     | id
 config       | currencies                   | currencies_pkey                   | PRIMARY KEY     | currency_code
 config       | games                        | games_pkey                        | PRIMARY KEY     | game_code
 config       | languages                    | languages_pkey                    | PRIMARY KEY     | language_code
 config       | operator_credentials         | operator_credentials_pkey         | PRIMARY KEY     | id
 config       | operator_currency_config     | operator_currency_config_pkey     | PRIMARY KEY     | currency_code
 config       | operator_currency_config     | operator_currency_config_pkey     | PRIMARY KEY     | operator_id
 config       | operator_custom_endpoints    | operator_custom_endpoints_pkey    | PRIMARY KEY     | id
 config       | operator_default_game_config | operator_default_game_config_pkey | PRIMARY KEY     | operator_id
 config       | operator_game_settings       | game_settings_pkey                | PRIMARY KEY     | id
 config       | redis_cache_config           | redis_cache_config_pkey           | PRIMARY KEY     | cache_key
 config       | rgs_keys                     | rgs_keys_pkey                     | PRIMARY KEY     | id
 config       | rtp_settings                 | rtp_settings_pkey                 | PRIMARY KEY     | id
 config       | system_settings              | system_settings_pkey              | PRIMARY KEY     | setting_key
 config       | ui_themes                    | ui_themes_pkey                    | PRIMARY KEY     | theme_id
 data         | game_rounds                  | game_rounds_pkey                  | PRIMARY KEY     | round_id
 data         | game_sessions                | game_sessions_pkey                | PRIMARY KEY     | session_id
 data         | operator_callbacks           | operator_callbacks_pkey           | PRIMARY KEY     | callback_id
 data         | operator_transactions        | operator_transactions_pkey        | PRIMARY KEY     | transaction_id
 data         | operators                    | operators_pkey                    | PRIMARY KEY     | operator_id
 data         | round_events                 | round_events_pkey                 | PRIMARY KEY     | event_id
 data         | session_events               | session_events_pkey               | PRIMARY KEY     | event_id
 data         | user_transactions            | user_transactions_pkey            | PRIMARY KEY     | txn_id
 data         | users                        | users_pkey                        | PRIMARY KEY     | user_id
(33 rows)

 table_schema |        table_name        |  column_name  | foreign_table_schema | foreign_table_name | foreign_column_name |               constraint_name               
--------------+--------------------------+---------------+----------------------+--------------------+---------------------+---------------------------------------------
 config       | bet_limits               | game_code     | config               | games              | game_code           | bet_limits_game_code_fkey
 config       | bet_limits               | currency      | config               | currencies         | currency_code       | fk_currency
 config       | operator_credentials     | rgs_key_id    | config               | rgs_keys           | id                  | fk_rgs_key
 config       | operator_currency_config | currency_code | config               | currencies         | currency_code       | fk_operator_currency
 config       | operator_currency_config | currency_code | config               | currencies         | currency_code       | operator_currency_config_currency_code_fkey
 config       | operator_game_settings   | game_code     | config               | games              | game_code           | game_settings_game_code_fkey
 config       | operator_game_settings   | game_code     | config               | games              | game_code           | fk_game_code
 config       | rtp_settings             | game_code     | config               | games              | game_code           | rtp_settings_game_code_fkey
 config       | rtp_settings             | game_code     | config               | games              | game_code           | fk_rtp_game_code
 data         | game_sessions            | user_id       | data                 | users              | user_id             | game_sessions_user_id_fkey
 data         | operator_callbacks       | operator_id   | data                 | operators          | operator_id         | operator_callbacks_operator_id_fkey
 data         | operator_transactions    | round_id      | data                 | game_rounds        | round_id            | fk_operator_transactions_round
 data         | operator_transactions    | session_id    | data                 | game_sessions      | session_id          | fk_operator_transactions_session
 data         | session_events           | user_id       | data                 | users              | user_id             | fk_session_events_user_id
 data         | session_events           | operator_id   | data                 | operators          | operator_id         | fk_session_events_operator_id
 data         | session_events           | session_id    | data                 | game_sessions      | session_id          | fk_session_events_session_id
 data         | user_transactions        | round_id      | data                 | game_rounds        | round_id            | user_transactions_round_id_fkey
 data         | user_transactions        | session_id    | data                 | game_sessions      | session_id          | user_transactions_session_id_fkey
 data         | user_transactions        | user_id       | data                 | users              | user_id             | user_transactions_user_id_fkey
 data         | users                    | operator_id   | data                 | operators          | operator_id         | users_operator_id_fkey
(20 rows)

 schemaname |          tablename           |                    indexname                    |                                                                                                                          indexdef                                                                                                                           
------------+------------------------------+-------------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 audit      | audit_logs                   | audit_logs_pkey                                 | CREATE UNIQUE INDEX audit_logs_pkey ON audit.audit_logs USING btree (log_id)
 audit      | callback_failures            | callback_failures_pkey                          | CREATE UNIQUE INDEX callback_failures_pkey ON audit.callback_failures USING btree (failure_id)
 audit      | callback_failures            | idx_callback_failures_error_type                | CREATE INDEX idx_callback_failures_error_type ON audit.callback_failures USING btree (error_type, created_at)
 audit      | callback_failures            | idx_callback_failures_operator_created          | CREATE INDEX idx_callback_failures_operator_created ON audit.callback_failures USING btree (operator_id, created_at)
 audit      | callback_failures            | idx_callback_failures_unresolved                | CREATE INDEX idx_callback_failures_unresolved ON audit.callback_failures USING btree (resolved, created_at) WHERE (resolved = false)
 audit      | financial_events             | financial_events_pkey                           | CREATE UNIQUE INDEX financial_events_pkey ON audit.financial_events USING btree (event_id)
 audit      | financial_events             | idx_financial_events_operator_type_created      | CREATE INDEX idx_financial_events_operator_type_created ON audit.financial_events USING btree (operator_id, event_type, created_at)
 audit      | financial_events             | idx_financial_events_session_created            | CREATE INDEX idx_financial_events_session_created ON audit.financial_events USING btree (session_id, created_at)
 audit      | financial_events             | idx_financial_events_transaction_id             | CREATE INDEX idx_financial_events_transaction_id ON audit.financial_events USING btree (transaction_id)
 audit      | financial_events             | idx_financial_events_user_created               | CREATE INDEX idx_financial_events_user_created ON audit.financial_events USING btree (user_id, created_at)
 audit      | fraud_flags                  | fraud_flags_pkey                                | CREATE UNIQUE INDEX fraud_flags_pkey ON audit.fraud_flags USING btree (flag_id)
 audit      | player_activity_log          | idx_player_activity_operator_type               | CREATE INDEX idx_player_activity_operator_type ON audit.player_activity_log USING btree (operator_id, activity_type, created_at)
 audit      | player_activity_log          | idx_player_activity_retention                   | CREATE INDEX idx_player_activity_retention ON audit.player_activity_log USING btree (retention_until)
 audit      | player_activity_log          | idx_player_activity_user_created                | CREATE INDEX idx_player_activity_user_created ON audit.player_activity_log USING btree (user_id, created_at)
 audit      | player_activity_log          | player_activity_log_pkey                        | CREATE UNIQUE INDEX player_activity_log_pkey ON audit.player_activity_log USING btree (activity_id)
 audit      | provably_fair_evidence       | idx_provably_fair_game_created                  | CREATE INDEX idx_provably_fair_game_created ON audit.provably_fair_evidence USING btree (game_code, created_at)
 audit      | provably_fair_evidence       | idx_provably_fair_retention                     | CREATE INDEX idx_provably_fair_retention ON audit.provably_fair_evidence USING btree (retention_until)
 audit      | provably_fair_evidence       | idx_provably_fair_round                         | CREATE INDEX idx_provably_fair_round ON audit.provably_fair_evidence USING btree (round_id)
 audit      | provably_fair_evidence       | provably_fair_evidence_pkey                     | CREATE UNIQUE INDEX provably_fair_evidence_pkey ON audit.provably_fair_evidence USING btree (evidence_id)
 audit      | round_events                 | round_events_pkey                               | CREATE UNIQUE INDEX round_events_pkey ON audit.round_events USING btree (event_id)
 audit      | security_events              | idx_security_events_failed_attempts             | CREATE INDEX idx_security_events_failed_attempts ON audit.security_events USING btree (success, created_at) WHERE (success = false)
 audit      | security_events              | idx_security_events_ip_created                  | CREATE INDEX idx_security_events_ip_created ON audit.security_events USING btree (ip_address, created_at)
 audit      | security_events              | idx_security_events_operator_type_created       | CREATE INDEX idx_security_events_operator_type_created ON audit.security_events USING btree (operator_id, event_type, created_at)
 audit      | security_events              | idx_security_events_risk_score                  | CREATE INDEX idx_security_events_risk_score ON audit.security_events USING btree (risk_score, created_at) WHERE (risk_score > 50)
 audit      | security_events              | security_events_pkey                            | CREATE UNIQUE INDEX security_events_pkey ON audit.security_events USING btree (event_id)
 audit      | sessions_archive             | sessions_archive_pkey                           | CREATE UNIQUE INDEX sessions_archive_pkey ON audit.sessions_archive USING btree (archived_session_id)
 config     | bet_limits                   | bet_limits_pkey                                 | CREATE UNIQUE INDEX bet_limits_pkey ON config.bet_limits USING btree (id)
 config     | currencies                   | currencies_pkey                                 | CREATE UNIQUE INDEX currencies_pkey ON config.currencies USING btree (currency_code)
 config     | games                        | games_pkey                                      | CREATE UNIQUE INDEX games_pkey ON config.games USING btree (game_code)
 config     | games                        | idx_games_status_globally_enabled               | CREATE INDEX idx_games_status_globally_enabled ON config.games USING btree (status, globally_enabled, game_code) WHERE (status = 'active'::game_status_type)
 config     | languages                    | languages_pkey                                  | CREATE UNIQUE INDEX languages_pkey ON config.languages USING btree (language_code)
 config     | operator_credentials         | idx_operator_credentials_operator_env           | CREATE INDEX idx_operator_credentials_operator_env ON config.operator_credentials USING btree (operator_id, environment)
 config     | operator_credentials         | operator_credentials_pkey                       | CREATE UNIQUE INDEX operator_credentials_pkey ON config.operator_credentials USING btree (id)
 config     | operator_credentials         | uniq_operator_env                               | CREATE UNIQUE INDEX uniq_operator_env ON config.operator_credentials USING btree (operator_id, environment)
 config     | operator_currency_config     | idx_operator_currency_config_operator_enabled   | CREATE INDEX idx_operator_currency_config_operator_enabled ON config.operator_currency_config USING btree (operator_id, currency_code) WHERE (is_enabled = true)
 config     | operator_currency_config     | operator_currency_config_pkey                   | CREATE UNIQUE INDEX operator_currency_config_pkey ON config.operator_currency_config USING btree (operator_id, currency_code)
 config     | operator_custom_endpoints    | idx_operator_endpoints_operator_env_type        | CREATE INDEX idx_operator_endpoints_operator_env_type ON config.operator_custom_endpoints USING btree (operator_id, environment, endpoint_type) WHERE (is_enabled = true)
 config     | operator_custom_endpoints    | operator_custom_endpoints_pkey                  | CREATE UNIQUE INDEX operator_custom_endpoints_pkey ON config.operator_custom_endpoints USING btree (id)
 config     | operator_custom_endpoints    | uniq_operator_endpoint_type                     | CREATE UNIQUE INDEX uniq_operator_endpoint_type ON config.operator_custom_endpoints USING btree (operator_id, endpoint_type)
 config     | operator_custom_endpoints    | uniq_operator_endpoint_type_env                 | CREATE UNIQUE INDEX uniq_operator_endpoint_type_env ON config.operator_custom_endpoints USING btree (operator_id, environment, endpoint_type)
 config     | operator_default_game_config | operator_default_game_config_pkey               | CREATE UNIQUE INDEX operator_default_game_config_pkey ON config.operator_default_game_config USING btree (operator_id)
 config     | operator_game_settings       | game_settings_pkey                              | CREATE UNIQUE INDEX game_settings_pkey ON config.operator_game_settings USING btree (id)
 config     | redis_cache_config           | redis_cache_config_pkey                         | CREATE UNIQUE INDEX redis_cache_config_pkey ON config.redis_cache_config USING btree (cache_key)
 config     | rgs_keys                     | rgs_keys_kid_key                                | CREATE UNIQUE INDEX rgs_keys_kid_key ON config.rgs_keys USING btree (kid)
 config     | rgs_keys                     | rgs_keys_pkey                                   | CREATE UNIQUE INDEX rgs_keys_pkey ON config.rgs_keys USING btree (id)
 config     | rtp_settings                 | idx_rtp_settings_operator_game_status           | CREATE INDEX idx_rtp_settings_operator_game_status ON config.rtp_settings USING btree (operator_id, game_code, status) WHERE (status = 'active'::rtp_status_type)
 config     | rtp_settings                 | rtp_settings_pkey                               | CREATE UNIQUE INDEX rtp_settings_pkey ON config.rtp_settings USING btree (id)
 config     | system_settings              | system_settings_pkey                            | CREATE UNIQUE INDEX system_settings_pkey ON config.system_settings USING btree (setting_key)
 config     | ui_themes                    | ui_themes_pkey                                  | CREATE UNIQUE INDEX ui_themes_pkey ON config.ui_themes USING btree (theme_id)
 data       | game_rounds                  | game_rounds_pkey                                | CREATE UNIQUE INDEX game_rounds_pkey ON data.game_rounds USING btree (round_id)
 data       | game_sessions                | game_sessions_pkey                              | CREATE UNIQUE INDEX game_sessions_pkey ON data.game_sessions USING btree (session_id)
 data       | game_sessions                | idx_game_sessions_game_code                     | CREATE INDEX idx_game_sessions_game_code ON data.game_sessions USING btree (game_code)
 data       | game_sessions                | idx_game_sessions_operator_id                   | CREATE INDEX idx_game_sessions_operator_id ON data.game_sessions USING btree (operator_id)
 data       | game_sessions                | idx_game_sessions_operator_status               | CREATE INDEX idx_game_sessions_operator_status ON data.game_sessions USING btree (operator_id, status, started_at)
 data       | game_sessions                | idx_game_sessions_session_status_started        | CREATE INDEX idx_game_sessions_session_status_started ON data.game_sessions USING btree (session_id, status, started_at) WHERE (status = 'open'::game_status_type)
 data       | game_sessions                | idx_game_sessions_status                        | CREATE INDEX idx_game_sessions_status ON data.game_sessions USING btree (status)
 data       | game_sessions                | idx_game_sessions_user_game                     | CREATE INDEX idx_game_sessions_user_game ON data.game_sessions USING btree (user_id, game_code)
 data       | game_sessions                | idx_game_sessions_user_status_started           | CREATE INDEX idx_game_sessions_user_status_started ON data.game_sessions USING btree (user_id, status, started_at) WHERE (status = 'open'::game_status_type)
 data       | operator_callbacks           | operator_callbacks_pkey                         | CREATE UNIQUE INDEX operator_callbacks_pkey ON data.operator_callbacks USING btree (callback_id)
 data       | operator_transactions        | idx_operator_transactions_operator_status       | CREATE INDEX idx_operator_transactions_operator_status ON data.operator_transactions USING btree (operator_id, status, created_at)
 data       | operator_transactions        | idx_operator_transactions_related               | CREATE INDEX idx_operator_transactions_related ON data.operator_transactions USING btree (related_transaction_id) WHERE (related_transaction_id IS NOT NULL)
 data       | operator_transactions        | idx_operator_transactions_session_type          | CREATE INDEX idx_operator_transactions_session_type ON data.operator_transactions USING btree (session_id, transaction_type, created_at)
 data       | operator_transactions        | idx_operator_transactions_status_retry          | CREATE INDEX idx_operator_transactions_status_retry ON data.operator_transactions USING btree (status, retry_count, created_at) WHERE ((status)::text = 'pending'::text)
 data       | operator_transactions        | idx_operator_transactions_transaction_id        | CREATE INDEX idx_operator_transactions_transaction_id ON data.operator_transactions USING btree (transaction_id)
 data       | operator_transactions        | operator_transactions_pkey                      | CREATE UNIQUE INDEX operator_transactions_pkey ON data.operator_transactions USING btree (transaction_id)
 data       | operators                    | operators_pkey                                  | CREATE UNIQUE INDEX operators_pkey ON data.operators USING btree (operator_id)
 data       | round_events                 | idx_round_events_game_code                      | CREATE INDEX idx_round_events_game_code ON data.round_events USING btree (game_code)
 data       | round_events                 | idx_round_events_round_id                       | CREATE INDEX idx_round_events_round_id ON data.round_events USING btree (round_id)
 data       | round_events                 | idx_round_events_session_game_created           | CREATE INDEX idx_round_events_session_game_created ON data.round_events USING btree (session_id, game_code, created_at)
 data       | round_events                 | idx_round_events_session_id                     | CREATE INDEX idx_round_events_session_id ON data.round_events USING btree (session_id)
 data       | round_events                 | round_events_pkey                               | CREATE UNIQUE INDEX round_events_pkey ON data.round_events USING btree (event_id)
 data       | session_events               | idx_session_events_created_at                   | CREATE INDEX idx_session_events_created_at ON data.session_events USING btree (created_at)
 data       | session_events               | idx_session_events_event_category               | CREATE INDEX idx_session_events_event_category ON data.session_events USING btree (event_category)
 data       | session_events               | idx_session_events_event_type                   | CREATE INDEX idx_session_events_event_type ON data.session_events USING btree (event_type)
 data       | session_events               | idx_session_events_failed_events                | CREATE INDEX idx_session_events_failed_events ON data.session_events USING btree (session_id, event_type, created_at) WHERE ((status)::text = ANY ((ARRAY['failed'::character varying, 'error'::character varying, 'timeout'::character varying])::text[]))
 data       | session_events               | idx_session_events_operator_category_created    | CREATE INDEX idx_session_events_operator_category_created ON data.session_events USING btree (operator_id, event_category, created_at)
 data       | session_events               | idx_session_events_operator_category_status     | CREATE INDEX idx_session_events_operator_category_status ON data.session_events USING btree (operator_id, event_category, status, created_at)
 data       | session_events               | idx_session_events_operator_id                  | CREATE INDEX idx_session_events_operator_id ON data.session_events USING btree (operator_id)
 data       | session_events               | idx_session_events_session_id                   | CREATE INDEX idx_session_events_session_id ON data.session_events USING btree (session_id)
 data       | session_events               | idx_session_events_session_type_created         | CREATE INDEX idx_session_events_session_type_created ON data.session_events USING btree (session_id, event_type, created_at)
 data       | session_events               | idx_session_events_status                       | CREATE INDEX idx_session_events_status ON data.session_events USING btree (status)
 data       | session_events               | idx_session_events_user_id                      | CREATE INDEX idx_session_events_user_id ON data.session_events USING btree (user_id)
 data       | session_events               | session_events_pkey                             | CREATE UNIQUE INDEX session_events_pkey ON data.session_events USING btree (event_id)
 data       | user_transactions            | user_transactions_pkey                          | CREATE UNIQUE INDEX user_transactions_pkey ON data.user_transactions USING btree (txn_id)
 data       | users                        | users_operator_id_external_user_id_currency_key | CREATE UNIQUE INDEX users_operator_id_external_user_id_currency_key ON data.users USING btree (operator_id, external_user_id, currency)
 data       | users                        | users_pkey                                      | CREATE UNIQUE INDEX users_pkey ON data.users USING btree (user_id)
 data       | users                        | users_wallet_address_key                        | CREATE UNIQUE INDEX users_wallet_address_key ON data.users USING btree (wallet_address)
(87 rows)

 table_schema |          table_name          |               constraint_name                |                                                                                                                                                                                                                                                                                                            check_clause                                                                                                                                                                                                                                                                                                             
--------------+------------------------------+----------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 audit        | audit_logs                   | 17034_17266_1_not_null                       | log_id IS NOT NULL
 audit        | callback_failures            | 17034_19136_1_not_null                       | failure_id IS NOT NULL
 audit        | callback_failures            | 17034_19136_4_not_null                       | error_type IS NOT NULL
 audit        | callback_failures            | 17034_19136_2_not_null                       | operator_id IS NOT NULL
 audit        | financial_events             | 17034_19107_6_not_null                       | operator_id IS NOT NULL
 audit        | financial_events             | 17034_19107_8_not_null                       | currency IS NOT NULL
 audit        | financial_events             | 17034_19107_1_not_null                       | event_id IS NOT NULL
 audit        | financial_events             | 17034_19107_2_not_null                       | event_type IS NOT NULL
 audit        | financial_events             | 17034_19107_7_not_null                       | amount IS NOT NULL
 audit        | financial_events             | 17034_19107_4_not_null                       | session_id IS NOT NULL
 audit        | financial_events             | 17034_19107_5_not_null                       | user_id IS NOT NULL
 audit        | fraud_flags                  | 17034_17289_1_not_null                       | flag_id IS NOT NULL
 audit        | player_activity_log          | 17034_19150_4_not_null                       | operator_id IS NOT NULL
 audit        | player_activity_log          | 17034_19150_7_not_null                       | details IS NOT NULL
 audit        | player_activity_log          | 17034_19150_9_not_null                       | retention_until IS NOT NULL
 audit        | player_activity_log          | 17034_19150_1_not_null                       | activity_id IS NOT NULL
 audit        | player_activity_log          | 17034_19150_2_not_null                       | user_id IS NOT NULL
 audit        | player_activity_log          | 17034_19150_5_not_null                       | activity_type IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_5_not_null                       | nonce IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_9_not_null                       | retention_until IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_7_not_null                       | game_code IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_1_not_null                       | evidence_id IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_2_not_null                       | round_id IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_3_not_null                       | server_seed_hash IS NOT NULL
 audit        | provably_fair_evidence       | 17034_19162_6_not_null                       | result_hash IS NOT NULL
 audit        | round_events                 | 17034_17275_1_not_null                       | event_id IS NOT NULL
 audit        | security_events              | 17034_19121_1_not_null                       | event_id IS NOT NULL
 audit        | security_events              | 17034_19121_8_not_null                       | success IS NOT NULL
 audit        | security_events              | 17034_19121_2_not_null                       | event_type IS NOT NULL
 audit        | sessions_archive             | 17034_17308_1_not_null                       | archived_session_id IS NOT NULL
 config       | bet_limits                   | 17032_17055_3_not_null                       | game_code IS NOT NULL
 config       | bet_limits                   | 17032_17055_5_not_null                       | min_bet IS NOT NULL
 config       | bet_limits                   | 17032_17055_6_not_null                       | max_bet IS NOT NULL
 config       | bet_limits                   | 17032_17055_1_not_null                       | id IS NOT NULL
 config       | currencies                   | 17032_17117_4_not_null                       | is_enabled_by_default IS NOT NULL
 config       | currencies                   | 17032_17117_1_not_null                       | currency_code IS NOT NULL
 config       | currencies                   | 17032_17117_2_not_null                       | symbol IS NOT NULL
 config       | currencies                   | 17032_17117_3_not_null                       | pretty_multiplier IS NOT NULL
 config       | games                        | 17032_17044_1_not_null                       | game_code IS NOT NULL
 config       | games                        | 17032_17044_2_not_null                       | display_name IS NOT NULL
 config       | games                        | 17032_17044_3_not_null                       | engine IS NOT NULL
 config       | languages                    | 17032_17808_1_not_null                       | language_code IS NOT NULL
 config       | languages                    | 17032_17808_3_not_null                       | is_enabled_by_default IS NOT NULL
 config       | languages                    | 17032_17808_2_not_null                       | language_name IS NOT NULL
 config       | operator_credentials         | 17032_18834_4_not_null                       | api_key IS NOT NULL
 config       | operator_credentials         | 17032_18834_1_not_null                       | id IS NOT NULL
 config       | operator_credentials         | 17032_18834_2_not_null                       | operator_id IS NOT NULL
 config       | operator_credentials         | operator_credentials_environment_check       | (environment = ANY (ARRAY['production'::config.environment_type, 'staging'::config.environment_type, 'development'::config.environment_type]))
 config       | operator_credentials         | 17032_18834_3_not_null                       | environment IS NOT NULL
 config       | operator_credentials         | 17032_18834_7_not_null                       | algorithm IS NOT NULL
 config       | operator_credentials         | 17032_18834_5_not_null                       | public_key IS NOT NULL
 config       | operator_credentials         | 17032_18834_8_not_null                       | rgs_key_id IS NOT NULL
 config       | operator_currency_config     | 17032_17128_2_not_null                       | currency_code IS NOT NULL
 config       | operator_currency_config     | 17032_17128_1_not_null                       | operator_id IS NOT NULL
 config       | operator_custom_endpoints    | 17032_18797_1_not_null                       | id IS NOT NULL
 config       | operator_custom_endpoints    | 17032_18797_2_not_null                       | operator_id IS NOT NULL
 config       | operator_custom_endpoints    | 17032_18797_3_not_null                       | endpoint_type IS NOT NULL
 config       | operator_custom_endpoints    | 17032_18797_4_not_null                       | endpoint_url IS NOT NULL
 config       | operator_default_game_config | 17032_17072_1_not_null                       | operator_id IS NOT NULL
 config       | operator_game_settings       | 17032_17101_3_not_null                       | game_code IS NOT NULL
 config       | operator_game_settings       | 17032_17101_4_not_null                       | settings IS NOT NULL
 config       | operator_game_settings       | 17032_17101_1_not_null                       | id IS NOT NULL
 config       | redis_cache_config           | 17032_19055_2_not_null                       | ttl_seconds IS NOT NULL
 config       | redis_cache_config           | 17032_19055_1_not_null                       | cache_key IS NOT NULL
 config       | rgs_keys                     | 17032_18626_4_not_null                       | public_key_pem IS NOT NULL
 config       | rgs_keys                     | 17032_18626_3_not_null                       | private_key_path IS NOT NULL
 config       | rgs_keys                     | 17032_18626_2_not_null                       | kid IS NOT NULL
 config       | rgs_keys                     | 17032_18626_1_not_null                       | id IS NOT NULL
 config       | rgs_keys                     | 17032_18626_6_not_null                       | status IS NOT NULL
 config       | rgs_keys                     | 17032_18626_5_not_null                       | algorithm IS NOT NULL
 config       | rtp_settings                 | 17032_17084_4_not_null                       | rtp IS NOT NULL
 config       | rtp_settings                 | 17032_17084_3_not_null                       | game_code IS NOT NULL
 config       | rtp_settings                 | 17032_17084_1_not_null                       | id IS NOT NULL
 config       | system_settings              | 17032_19187_1_not_null                       | setting_key IS NOT NULL
 config       | system_settings              | 17032_19187_2_not_null                       | setting_value IS NOT NULL
 config       | ui_themes                    | 17032_17143_1_not_null                       | theme_id IS NOT NULL
 config       | ui_themes                    | 17032_17143_2_not_null                       | name IS NOT NULL
 data         | game_rounds                  | 17033_17206_6_not_null                       | win_amount IS NOT NULL
 data         | game_rounds                  | 17033_17206_9_not_null                       | server_seed IS NOT NULL
 data         | game_rounds                  | 17033_17206_8_not_null                       | outcome IS NOT NULL
 data         | game_rounds                  | 17033_17206_19_not_null                      | multiplier IS NOT NULL
 data         | game_rounds                  | 17033_17206_5_not_null                       | bet_amount IS NOT NULL
 data         | game_rounds                  | 17033_17206_4_not_null                       | game_code IS NOT NULL
 data         | game_rounds                  | 17033_17206_1_not_null                       | round_id IS NOT NULL
 data         | game_rounds                  | 17033_17206_14_not_null                      | start_time IS NOT NULL
 data         | game_rounds                  | 17033_17206_18_not_null                      | layout_hash IS NOT NULL
 data         | game_rounds                  | 17033_17206_12_not_null                      | status IS NOT NULL
 data         | game_sessions                | 17033_17188_3_not_null                       | user_id IS NOT NULL
 data         | game_sessions                | 17033_17188_1_not_null                       | session_id IS NOT NULL
 data         | game_sessions                | 17033_17188_2_not_null                       | operator_id IS NOT NULL
 data         | game_sessions                | 17033_17188_4_not_null                       | game_code IS NOT NULL
 data         | game_sessions                | 17033_17188_7_not_null                       | currency IS NOT NULL
 data         | game_sessions                | 17033_17188_24_not_null                      | promo_eligible IS NOT NULL
 data         | game_sessions                | check_status                                 | (status = ANY (ARRAY['open'::game_status_type, 'closed'::game_status_type, 'suspended'::game_status_type, 'disabled'::game_status_type]))
 data         | operator_callbacks           | 17033_17251_1_not_null                       | callback_id IS NOT NULL
 data         | operator_transactions        | 17033_19080_7_not_null                       | amount IS NOT NULL
 data         | operator_transactions        | 17033_19080_6_not_null                       | transaction_type IS NOT NULL
 data         | operator_transactions        | 17033_19080_5_not_null                       | operator_id IS NOT NULL
 data         | operator_transactions        | 17033_19080_4_not_null                       | user_id IS NOT NULL
 data         | operator_transactions        | 17033_19080_3_not_null                       | session_id IS NOT NULL
 data         | operator_transactions        | 17033_19080_1_not_null                       | transaction_id IS NOT NULL
 data         | operator_transactions        | 17033_19080_8_not_null                       | currency IS NOT NULL
 data         | operator_transactions        | operator_transactions_transaction_type_check | ((transaction_type)::text = ANY ((ARRAY['bet'::character varying, 'win'::character varying, 'promo_win'::character varying])::text[]))
 data         | operator_transactions        | operator_transactions_status_check           | ((status)::text = ANY ((ARRAY['pending'::character varying, 'completed'::character varying, 'failed'::character varying, 'cancelled'::character varying])::text[]))
 data         | operators                    | 17033_17154_1_not_null                       | operator_id IS NOT NULL
 data         | operators                    | 17033_17154_2_not_null                       | name IS NOT NULL
 data         | round_events                 | 17033_18954_12_not_null                      | state IS NOT NULL
 data         | round_events                 | 17033_18954_6_not_null                       | action_type IS NOT NULL
 data         | round_events                 | 17033_18954_5_not_null                       | play_mode IS NOT NULL
 data         | round_events                 | 17033_18954_4_not_null                       | game_code IS NOT NULL
 data         | round_events                 | 17033_18954_3_not_null                       | session_id IS NOT NULL
 data         | round_events                 | 17033_18954_2_not_null                       | round_id IS NOT NULL
 data         | round_events                 | 17033_18954_1_not_null                       | event_id IS NOT NULL
 data         | round_events                 | round_events_action_type_check               | (action_type = ANY (ARRAY['start'::text, 'reveal'::text, 'drop'::text, 'roll'::text, 'cashout'::text, 'bomb'::text, 'finish'::text, 'timeout'::text, 'rollback'::text]))
 data         | round_events                 | round_events_play_mode_check                 | (play_mode = ANY (ARRAY['real'::text, 'demo'::text, 'sim'::text]))
 data         | session_events               | 17033_18986_15_not_null                      | created_at IS NOT NULL
 data         | session_events               | 17033_18986_6_not_null                       | event_category IS NOT NULL
 data         | session_events               | 17033_18986_5_not_null                       | event_type IS NOT NULL
 data         | session_events               | 17033_18986_3_not_null                       | operator_id IS NOT NULL
 data         | session_events               | 17033_18986_2_not_null                       | session_id IS NOT NULL
 data         | session_events               | 17033_18986_1_not_null                       | event_id IS NOT NULL
 data         | session_events               | chk_session_events_category                  | ((event_category)::text = ANY ((ARRAY['session'::character varying, 'authentication'::character varying, 'transaction'::character varying, 'operator'::character varying, 'compliance'::character varying])::text[]))
 data         | session_events               | chk_session_events_event_type                | ((event_type)::text = ANY ((ARRAY['authenticate'::character varying, 'authenticate_failed'::character varying, 'session_created'::character varying, 'session_closed'::character varying, 'operator_validate'::character varying, 'operator_validate_failed'::character varying, 'balance_update'::character varying, 'balance_inquiry'::character varying, 'transaction_bet'::character varying, 'transaction_win'::character varying, 'transaction_failed'::character varying, 'api_call'::character varying, 'api_error'::character varying, 'fraud_check'::character varying, 'compliance_check'::character varying])::text[]))
 data         | session_events               | chk_session_events_status                    | ((status)::text = ANY ((ARRAY['success'::character varying, 'failed'::character varying, 'pending'::character varying, 'timeout'::character varying, 'error'::character varying])::text[]))
 data         | user_transactions            | 17033_17222_5_not_null                       | type IS NOT NULL
 data         | user_transactions            | 17033_17222_6_not_null                       | amount IS NOT NULL
 data         | user_transactions            | 17033_17222_1_not_null                       | txn_id IS NOT NULL
 data         | users                        | 17033_17167_2_not_null                       | operator_id IS NOT NULL
 data         | users                        | 17033_17167_4_not_null                       | currency IS NOT NULL
 data         | users                        | 17033_17167_1_not_null                       | user_id IS NOT NULL
 data         | users                        | 17033_17167_3_not_null                       | external_user_id IS NOT NULL
(131 rows)

 table_schema |           table_name            |                                                                             view_definition                                                                             
--------------+---------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 audit        | callback_monitoring             |  SELECT o.operator_id,                                                                                                                                                 +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (((o.status)::text = 'pending'::text) AND (o.created_at > (now() - '00:05:00'::interval))) THEN 1                                                     +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS pending_recent,                                                                                                                                        +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (((o.status)::text = 'failed'::text) AND (o.created_at > (now() - '01:00:00'::interval))) THEN 1                                                      +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS failed_last_hour,                                                                                                                                      +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN ((cf.resolved = false) AND (cf.created_at > (now() - '01:00:00'::interval))) THEN 1                                                                   +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS unresolved_failures,                                                                                                                                   +
              |                                 |     max(o.created_at) AS last_transaction_time,                                                                                                                        +
              |                                 |     avg(                                                                                                                                                               +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (((o.status)::text = 'completed'::text) AND (o.completed_at IS NOT NULL)) THEN (EXTRACT(epoch FROM (o.completed_at - o.created_at)) * (1000)::numeric)+
              |                                 |             ELSE NULL::numeric                                                                                                                                         +
              |                                 |         END) AS avg_response_time_ms                                                                                                                                   +
              |                                 |    FROM (data.operator_transactions o                                                                                                                                  +
              |                                 |      LEFT JOIN audit.callback_failures cf ON (((o.operator_id)::text = (cf.operator_id)::text)))                                                                       +
              |                                 |   WHERE (o.created_at > (now() - '24:00:00'::interval))                                                                                                                +
              |                                 |   GROUP BY o.operator_id;
 audit        | operator_transaction_metrics    |  SELECT operator_id,                                                                                                                                                   +
              |                                 |     transaction_type,                                                                                                                                                  +
              |                                 |     date_trunc('hour'::text, created_at) AS hour_bucket,                                                                                                               +
              |                                 |     count(*) AS total_transactions,                                                                                                                                    +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN ((status)::text = 'completed'::text) THEN 1                                                                                                           +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS successful_transactions,                                                                                                                               +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN ((status)::text = 'failed'::text) THEN 1                                                                                                              +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS failed_transactions,                                                                                                                                   +
              |                                 |     sum(                                                                                                                                                               +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN ((status)::text = 'completed'::text) THEN amount                                                                                                      +
              |                                 |             ELSE (0)::numeric                                                                                                                                          +
              |                                 |         END) AS total_amount,                                                                                                                                          +
              |                                 |     avg(                                                                                                                                                               +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (((status)::text = 'completed'::text) AND (completed_at IS NOT NULL)) THEN (EXTRACT(epoch FROM (completed_at - created_at)) * (1000)::numeric)        +
              |                                 |             ELSE NULL::numeric                                                                                                                                         +
              |                                 |         END) AS avg_processing_time_ms                                                                                                                                 +
              |                                 |    FROM data.operator_transactions                                                                                                                                     +
              |                                 |   WHERE (created_at > (now() - '7 days'::interval))                                                                                                                    +
              |                                 |   GROUP BY operator_id, transaction_type, (date_trunc('hour'::text, created_at));
 data         | v_cache_performance             |  SELECT operator_id,                                                                                                                                                   +
              |                                 |     event_type,                                                                                                                                                        +
              |                                 |     count(*) AS total_events,                                                                                                                                          +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (duration_ms < 100) THEN 1                                                                                                                            +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS fast_responses,                                                                                                                                        +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (duration_ms >= 100) THEN 1                                                                                                                           +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS slow_responses,                                                                                                                                        +
              |                                 |     avg(duration_ms) AS avg_duration_ms,                                                                                                                               +
              |                                 |     max(duration_ms) AS max_duration_ms                                                                                                                                +
              |                                 |    FROM data.session_events se                                                                                                                                         +
              |                                 |   WHERE ((created_at > (now() - '01:00:00'::interval)) AND ((event_category)::text = 'authentication'::text))                                                          +
              |                                 |   GROUP BY operator_id, event_type                                                                                                                                     +
              |                                 |   ORDER BY (avg(duration_ms)) DESC;
 data         | v_session_authentication_events |  SELECT se.event_id,                                                                                                                                                   +
              |                                 |     se.session_id,                                                                                                                                                     +
              |                                 |     se.operator_id,                                                                                                                                                    +
              |                                 |     se.user_id,                                                                                                                                                        +
              |                                 |     se.event_type,                                                                                                                                                     +
              |                                 |     se.status,                                                                                                                                                         +
              |                                 |     se.error_message,                                                                                                                                                  +
              |                                 |     se.duration_ms,                                                                                                                                                    +
              |                                 |     se.created_at,                                                                                                                                                     +
              |                                 |     gs.game_code,                                                                                                                                                      +
              |                                 |     gs.play_mode,                                                                                                                                                      +
              |                                 |     gs.currency,                                                                                                                                                       +
              |                                 |     o.name AS operator_name                                                                                                                                            +
              |                                 |    FROM ((data.session_events se                                                                                                                                       +
              |                                 |      JOIN data.game_sessions gs ON ((se.session_id = gs.session_id)))                                                                                                  +
              |                                 |      JOIN data.operators o ON (((se.operator_id)::text = (o.operator_id)::text)))                                                                                      +
              |                                 |   WHERE ((se.event_category)::text = 'authentication'::text)                                                                                                           +
              |                                 |   ORDER BY se.created_at DESC;
 data         | v_session_performance           |  SELECT operator_id,                                                                                                                                                   +
              |                                 |     game_code,                                                                                                                                                         +
              |                                 |     count(*) AS total_sessions,                                                                                                                                        +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (status = 'open'::game_status_type) THEN 1                                                                                                            +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS active_sessions,                                                                                                                                       +
              |                                 |     avg(EXTRACT(epoch FROM (COALESCE(ended_at, now()) - started_at))) AS avg_duration_seconds,                                                                         +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (play_mode = 'real'::play_mode_type) THEN 1                                                                                                           +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS real_sessions,                                                                                                                                         +
              |                                 |     count(                                                                                                                                                             +
              |                                 |         CASE                                                                                                                                                           +
              |                                 |             WHEN (play_mode = 'demo'::play_mode_type) THEN 1                                                                                                           +
              |                                 |             ELSE NULL::integer                                                                                                                                         +
              |                                 |         END) AS demo_sessions,                                                                                                                                         +
              |                                 |     max(started_at) AS last_session_start                                                                                                                              +
              |                                 |    FROM data.game_sessions gs                                                                                                                                          +
              |                                 |   WHERE (started_at > (now() - '24:00:00'::interval))                                                                                                                  +
              |                                 |   GROUP BY operator_id, game_code                                                                                                                                      +
              |                                 |   ORDER BY (count(*)) DESC;
(5 rows)

 routine_schema |           routine_name           | routine_type | data_type |                              routine_definition                              
----------------+----------------------------------+--------------+-----------+------------------------------------------------------------------------------
 audit          | cleanup_expired_data             | FUNCTION     | integer   |                                                                             +
                |                                  |              |           | DECLARE                                                                     +
                |                                  |              |           |     deleted_count INTEGER := 0;                                             +
                |                                  |              |           | BEGIN                                                                       +
                |                                  |              |           |     -- Remove expired player activity logs (GDPR compliance)                +
                |                                  |              |           |     DELETE FROM audit.player_activity_log                                   +
                |                                  |              |           |     WHERE retention_until < NOW();                                          +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     GET DIAGNOSTICS deleted_count = ROW_COUNT;                              +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     -- Archive old financial events (keep for 7 years, then move to archive)+
                |                                  |              |           |     INSERT INTO audit.financial_events_archive                              +
                |                                  |              |           |     SELECT * FROM audit.financial_events                                    +
                |                                  |              |           |     WHERE created_at < NOW() - INTERVAL '1 year';                           +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     -- Remove resolved callback failures older than 30 days                 +
                |                                  |              |           |     DELETE FROM audit.callback_failures                                     +
                |                                  |              |           |     WHERE resolved = true AND resolved_at < NOW() - INTERVAL '30 days';     +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     -- Clean up old security events (keep for 2 years)                      +
                |                                  |              |           |     DELETE FROM audit.security_events                                       +
                |                                  |              |           |     WHERE created_at < NOW() - INTERVAL '2 years';                          +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     RETURN deleted_count;                                                   +
                |                                  |              |           | END;                                                                        +
                |                                  |              |           | 
 audit          | notify_critical_callback_failure | FUNCTION     | trigger   |                                                                             +
                |                                  |              |           | BEGIN                                                                       +
                |                                  |              |           |     -- Notify application of critical callback failures                     +
                |                                  |              |           |     IF NEW.retry_count >= 3 AND NOT NEW.resolved THEN                       +
                |                                  |              |           |         PERFORM pg_notify('critical_callback_failure',                      +
                |                                  |              |           |             json_build_object(                                              +
                |                                  |              |           |                 'operator_id', NEW.operator_id,                             +
                |                                  |              |           |                 'transaction_id', NEW.transaction_id,                       +
                |                                  |              |           |                 'error_type', NEW.error_type,                               +
                |                                  |              |           |                 'retry_count', NEW.retry_count                              +
                |                                  |              |           |             )::text                                                         +
                |                                  |              |           |         );                                                                  +
                |                                  |              |           |     END IF;                                                                 +
                |                                  |              |           |     RETURN NEW;                                                             +
                |                                  |              |           | END;                                                                        +
                |                                  |              |           | 
 config         | touch_updated_at                 | FUNCTION     | trigger   |                                                                             +
                |                                  |              |           | BEGIN                                                                       +
                |                                  |              |           |   NEW.updated_at := NOW();                                                  +
                |                                  |              |           |   RETURN NEW;                                                               +
                |                                  |              |           | END;                                                                        +
                |                                  |              |           | 
 public         | cleanup_old_sessions             | FUNCTION     | integer   |                                                                             +
                |                                  |              |           | DECLARE                                                                     +
                |                                  |              |           |     deleted_count INTEGER;                                                  +
                |                                  |              |           | BEGIN                                                                       +
                |                                  |              |           |     -- Archive old closed sessions                                          +
                |                                  |              |           |     INSERT INTO audit.sessions_archive                                      +
                |                                  |              |           |     SELECT                                                                  +
                |                                  |              |           |         gen_random_uuid() as archived_session_id,                           +
                |                                  |              |           |         session_id,                                                         +
                |                                  |              |           |         operator_id,                                                        +
                |                                  |              |           |         user_id,                                                            +
                |                                  |              |           |         game_code,                                                          +
                |                                  |              |           |         play_mode,                                                          +
                |                                  |              |           |         currency,                                                           +
                |                                  |              |           |         started_at,                                                         +
                |                                  |              |           |         ended_at,                                                           +
                |                                  |              |           |         NOW() as archived_at                                                +
                |                                  |              |           |     FROM data.game_sessions                                                 +
                |                                  |              |           |     WHERE status = 'closed'                                                 +
                |                                  |              |           |         AND ended_at < NOW() - INTERVAL '7 days';                           +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     -- Delete old closed sessions                                           +
                |                                  |              |           |     DELETE FROM data.game_sessions                                          +
                |                                  |              |           |     WHERE status = 'closed'                                                 +
                |                                  |              |           |         AND ended_at < NOW() - INTERVAL '7 days';                           +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     GET DIAGNOSTICS deleted_count = ROW_COUNT;                              +
                |                                  |              |           |                                                                             +
                |                                  |              |           |     RETURN deleted_count;                                                   +
                |                                  |              |           | END;                                                                        +
                |                                  |              |           | 
 public         | invalidate_cache_version         | FUNCTION     | trigger   |                                                                             +
                |                                  |              |           | BEGIN                                                                       +
                |                                  |              |           |     NEW.cache_version = COALESCE(OLD.cache_version, 0) + 1;                 +
                |                                  |              |           |     NEW.cache_invalidated_at = NOW();                                       +
                |                                  |              |           |     RETURN NEW;                                                             +
                |                                  |              |           | END;                                                                        +
                |                                  |              |           | 
 public         | update_updated_at_column         | FUNCTION     | trigger   |                                                                             +
                |                                  |              |           | BEGIN                                                                       +
                |                                  |              |           |   NEW.updated_at = NOW();                                                   +
                |                                  |              |           |   RETURN NEW;                                                               +
                |                                  |              |           | END;                                                                        +
                |                                  |              |           | 
(6 rows)

 trigger_schema |                 trigger_name                  | event_manipulation | event_object_schema |   event_object_table   |                     action_statement                      | action_timing 
----------------+-----------------------------------------------+--------------------+---------------------+------------------------+-----------------------------------------------------------+---------------
 audit          | trigger_critical_callback_failure             | INSERT             | audit               | callback_failures      | EXECUTE FUNCTION audit.notify_critical_callback_failure() | AFTER
 audit          | trigger_critical_callback_failure             | UPDATE             | audit               | callback_failures      | EXECUTE FUNCTION audit.notify_critical_callback_failure() | AFTER
 config         | trg_games_cache_invalidation                  | UPDATE             | config              | games                  | EXECUTE FUNCTION invalidate_cache_version()               | BEFORE
 config         | trg_operator_game_settings_cache_invalidation | UPDATE             | config              | operator_game_settings | EXECUTE FUNCTION invalidate_cache_version()               | BEFORE
 config         | trg_update_setting_timestamp                  | UPDATE             | config              | system_settings        | EXECUTE FUNCTION config.touch_updated_at()                | BEFORE
 data           | trg_update_game_rounds_updated_at             | UPDATE             | data                | game_rounds            | EXECUTE FUNCTION update_updated_at_column()               | BEFORE
 data           | trg_operators_cache_invalidation              | UPDATE             | data                | operators              | EXECUTE FUNCTION invalidate_cache_version()               | BEFORE
(7 rows)

 operator_transactions_exists 
------------------------------
 t
(1 row)

       column_name       |          data_type          | is_nullable |        column_default        | character_maximum_length 
-------------------------+-----------------------------+-------------+------------------------------+--------------------------
 transaction_id          | character varying           | NO          |                              |                      255
 round_id                | uuid                        | YES         |                              |                         
 session_id              | uuid                        | NO          |                              |                         
 user_id                 | uuid                        | NO          |                              |                         
 operator_id             | character varying           | NO          |                              |                       36
 transaction_type        | character varying           | NO          |                              |                       20
 amount                  | numeric                     | NO          |                              |                         
 currency                | character varying           | NO          |                              |                        3
 game_code               | character varying           | YES         |                              |                       12
 status                  | character varying           | YES         | 'pending'::character varying |                       20
 operator_response       | jsonb                       | YES         |                              |                         
 operator_transaction_id | character varying           | YES         |                              |                      255
 new_balance             | numeric                     | YES         |                              |                         
 related_transaction_id  | character varying           | YES         |                              |                      255
 promotion_type          | character varying           | YES         |                              |                       20
 tournament_id           | character varying           | YES         |                              |                      255
 retry_count             | integer                     | YES         | 0                            |                         
 created_at              | timestamp without time zone | YES         | now()                        |                         
 completed_at            | timestamp without time zone | YES         |                              |                         
(19 rows)

       table_name       | table_exists 
------------------------+--------------
 financial_events       | t
 security_events        | t
 callback_failures      | t
 player_activity_log    | t
 provably_fair_evidence | t
(5 rows)

                 indexname                 |                                                                                 indexdef                                                                                 
-------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 operator_transactions_pkey                | CREATE UNIQUE INDEX operator_transactions_pkey ON data.operator_transactions USING btree (transaction_id)
 idx_operator_transactions_session_type    | CREATE INDEX idx_operator_transactions_session_type ON data.operator_transactions USING btree (session_id, transaction_type, created_at)
 idx_operator_transactions_operator_status | CREATE INDEX idx_operator_transactions_operator_status ON data.operator_transactions USING btree (operator_id, status, created_at)
 idx_operator_transactions_status_retry    | CREATE INDEX idx_operator_transactions_status_retry ON data.operator_transactions USING btree (status, retry_count, created_at) WHERE ((status)::text = 'pending'::text)
 idx_operator_transactions_transaction_id  | CREATE INDEX idx_operator_transactions_transaction_id ON data.operator_transactions USING btree (transaction_id)
 idx_operator_transactions_related         | CREATE INDEX idx_operator_transactions_related ON data.operator_transactions USING btree (related_transaction_id) WHERE (related_transaction_id IS NOT NULL)
(6 rows)

 schemaname |          tablename           |    size    | row_count_estimate 
------------+------------------------------+------------+--------------------
 data       | session_events               | 240 kB     |                  0
 data       | round_events                 | 168 kB     |                 38
 data       | game_rounds                  | 88 kB      |                 40
 config     | operator_custom_endpoints    | 72 kB      |               4077
 data       | game_sessions                | 72 kB      |                  0
 config     | operator_credentials         | 64 kB      |               8308
 data       | operator_transactions        | 56 kB      |                  0
 config     | rgs_keys                     | 48 kB      |                  0
 audit      | financial_events             | 48 kB      |                  0
 config     | games                        | 48 kB      |              26235
 audit      | security_events              | 48 kB      |                  0
 config     | operator_currency_config     | 40 kB      |               4108
 audit      | callback_failures            | 40 kB      |                  0
 audit      | player_activity_log          | 40 kB      |                  0
 data       | users                        | 32 kB      |                  0
 data       | operators                    | 32 kB      |               4219
 audit      | provably_fair_evidence       | 32 kB      |                  0
 config     | redis_cache_config           | 32 kB      |                  0
 config     | operator_game_settings       | 32 kB      |              12587
 config     | system_settings              | 32 kB      |                  0
 config     | currencies                   | 24 kB      |              15494
 config     | languages                    | 24 kB      |                  0
 config     | operator_default_game_config | 24 kB      |                  0
 config     | rtp_settings                 | 24 kB      |                  0
 audit      | round_events                 | 16 kB      |                  0
 data       | operator_callbacks           | 16 kB      |                  0
 audit      | sessions_archive             | 16 kB      |                  0
 config     | ui_themes                    | 16 kB      |                  0
 config     | bet_limits                   | 16 kB      |                  0
 audit      | audit_logs                   | 16 kB      |                  0
 audit      | fraud_flags                  | 16 kB      |                  0
 data       | user_transactions            | 8192 bytes |                  0
(32 rows)

psql:docs/database/analysis-queries.sql:227: ERROR:  column "tablename" does not exist
LINE 3:     tablename,
            ^
