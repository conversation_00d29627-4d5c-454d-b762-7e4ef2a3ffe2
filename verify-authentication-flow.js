#!/usr/bin/env node

/**
 * Comprehensive Authentication Flow Verification
 * Tests the complete casino-grade session management system
 */

const API_BASE = process.env.BASE_API_ROUTE || 'http://localhost:3100';
const FRONTEND_BASE = process.env.BASE_GAME_URL || 'http://localhost:5174';

console.log('🔐 Starting Authentication Flow Verification...');
console.log(`📡 API Base: ${API_BASE}`);
console.log(`🎮 Frontend Base: ${FRONTEND_BASE}`);

/**
 * Test 1: Session Creation and Authentication
 */
async function testSessionCreationAndAuthentication() {
  console.log('\n🧪 Test 1: Session Creation and Authentication');
  
  try {
    // Create a new session
    const createResponse = await fetch(`${API_BASE}/api/session/create`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operator_id: 'demo_server',
        external_user_id: 'test_user_001',
        game_code: 'ins_mines',
        currency: 'USD',
        play_mode: 'demo'
      })
    });

    if (!createResponse.ok) {
      throw new Error(`Session creation failed: ${createResponse.status}`);
    }

    const sessionData = await createResponse.json();
    console.log('  ✅ Session created:', sessionData.session_id);

    // Authenticate the session
    const authResponse = await fetch(`${API_BASE}/api/session/authenticate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        session_id: sessionData.session_id,
        operator_id: 'demo_server',
        external_user_id: 'test_user_001',
        game_code: 'ins_mines',
        currency: 'USD',
        play_mode: 'demo'
      })
    });

    if (!authResponse.ok) {
      throw new Error(`Authentication failed: ${authResponse.status}`);
    }

    const authData = await authResponse.json();
    console.log('  ✅ Session authenticated:', authData.authenticated);
    console.log('  💰 Balance:', authData.balance);

    return sessionData;

  } catch (error) {
    console.error('  ❌ Session creation/authentication failed:', error.message);
    throw error;
  }
}

/**
 * Test 2: Operator Games Configuration
 */
async function testOperatorGamesConfig() {
  console.log('\n🧪 Test 2: Operator Games Configuration');
  
  try {
    // Test demo_server games
    const demoResponse = await fetch(`${API_BASE}/api/config/operator-games`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        operator_id: 'demo_server'
      })
    });

    if (!demoResponse.ok) {
      throw new Error(`Demo operator games failed: ${demoResponse.status}`);
    }

    const demoGames = await demoResponse.json();
    console.log('  ✅ Demo server games:', demoGames.games);
    console.log('  📊 Total games:', demoGames.total_games);

    // Test all games endpoint
    const allGamesResponse = await fetch(`${API_BASE}/api/config/all-games`);
    
    if (allGamesResponse.ok) {
      const allGames = await allGamesResponse.json();
      console.log('  ✅ All available games:', allGames.total_games);
    } else {
      console.log('  ⚠️ All games endpoint not available (expected in some setups)');
    }

    return demoGames.games;

  } catch (error) {
    console.error('  ❌ Operator games configuration failed:', error.message);
    throw error;
  }
}

/**
 * Test 3: Game Launch URL Generation
 */
async function testGameLaunchUrls(sessionData, availableGames) {
  console.log('\n🧪 Test 3: Game Launch URL Generation');
  
  try {
    for (const gameCode of availableGames.slice(0, 2)) { // Test first 2 games
      const launchUrl = new URL(`/${gameCode}.html`, FRONTEND_BASE);
      
      // Add session parameters
      launchUrl.searchParams.set('session_id', sessionData.session_id);
      launchUrl.searchParams.set('operator_id', 'demo_server');
      launchUrl.searchParams.set('currency', 'USD');
      launchUrl.searchParams.set('play_mode', 'demo');
      launchUrl.searchParams.set('game_code', gameCode);

      console.log(`  🚀 ${gameCode} launch URL:`, launchUrl.toString());

      // Test if the game HTML exists
      try {
        const gameResponse = await fetch(launchUrl.toString(), { method: 'HEAD' });
        if (gameResponse.ok) {
          console.log(`  ✅ ${gameCode} game available`);
        } else {
          console.log(`  ⚠️ ${gameCode} game not built/available`);
        }
      } catch (error) {
        console.log(`  ⚠️ ${gameCode} game check failed (expected during development)`);
      }
    }

  } catch (error) {
    console.error('  ❌ Game launch URL generation failed:', error.message);
    throw error;
  }
}

/**
 * Test 4: ins_mines Game Interaction
 */
async function testInsMinesGameplay(sessionData) {
  console.log('\n🧪 Test 4: ins_mines Game Interaction');
  
  try {
    // Start a round
    const startRoundResponse = await fetch(`${API_BASE}/api/interact/play`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        game_code: 'ins_mines',
        action: 'startRound',
        session_id: sessionData.session_id,
        user_id: sessionData.user_id || 'test_user_001',
        operator_id: 'demo_server',
        currency: 'USD',
        play_mode: 'demo',
        grid_size: 5,
        mine_count: 5,
        bet_amount: 1
      })
    });

    if (!startRoundResponse.ok) {
      const errorData = await startRoundResponse.json();
      throw new Error(`Start round failed: ${startRoundResponse.status} - ${errorData.message}`);
    }

    const roundData = await startRoundResponse.json();
    console.log('  ✅ Round started:', roundData.data.round_id || roundData.data.roundId);
    console.log('  🎯 Max multiplier:', roundData.data.max_multiplier || roundData.data.maxMultiplier);

    // Reveal a tile
    const revealResponse = await fetch(`${API_BASE}/api/interact/play`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        game_code: 'ins_mines',
        action: 'revealTileAction',
        session_id: sessionData.session_id,
        user_id: sessionData.user_id || 'test_user_001',
        operator_id: 'demo_server',
        currency: 'USD',
        play_mode: 'demo',
        round_id: roundData.data.round_id || roundData.data.roundId,
        index: 0,
        bet_amount: 1
      })
    });

    if (!revealResponse.ok) {
      const errorData = await revealResponse.json();
      throw new Error(`Reveal tile failed: ${revealResponse.status} - ${errorData.message}`);
    }

    const revealData = await revealResponse.json();
    console.log('  ✅ Tile revealed:', revealData.data.is_bomb ? 'BOMB' : 'SAFE');
    console.log('  🎮 Game over:', revealData.data.is_bomb || false);

  } catch (error) {
    console.error('  ❌ ins_mines gameplay test failed:', error.message);
    throw error;
  }
}

/**
 * Test 5: Frontend Session Manager Integration
 */
async function testFrontendIntegration() {
  console.log('\n🧪 Test 5: Frontend Session Manager Integration');
  
  try {
    // Test lobby access
    const lobbyResponse = await fetch(FRONTEND_BASE, { method: 'HEAD' });
    if (lobbyResponse.ok) {
      console.log('  ✅ Lobby accessible');
    } else {
      console.log('  ⚠️ Lobby not accessible (may not be running)');
    }

    // Test ins_mines game access
    const minesResponse = await fetch(`${FRONTEND_BASE}/ins_mines.html`, { method: 'HEAD' });
    if (minesResponse.ok) {
      console.log('  ✅ ins_mines game accessible');
    } else {
      console.log('  ⚠️ ins_mines game not accessible (may not be built)');
    }

    console.log('  📋 Frontend integration tests completed');

  } catch (error) {
    console.error('  ❌ Frontend integration test failed:', error.message);
    // Don't throw - frontend may not be running
  }
}

/**
 * Main test execution
 */
async function runAllTests() {
  try {
    console.log('🚀 Starting comprehensive authentication flow tests...\n');

    const sessionData = await testSessionCreationAndAuthentication();
    const availableGames = await testOperatorGamesConfig();
    await testGameLaunchUrls(sessionData, availableGames);
    await testInsMinesGameplay(sessionData);
    await testFrontendIntegration();

    console.log('\n🎉 All authentication flow tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('  ✅ Session creation and authentication working');
    console.log('  ✅ Operator games configuration working');
    console.log('  ✅ Game launch URL generation working');
    console.log('  ✅ ins_mines gameplay working');
    console.log('  ✅ Frontend integration tested');

  } catch (error) {
    console.error('\n💥 Authentication flow tests failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}
